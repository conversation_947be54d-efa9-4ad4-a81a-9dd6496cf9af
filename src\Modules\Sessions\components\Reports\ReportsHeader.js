import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Badge } from 'react-bootstrap';

const ReportsHeader = ({ selectedFilter, handleFilterChange }) => {
  const { t } = useTranslation();
  const filters = [
    { type: 'overview', label: 'Overview' },
    { type: 'session_status', label: 'Session Status' },
    { type: 'attendance_log', label: 'Attendance Log' },
    { type: 'student_details', label: 'Student Details' },
    { type: 'staff_details', label: 'Staff Details' },
    { type: 'activity', label: 'Activity' },
  ];

  return (
    <div className="inner_header">
      <div className="row digi-white-bg digi-set-pad align-items-center">
        <div className="col-lg-12">
          <div className="row">
            <div className={'col-md-12'} style={{ position: 'relative' }}>
              <div
                className={`badge-container py-1`}
                style={{
                  overflowX: 'hidden',
                  whiteSpace: 'nowrap',
                  paddingRight: '40px',
                }}
              >
                {filters.map((filter, index) => (
                  <span className="pb-2" key={index}>
                    <Badge
                      className={`digi-recent-button btn ${
                        selectedFilter === filter.type ? 'active' : ''
                      }`}
                      pill
                      variant=""
                      onClick={() => handleFilterChange(filter.type)}
                    >
                      {t(filter.type)}
                    </Badge>
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

ReportsHeader.propTypes = {
  selectedFilter: PropTypes.object.isRequired,
  handleFilterChange: PropTypes.func.isRequired,
};

export default ReportsHeader;
