<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Face Photo Capture with MediaPipe</title>
    <link
      rel="stylesheet"
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
    />
    <style>
      body {
        background-color: black;
        color: white; /* Optional: Adjust text color for visibility */
      }
      #camera-container {
        position: relative; /* Make the container relative for absolute positioning of children */
        width: 500px;
        height: 375px;
        margin: auto;
      }
      #camera {
        /* width: 100%;
        max-width: 500px;
        margin: auto; */
        transform: scaleX(-1);
      }
      .preview-box {
        width: 100px;
        height: 100px;
        margin: 5px;
        overflow: hidden;
        border: 2px solid #ddd;
      }
      .preview-box img {
        width: 100%;
        /* height: auto; */
        height: 100px;
      }
      .oval-image {
        position: absolute;
        z-index: 1;
      }
      #img img {
        width: 40px;
      }
      #overlay {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none; /* Prevent canvas from blocking interactions with other elements */
      }
    </style>
    <script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="./js/face-api.js"></script>
    <script src="./utils.js"></script>
  </head>
  <body>
    <div class="text-center">
      <div id="camera-container" class="mb-2">
        <img src="./images/oval.png" alt="Oval shape" class="oval-image" />
        <video
          id="camera"
          width="500"
          height="375"
          autoplay
          muted
          playsinline
        ></video>
        <canvas id="overlay"></canvas>
      </div>

      <button
        class="btn btn-primary"
        id="startCaptureBtn"
        onclick="captureImage()"
        style="display: none"
      >
        Capture
      </button>
      <p id="resultMessage" class="mt-1"></p>
      <div id="img" style="display: none"></div>
    </div>

    <script type="module">
      import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';

      let uploadedDescriptors = [];
      let video = document.getElementById('camera');

      const overlay = document.getElementById('overlay');
      let isModelLoaded = false;
      const minConfidence = 0.75;
      let inputSize = 416;
      let scoreThreshold = 0.5;
      let isLiveDetection = false;
      let actionName = '';
      // let blinkDetected = false;
      // let headMovementVerified = false;
      // Initialize the MediaPipe Face Landmarks detector

      const URL = getQueryParam('url') || '';
      const isLive = getQueryParam('isLive') || '';
      function sendMessageToPlatform(messageType, data = null) {
        const message = {
          type: messageType,
          payload: data,
        };
        window.parent.postMessage(message, '*');
      }

      async function initFaceLandmarks() {
        const filesetResolver = await vision.FilesetResolver.forVisionTasks(
          'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
        );
        return await vision.FaceLandmarker.createFromOptions(filesetResolver, {
          baseOptions: {
            modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
          },
          runningMode: 'IMAGE',
          numFaces: 1,
        });
        return faceLandmarker;
      }

      // Define random challenges
      const challenges = [
        {
          action: 'blink both eyes',
          validator: (landmarks) => detectEyeBlink(landmarks),
        },
        {
          action: 'look left',
          validator: (landmarks) => detectEyeLookOutRight(landmarks),
        },
        {
          action: 'look right',
          validator: (landmarks) => detectEyeLookOutLeft(landmarks),
        },
        { action: 'smile', validator: (landmarks) => detectSmile(landmarks) },
        // {
        //   action: 'pucker lips',
        //   validator: (landmarks) => detectMouthPucker(landmarks),
        // },
      ];

      // Start the live detection
      async function startLiveDetection(videoElement) {
        const faceLandmarker = await initFaceLandmarks();

        const canvas1 = document.createElement('canvas');
        canvas1.width = videoElement.videoWidth;
        canvas1.height = videoElement.videoHeight;
        canvas1.style.display = 'none';
        const ctx1 = canvas1.getContext('2d');
        document.body.appendChild(canvas1);

        // Choose a random challenge
        const randomIndex = Math.floor(Math.random() * challenges.length);
        const challenge = challenges[randomIndex];
        console.log(
          `Perform the action: ${challenge.action}`,
          Math.floor(Math.random() * challenges.length)
        );
        // alert(`Please ${challenge.action}`);
        actionName = `Perform the action: ${challenge.action}`;
        // Process each frame

        const interval = setInterval(async () => {
          ctx1.drawImage(videoElement, 0, 0, canvas1.width, canvas1.height);

          // Get landmarks
          const results = await faceLandmarker.detect(canvas1);
          if (results && results.faceLandmarks && results.faceLandmarks[0]) {
            const landmarks = results.faceLandmarks[0];
            console.log({ landmarks: challenge.validator(landmarks) });
            // Validate the challenge
            if (isLive == 'false') {
              isLiveDetection = true;
              actionName = '';
            } else if (challenge.validator(landmarks)) {
              console.log('Challenge completed successfully!');
              isLiveDetection = true;
              clearInterval(interval);
              actionName = 'Live detection successfully';
            }
          } else {
            console.log('No face detected');
          }
        }, 1000);
      }

      // Helper functions for validation

      function detectEyeBlink(landmarks) {
        // EAR calculation for both eyes
        const leftEAR = calculateEAR(landmarks, 'LEFT_EYE');
        const rightEAR = calculateEAR(landmarks, 'RIGHT_EYE');
        return leftEAR < 0.2 && rightEAR < 0.2; // Threshold for eye closure
      }

      function detectEyeLookOutLeft(landmarks) {
        // Check if the left eye is looking out (left)
        const leftEyeCenter = calculateEyeCenter(landmarks, 'LEFT_EYE');
        const noseCenter = calculateNoseCenter(landmarks);
        return leftEyeCenter.x < noseCenter.x; // Left eye looking outwards
      }

      function detectEyeLookOutRight(landmarks) {
        // Check if the right eye is looking out (right)
        const rightEyeCenter = calculateEyeCenter(landmarks, 'RIGHT_EYE');
        const noseCenter = calculateNoseCenter(landmarks);
        return rightEyeCenter.x > noseCenter.x; // Right eye looking outwards
      }

      function detectSmile(landmarks) {
        const leftMouthCorner = landmarks[61]; // Left corner of the mouth
        const rightMouthCorner = landmarks[291]; // Right corner of the mouth

        // Calculate the distance between the corners
        const distance = Math.hypot(
          rightMouthCorner.x - leftMouthCorner.x,
          rightMouthCorner.y - leftMouthCorner.y
        );
        console.log({ distance });
        // Return true if the distance is above the threshold for a smile
        return distance > 0.12; // Adjust threshold as needed
      }

      function detectMouthPucker(landmarks) {
        // Access indices for the upper and lower lips
        const upperLip = landmarks[13]; // Approximate index for the upper lip
        const lowerLip = landmarks[14]; // Approximate index for the lower lip

        // Calculate the distance between the upper and lower lip
        const distance = Math.hypot(
          upperLip.x - lowerLip.x,
          upperLip.y - lowerLip.y
        );
        console.log(distance);
        // Return true if the distance is below the threshold for puckering
        return false;
        // return distance < 0.2; // Adjust threshold as needed
      }

      // Helper functions for calculations
      function calculateEAR(landmarks, eye) {
        const indices =
          eye === 'LEFT_EYE'
            ? [33, 160, 158, 133, 153, 144]
            : [362, 385, 387, 263, 373, 380];
        const vertical1 = Math.hypot(
          landmarks[indices[1]].x - landmarks[indices[5]].x,
          landmarks[indices[1]].y - landmarks[indices[5]].y
        );
        const vertical2 = Math.hypot(
          landmarks[indices[2]].x - landmarks[indices[4]].x,
          landmarks[indices[2]].y - landmarks[indices[4]].y
        );
        const horizontal = Math.hypot(
          landmarks[indices[0]].x - landmarks[indices[3]].x,
          landmarks[indices[0]].y - landmarks[indices[3]].y
        );
        return (vertical1 + vertical2) / (2.0 * horizontal);
      }

      function calculateEyeCenter(landmarks, eye) {
        const indices = eye === 'LEFT_EYE' ? [33, 133] : [362, 263];
        return {
          x: (landmarks[indices[0]].x + landmarks[indices[1]].x) / 2,
          y: (landmarks[indices[0]].y + landmarks[indices[1]].y) / 2,
        };
      }

      function calculateNoseCenter(landmarks) {
        return landmarks[1]; // Approximate nose center using a specific index
      }

      // Start video stream
      async function startVideo() {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        video.srcObject = stream;
      }
      // Capture and process the image
      // let isBlinking = false;
      // let isHeadMovement = false;
      async function capturePosition() {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const context = canvas.getContext('2d');
        context.filter = 'brightness(1.2) contrast(1.2)';
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        document.getElementById(`img`).innerHTML = ``;
        const detections = await faceapi
          .detectSingleFace(
            video,
            // new faceapi.SsdMobilenetv1Options({ minConfidence })
            new faceapi.TinyFaceDetectorOptions({ inputSize, scoreThreshold })
          )
          .withFaceLandmarks()
          .withFaceDescriptor();

        if (
          // isBlinking &&
          // isHeadMovement &&
          isLiveDetection &&
          detections &&
          detections.landmarks &&
          detections.landmarks._positions.length > 0 &&
          detections.detection &&
          detections.detection.score > 0.5
        ) {
          const box = detections.detection.box; // Bounding box of the face
          const landmarks = detections.landmarks; // 68-point landmarks

          let { x: minX, y: minY, width: boxWidth, height: boxHeight } = box;

          // Add padding to the bounding box
          const padding = 0.4; // Adjust padding as needed (e.g., 40%)
          const paddedWidth = boxWidth * (1 + padding);
          const paddedHeight = boxHeight * (1 + padding);

          // Adjust `minX` and `minY` to apply padding
          minX -= (paddedWidth - boxWidth) / 2;
          minY -= (paddedHeight - boxHeight) / 2;

          // Ensure the boundaries stay within the canvas dimensions
          minX = Math.max(0, minX);
          minY = Math.max(0, minY);
          const maxWidth = Math.min(canvas.width - minX, paddedWidth);
          const maxHeight = Math.min(canvas.height - minY, paddedHeight);

          // Create a new canvas for the cropped face
          const croppedCanvas = document.createElement('canvas');
          croppedCanvas.width = maxWidth;
          croppedCanvas.height = maxHeight;

          // Draw the cropped face onto the new canvas
          const croppedCtx = croppedCanvas.getContext('2d');
          croppedCtx.drawImage(
            canvas, // Source canvas
            minX, // Source x
            minY, // Source y
            maxWidth, // Source width
            maxHeight, // Source height
            0, // Destination x
            0, // Destination y
            maxWidth, // Destination width
            maxHeight // Destination height
          );

          const croppedImage = new Image();
          croppedImage.src = croppedCanvas.toDataURL();
          await croppedImage.decode();

          // console.log(croppedImage);

          // Detect face descriptor from the cropped image
          const detectionsFace = await faceapi
            .detectSingleFace(
              video,
              // new faceapi.SsdMobilenetv1Options({ minConfidence })
              new faceapi.TinyFaceDetectorOptions({ inputSize, scoreThreshold })
              // new faceapi.TinyFaceDetectorOptions()
            )
            .withFaceLandmarks()
            .withFaceDescriptor();
          // console.log(detectionsFace);
          if (detectionsFace) {
            // console.log('Face descriptor:', detectionsFace.descriptor);
            const capturedDescriptor = detectionsFace.descriptor;
            const matchFound = compareDescriptors(capturedDescriptor);
            // console.log(matchFound);
            if (matchFound) {
              document.getElementById('resultMessage').textContent =
                'Success! Match found.';
              sendMessageToPlatform('success');
              document.getElementById('startCaptureBtn').style.display =
                'inline-block';
              document.getElementById(
                `img`
              ).innerHTML = `<img src="${croppedImage.src}" alt="Captured Image">`;
            } else {
              // document.getElementById('resultMessage').textContent =
              // 	'No match found. Try again.';
              await countdownAndCapture();
            }
          } else {
            // document.getElementById('resultMessage').textContent =
            // 	'No face detected in cropped image.';
            await countdownAndCapture();
          }
        } else {
          // document.getElementById('resultMessage').textContent =
          // 	'Face not detected, please adjust your position!';
          await countdownAndCapture();
        }
      }
      let secondsPassed = 0;
      async function countdownAndCapture() {
        let countdown = 1;

        const countdownInterval = setInterval(() => {
          secondsPassed++;
          if (secondsPassed == 60) {
            clearInterval(countdownInterval);
            document.getElementById('startCaptureBtn').style.display =
              'inline-block';
            document.getElementById('startCaptureBtn').innerHTML = 'Re Verify';
            secondsPassed = 0;
            document.getElementById(
              'resultMessage'
            ).innerText = `Face not detected, please adjust your position and Re verify`;
            sendMessageToPlatform('failed');
            return;
          }
          if (countdown > 0) {
            document.getElementById(
              'resultMessage'
            ).innerText = `Verifying in progress. Please wait...`;
            countdown--;
          } else {
            clearInterval(countdownInterval);
            capturePosition();
          }
        }, 1000);
      }

      async function captureImage() {
        document.getElementById('startCaptureBtn').style.display = 'none';
        detectFaces();
        startLiveDetection(video);
        // detectFaces();
        await countdownAndCapture();
      }

      function compareDescriptors(capturedDescriptor) {
        let threshold = 0.47; // Define a threshold for matching
        let matchCount = 0;
        // console.log({ uploadedDescriptors, capturedDescriptor });
        uploadedDescriptors.forEach((descriptor) => {
          const distance = faceapi.euclideanDistance(
            capturedDescriptor,
            descriptor
          );
          if (distance < threshold) {
            matchCount++;
          }
        });

        const averageDistance = matchCount / uploadedDescriptors.length;
        console.log({ averageDistance, matchCount });
        // return averageDistance >= 0.5 || matchCount > 0;
        return averageDistance >= 0.75 && matchCount >= 2;
      }

      async function detectFaces() {
        if (!isModelLoaded) return;
        const displaySize = { width: video.width, height: video.height };
        faceapi.matchDimensions(overlay, displaySize);
        let prevNosePosition = null;
        setInterval(async () => {
          const detections = await faceapi
            .detectSingleFace(
              video,
              // new faceapi.SsdMobilenetv1Options({ minConfidence })
              new faceapi.TinyFaceDetectorOptions({ inputSize, scoreThreshold })
            )
            .withFaceLandmarks();

          // Clear previous drawings
          const context = overlay.getContext('2d');
          context.clearRect(0, 0, overlay.width, overlay.height);

          // Resize and draw the detected boxes
          if (detections) {
            const resizedDetections = faceapi.resizeResults(
              detections,
              displaySize
            );
            faceapi.draw.drawDetections(overlay, resizedDetections);

            const landmarks = detections.landmarks;

            // const isBlinkingNow = detectBlink(landmarks);
            // const hasHeadMoved = detectHeadMovement(landmarks);
            // let innerText = !isBlinking
            //   ? 'Blink your eyes'
            //   : 'Move your head left and right';
            // if (isBlinkingNow) {
            //   innerText = 'Blink Detected - Live Face';
            //   isBlinking = true;
            // } else if (hasHeadMoved && isBlinking) {
            //   innerText = 'Head Movement Detected - Live Face';
            //   isHeadMovement = true;
            // }

            context.font = '18px Arial';
            let brightnessHtml = '';

            if (detections.detection.score < 0.5) {
              brightnessHtml =
                'Please adjust lighting or move closer to the camera.';
            }

            const lines =
              `${brightnessHtml}\n                      ${actionName}`.split(
                '\n'
              ); // Split text into lines
            const x = resizedDetections.detection.box.x - 110;
            let y = resizedDetections.detection.box.y - 60;

            lines.forEach((line, i) => {
              context.fillText(line, x, y);
              context.fillStyle = 'red';
              y += 35; // Adjust this value based on your desired line spacing
            });
          }
        }, 1000); // Detect every 100ms
      }
      // video.addEventListener('play', detectFaces);
      // Initialize everything when the page loads
      window.onload = async () => {
        await startVideo();
      };
      window.captureImage = captureImage;

      // const daAuthAppUrl =
      //   'https://auth-staging.gcp.digivalitsolutions.com/api/v0/auth/facial-labeled-descriptors-v2?employeeOrAcademicId=';

      function loadSingleDescfromDb(url) {
        console.log('load from db called');
        // const descriptors = localStorage.getItem('descriptors');
        // if (descriptors !== null) {
        // 	const decodeDescriptors = JSON.parse(descriptors);
        // 	console.log({ decodeDescriptors });
        // 	uploadedDescriptors = decodeDescriptors;
        // }
        // return;
        $.ajax({
          url: url,
          type: 'GET',
          beforeSend: function (request) {
            request.setRequestHeader('api_key', 'apikey123');
          },
          dataType: 'json', // added data type
          success: function (res) {
            uploadedDescriptors = res.data.flatMap((item) => item.descriptors);
            console.log({ uploadedDescriptors });
          },
          error: function (err) {
            console.log('error occured load from db', err);
          },
        });
      }

      // Liveness Detection Variables

      let initialEyeDistance = null;

      // Liveness Detection: Check for Blinks
      function detectBlink(landmarks) {
        const leftEye = landmarks.getLeftEye();
        const rightEye = landmarks.getRightEye();

        const leftEAR = calculateEAR(leftEye); // Eye Aspect Ratio
        const rightEAR = calculateEAR(rightEye);

        const EAR_THRESHOLD = 0.26; // Adjust threshold for blink sensitivity
        if (leftEAR < EAR_THRESHOLD && rightEAR < EAR_THRESHOLD) {
          return true; // Blinking detected
        }
        return false;
      }

      // Helper: Calculate Eye Aspect Ratio (EAR)
      // function calculateEAR(eye) {
      //   const vertical1 = Math.hypot(eye[1].x - eye[5].x, eye[1].y - eye[5].y);
      //   const vertical2 = Math.hypot(eye[2].x - eye[4].x, eye[2].y - eye[4].y);
      //   const horizontal = Math.hypot(eye[0].x - eye[3].x, eye[0].y - eye[3].y);
      //   return (vertical1 + vertical2) / (2.0 * horizontal);
      // }

      // Monitor Head Movement
      function detectHeadMovement(landmarks) {
        const leftEye = landmarks.getLeftEye();
        const rightEye = landmarks.getRightEye();

        const eyeDistance = Math.abs(leftEye[0].x - rightEye[3].x);
        if (!initialEyeDistance) {
          initialEyeDistance = eyeDistance;
          return false;
        }

        const movementThreshold = 10; // Threshold for head movement detection
        return Math.abs(eyeDistance - initialEyeDistance) > movementThreshold;
      }

      async function loadLibraries() {
        const MODEL_URL = 'models';
        document.getElementById(
          'resultMessage'
        ).innerText = `Loading Face Models. Please wait...`;
        // await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
        await faceapi.loadFaceLandmarkModel(MODEL_URL);
        await faceapi.loadFaceRecognitionModel(MODEL_URL);
        await faceapi.loadTinyFaceDetectorModel(MODEL_URL);

        console.log('load facial Libraries done');
        document.getElementById('resultMessage').innerText = ``;
      }

      $(document).ready(async function () {
        console.log('webcam is ready');
        await loadLibraries();
        loadSingleDescfromDb(URL);
        isModelLoaded = true;
        document.getElementById('startCaptureBtn').style.display =
          'inline-block';
      });
    </script>
  </body>
</html>
