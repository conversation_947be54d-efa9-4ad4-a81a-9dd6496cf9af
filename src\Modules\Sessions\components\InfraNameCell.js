import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { getEnvLabelChanged, indVerRename } from '../../../utils';
import { getInfraName } from './utils';
import { CapitalizeAll } from '../../../ReduxApi/util';

const InfraNameCell = ({ schedule, programId }) => {
  const { t } = useTranslation();
  return (
    <div className="f-14">
      {getEnvLabelChanged()
        ? CapitalizeAll(indVerRename(getInfraName(schedule), programId))
        : t(CapitalizeAll(getInfraName(schedule)))}
    </div>
  );
};

InfraNameCell.propTypes = {
  schedule: PropTypes.object.isRequired,
  programId: PropTypes.string.isRequired,
};

export default InfraNameCell;
