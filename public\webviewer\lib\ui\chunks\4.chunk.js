(window.webpackJsonp=window.webpackJsonp||[]).push([[4],{1200:function(e,t,n){var o,r;self,e.exports=(o=n(0),r=n(22),(()=>{var e={926:e=>{"use strict";e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path class="cls-1" d="M18,6H17V4a2,2,0,0,0-2-2H9A2,2,0,0,0,7,4V6H3V8H5V20a2,2,0,0,0,2,2H17a2,2,0,0,0,2-2V8h2V6ZM9,4h6V6H9ZM7,20V8H17V20Zm6-10h2v8H13ZM9,10h2v8H9Z"/></svg>'},316:e=>{"use strict";e.exports='<svg\n  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">\n  <path class="cls-1" d="M9,14.5A1.5,1.5,0,1,1,7.5,13,1.5,1.5,0,0,1,9,14.5ZM10.5,6A1.5,1.5,0,1,0,12,7.5,1.5,1.5,0,0,0,10.5,6Zm-3,3A1.5,1.5,0,1,0,9,10.5,1.5,1.5,0,0,0,7.5,9Zm7.2,6.36a2,2,0,0,0-.09,1.92l.2.41A3,3,0,0,1,12.14,22H12a9.74,9.74,0,0,1-2.62-.36A10,10,0,0,1,4.46,5.43,10,10,0,0,1,22,12.14a3,3,0,0,1-3,3,3.09,3.09,0,0,1-1.3-.3l-.41-.2A2,2,0,0,0,14.7,15.36Zm3.44-2.55.42.19A1,1,0,0,0,20,12.11a8,8,0,0,0-6.87-8A7.24,7.24,0,0,0,12,4,8,8,0,0,0,6,6.74a7.92,7.92,0,0,0-1.89,6.39A8.05,8.05,0,0,0,9.9,19.72,8.42,8.42,0,0,0,12,20h.11a1,1,0,0,0,.84-.48,1,1,0,0,0,.05-1l-.2-.42A3.92,3.92,0,0,1,13,14.3,4.05,4.05,0,0,1,18.14,12.81ZM14.5,6A1.5,1.5,0,1,0,16,7.5,1.5,1.5,0,0,0,14.5,6Z"/>\n</svg>\n'},937:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(645),r=n.n(o)()((function(e){return e[1]}));r.push([e.id,"div#wv-read-mode {\n   height: 100%;\n   width: inherit;\n}\n\n.reader-mode-spinner-wrapper {\n   position: fixed;\n   left: 0;\n   width: inherit;\n   padding: inherit;\n   background-color: white;\n   background-clip: content-box;\n   display: flex;\n   justify-content: center;\n   align-items: center;\n}\n\n.reader-mode-spinner-wrapper.hidden {\n   display: none;\n}\n\n.reader-mode-spinner {\n   position: absolute;\n   height: 60px;\n   width: 60px;\n   margin: 0px auto;\n   -webkit-animation: rotation .6s infinite linear;\n   -moz-animation: rotation .6s infinite linear;\n   -o-animation: rotation .6s infinite linear;\n   animation: rotation .6s infinite linear;\n   border-left: 6px solid rgba(0, 174, 239, .15);\n   border-right: 6px solid rgba(0, 174, 239, .15);\n   border-bottom: 6px solid rgba(0, 174, 239, .15);\n   border-top: 6px solid rgba(0, 174, 239, .8);\n   border-radius: 100%;\n}\n\n@-webkit-keyframes rotation {\n   from {\n      -webkit-transform: rotate(0deg);\n   }\n\n   to {\n      -webkit-transform: rotate(359deg);\n   }\n}\n\n@-moz-keyframes rotation {\n   from {\n      -moz-transform: rotate(0deg);\n   }\n\n   to {\n      -moz-transform: rotate(359deg);\n   }\n}\n\n@-o-keyframes rotation {\n   from {\n      -o-transform: rotate(0deg);\n   }\n\n   to {\n      -o-transform: rotate(359deg);\n   }\n}\n\n@keyframes rotation {\n   from {\n      transform: rotate(0deg);\n   }\n\n   to {\n      transform: rotate(359deg);\n   }\n}\n",""]);const i=r},645:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,o){"string"==typeof e&&(e=[[null,e,""]]);var r={};if(o)for(var i=0;i<this.length;i++){var l=this[i][0];null!=l&&(r[l]=!0)}for(var a=0;a<e.length;a++){var s=[].concat(e[a]);o&&r[s[0]]||(n&&(s[2]?s[2]="".concat(n," and ").concat(s[2]):s[2]=n),t.push(s))}},t}},703:(e,t,n)=>{"use strict";var o=n(414);function r(){}function i(){}i.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,i,l){if(l!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return n.PropTypes=n,n}},697:(e,t,n)=>{e.exports=n(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},379:(e,t,n)=>{"use strict";var o,r=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),i=[];function l(e){for(var t=-1,n=0;n<i.length;n++)if(i[n].identifier===e){t=n;break}return t}function a(e,t){for(var n={},o=[],r=0;r<e.length;r++){var a=e[r],s=t.base?a[0]+t.base:a[0],c=n[s]||0,u="".concat(s," ").concat(c);n[s]=c+1;var d=l(u),f={css:a[1],media:a[2],sourceMap:a[3]};-1!==d?(i[d].references++,i[d].updater(f)):i.push({identifier:u,updater:m(f,t),references:1}),o.push(u)}return o}function s(e){var t=document.createElement("style"),o=e.attributes||{};if(void 0===o.nonce){var i=n.nc;i&&(o.nonce=i)}if(Object.keys(o).forEach((function(e){t.setAttribute(e,o[e])})),"function"==typeof e.insert)e.insert(t);else{var l=r(e.insert||"head");if(!l)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");l.appendChild(t)}return t}var c,u=(c=[],function(e,t){return c[e]=t,c.filter(Boolean).join("\n")});function d(e,t,n,o){var r=n?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=u(t,r);else{var i=document.createTextNode(r),l=e.childNodes;l[t]&&e.removeChild(l[t]),l.length?e.insertBefore(i,l[t]):e.appendChild(i)}}function f(e,t,n){var o=n.css,r=n.media,i=n.sourceMap;if(r?e.setAttribute("media",r):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var h=null,p=0;function m(e,t){var n,o,r;if(t.singleton){var i=p++;n=h||(h=s(t)),o=d.bind(null,n,i,!1),r=d.bind(null,n,i,!0)}else n=s(t),o=f.bind(null,n,t),r=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else r()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=(void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o));var n=a(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<n.length;o++){var r=l(n[o]);i[r].references--}for(var s=a(e,t),c=0;c<n.length;c++){var u=l(n[c]);0===i[u].references&&(i[u].updater(),i.splice(u,1))}n=s}}}},662:e=>{"use strict";e.exports='.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}'},421:e=>{"use strict";e.exports='.tippy-box[data-theme~=light-border]{background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,8,16,.15);color:#333;box-shadow:0 4px 14px -2px rgba(0,8,16,.08)}.tippy-box[data-theme~=light-border]>.tippy-backdrop{background-color:#fff}.tippy-box[data-theme~=light-border]>.tippy-arrow:after,.tippy-box[data-theme~=light-border]>.tippy-svg-arrow:after{content:"";position:absolute;z-index:-1}.tippy-box[data-theme~=light-border]>.tippy-arrow:after{border-color:transparent;border-style:solid}.tippy-box[data-theme~=light-border][data-placement^=top]>.tippy-arrow:before{border-top-color:#fff}.tippy-box[data-theme~=light-border][data-placement^=top]>.tippy-arrow:after{border-top-color:rgba(0,8,16,.2);border-width:7px 7px 0;top:17px;left:1px}.tippy-box[data-theme~=light-border][data-placement^=top]>.tippy-svg-arrow>svg{top:16px}.tippy-box[data-theme~=light-border][data-placement^=top]>.tippy-svg-arrow:after{top:17px}.tippy-box[data-theme~=light-border][data-placement^=bottom]>.tippy-arrow:before{border-bottom-color:#fff;bottom:16px}.tippy-box[data-theme~=light-border][data-placement^=bottom]>.tippy-arrow:after{border-bottom-color:rgba(0,8,16,.2);border-width:0 7px 7px;bottom:17px;left:1px}.tippy-box[data-theme~=light-border][data-placement^=bottom]>.tippy-svg-arrow>svg{bottom:16px}.tippy-box[data-theme~=light-border][data-placement^=bottom]>.tippy-svg-arrow:after{bottom:17px}.tippy-box[data-theme~=light-border][data-placement^=left]>.tippy-arrow:before{border-left-color:#fff}.tippy-box[data-theme~=light-border][data-placement^=left]>.tippy-arrow:after{border-left-color:rgba(0,8,16,.2);border-width:7px 0 7px 7px;left:17px;top:1px}.tippy-box[data-theme~=light-border][data-placement^=left]>.tippy-svg-arrow>svg{left:11px}.tippy-box[data-theme~=light-border][data-placement^=left]>.tippy-svg-arrow:after{left:12px}.tippy-box[data-theme~=light-border][data-placement^=right]>.tippy-arrow:before{border-right-color:#fff;right:16px}.tippy-box[data-theme~=light-border][data-placement^=right]>.tippy-arrow:after{border-width:7px 7px 7px 0;right:17px;top:1px;border-right-color:rgba(0,8,16,.2)}.tippy-box[data-theme~=light-border][data-placement^=right]>.tippy-svg-arrow>svg{right:11px}.tippy-box[data-theme~=light-border][data-placement^=right]>.tippy-svg-arrow:after{right:12px}.tippy-box[data-theme~=light-border]>.tippy-svg-arrow{fill:#fff}.tippy-box[data-theme~=light-border]>.tippy-svg-arrow:after{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCA2czEuNzk2LS4wMTMgNC42Ny0zLjYxNUM1Ljg1MS45IDYuOTMuMDA2IDggMGMxLjA3LS4wMDYgMi4xNDguODg3IDMuMzQzIDIuMzg1QzE0LjIzMyA2LjAwNSAxNiA2IDE2IDZIMHoiIGZpbGw9InJnYmEoMCwgOCwgMTYsIDAuMikiLz48L3N2Zz4=);background-size:16px 6px;width:16px;height:6px}'},773:e=>{"use strict";e.exports="body {\n  display: flex;\n  flex-direction: column;\n}\n\nhtml {\n  overflow: hidden;\n}\n"},370:e=>{"use strict";e.exports=".rm-tooltip-content {\n  margin: -5px -9px;\n  display: flex;\n  font-family: sans-serif;\n}\n\n.rm-tooltip-icon {\n  height: 32px;\n  width: 32px;\n  margin: 4px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  border-radius: 4px;\n}\n\n.rm-tooltip-icon:hover {\n  background-color: #E7EDF3;\n}\n\n.rm-tooltip-icon svg {\n  height: 18px;\n  width: 18px;\n  fill: #868e96;\n  pointer-events: none;\n}\n"},798:e=>{"use strict";e.exports=o},413:e=>{"use strict";e.exports=r}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={id:o,exports:{}};return e[o](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return(()=>{"use strict";n.r(i),n.d(i,{default:()=>ol});var e=n(798),t=n.n(e),o=n(413),r=n.n(o);function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function a(e,t){if(null==e)return{};var n,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var c=n(697),u=n.n(c),d=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,o){return e[0]===t&&(n=o,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),o=this.__entries__[n];return o&&o[1]},t.prototype.set=function(t,n){var o=e(this.__entries__,t);~o?this.__entries__[o][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,o=e(n,t);~o&&n.splice(o,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,o=this.__entries__;n<o.length;n++){var r=o[n];e.call(t,r[1],r[0])}},t}()}(),f="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,h=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),p="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(h):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},m=["top","right","bottom","left","width","height","size","weight"],g="undefined"!=typeof MutationObserver,v=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,o=!1,r=0;function i(){n&&(n=!1,e()),o&&a()}function l(){p(i)}function a(){var e=Date.now();if(n){if(e-r<2)return;o=!0}else n=!0,o=!1,setTimeout(l,20);r=e}return a}(this.refresh.bind(this))}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){f&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),g?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){f&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;m.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),y=function(e,t){for(var n=0,o=Object.keys(t);n<o.length;n++){var r=o[n];Object.defineProperty(e,r,{value:t[r],enumerable:!1,writable:!1,configurable:!0})}return e},b=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||h},w=R(0,0,0,0);function S(e){return parseFloat(e)||0}function x(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+S(e["border-"+n+"-width"])}),0)}var C="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof b(e).SVGGraphicsElement}:function(e){return e instanceof b(e).SVGElement&&"function"==typeof e.getBBox};function R(e,t,n,o){return{x:e,y:t,width:n,height:o}}var O=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=R(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=function(e){return f?C(e)?function(e){var t=e.getBBox();return R(0,0,t.width,t.height)}(e):function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return w;var o=b(e).getComputedStyle(e),r=function(e){for(var t={},n=0,o=["top","right","bottom","left"];n<o.length;n++){var r=o[n],i=e["padding-"+r];t[r]=S(i)}return t}(o),i=r.left+r.right,l=r.top+r.bottom,a=S(o.width),s=S(o.height);if("border-box"===o.boxSizing&&(Math.round(a+i)!==t&&(a-=x(o,"left","right")+i),Math.round(s+l)!==n&&(s-=x(o,"top","bottom")+l)),!function(e){return e===b(e).document.documentElement}(e)){var c=Math.round(a+i)-t,u=Math.round(s+l)-n;1!==Math.abs(c)&&(a-=c),1!==Math.abs(u)&&(s-=u)}return R(r.left,r.top,a,s)}(e):w}(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),T=function(e,t){var n,o,r,i,l,a,s,c=(o=(n=t).x,r=n.y,i=n.width,l=n.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,s=Object.create(a.prototype),y(s,{x:o,y:r,width:i,height:l,top:r,right:o+i,bottom:l+r,left:o}),s);y(this,{target:e,contentRect:c})},z=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new d,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof b(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new O(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof b(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new T(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),k="undefined"!=typeof WeakMap?new WeakMap:new d,P=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=v.getInstance(),o=new z(t,n,this);k.set(this,o)};["observe","unobserve","disconnect"].forEach((function(e){P.prototype[e]=function(){var t;return(t=k.get(this))[e].apply(t,arguments)}}));const I=void 0!==h.ResizeObserver?h.ResizeObserver:P;var M=["client","offset","scroll","bounds","margin"];function E(e){var t=[];return M.forEach((function(n){e[n]&&t.push(n)})),t}function A(e,t){var n={};if(t.indexOf("client")>-1&&(n.client={top:e.clientTop,left:e.clientLeft,width:e.clientWidth,height:e.clientHeight}),t.indexOf("offset")>-1&&(n.offset={top:e.offsetTop,left:e.offsetLeft,width:e.offsetWidth,height:e.offsetHeight}),t.indexOf("scroll")>-1&&(n.scroll={top:e.scrollTop,left:e.scrollLeft,width:e.scrollWidth,height:e.scrollHeight}),t.indexOf("bounds")>-1){var o=e.getBoundingClientRect();n.bounds={top:o.top,right:o.right,bottom:o.bottom,left:o.left,width:o.width,height:o.height}}if(t.indexOf("margin")>-1){var r=getComputedStyle(e);n.margin={top:r?parseInt(r.marginTop):0,right:r?parseInt(r.marginRight):0,bottom:r?parseInt(r.marginBottom):0,left:r?parseInt(r.marginLeft):0}}return n}function L(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||window}var j=function(t){var n,o;return o=n=function(n){var o,r;function i(){for(var e,t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return(e=n.call.apply(n,[this].concat(o))||this).state={contentRect:{entry:{},client:{},offset:{},scroll:{},bounds:{},margin:{}}},e._animationFrameID=null,e._resizeObserver=null,e._node=null,e._window=null,e.measure=function(t){var n=A(e._node,E(e.props));t&&(n.entry=t[0].contentRect),e._animationFrameID=e._window.requestAnimationFrame((function(){null!==e._resizeObserver&&(e.setState({contentRect:n}),"function"==typeof e.props.onResize&&e.props.onResize(n))}))},e._handleRef=function(t){null!==e._resizeObserver&&null!==e._node&&e._resizeObserver.unobserve(e._node),e._node=t,e._window=L(e._node);var n=e.props.innerRef;n&&("function"==typeof n?n(e._node):n.current=e._node),null!==e._resizeObserver&&null!==e._node&&e._resizeObserver.observe(e._node)},e}r=n,(o=i).prototype=Object.create(r.prototype),o.prototype.constructor=o,s(o,r);var c=i.prototype;return c.componentDidMount=function(){this._resizeObserver=null!==this._window&&this._window.ResizeObserver?new this._window.ResizeObserver(this.measure):new I(this.measure),null!==this._node&&(this._resizeObserver.observe(this._node),"function"==typeof this.props.onResize&&this.props.onResize(A(this._node,E(this.props))))},c.componentWillUnmount=function(){null!==this._window&&this._window.cancelAnimationFrame(this._animationFrameID),null!==this._resizeObserver&&(this._resizeObserver.disconnect(),this._resizeObserver=null)},c.render=function(){var n=this.props,o=(n.innerRef,n.onResize,a(n,["innerRef","onResize"]));return(0,e.createElement)(t,l({},o,{measureRef:this._handleRef,measure:this.measure,contentRect:this.state.contentRect}))},i}(e.Component),n.propTypes={client:u().bool,offset:u().bool,scroll:u().bool,bounds:u().bool,margin:u().bool,innerRef:u().oneOfType([u().object,u().func]),onResize:u().func},o}((function(e){var t=e.measure,n=e.measureRef,o=e.contentRect;return(0,e.children)({measure:t,measureRef:n,contentRect:o})}));j.displayName="Measure",j.propTypes.children=u().func;const H=j;var D="wv-read-mode",N="resize",W="pageNumberUpdated",G="zoomUpdated",F="addAnnotConfigUpdated",U=n(379),B=n.n(U),V=n(937);function q(e){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function Y(e,t,n,o,r,i,l){try{var a=e[i](l),s=a.value}catch(e){return void n(e)}a.done?t(s):Promise.resolve(s).then(o,r)}function X(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function l(e){Y(i,o,r,l,a,"next",e)}function a(e){Y(i,o,r,l,a,"throw",e)}l(void 0)}))}}function J(e,t){return(J=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function K(e,t){return!t||"object"!==q(t)&&"function"!=typeof t?$(e):t}function $(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Q(e){return(Q=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}B()(V.Z,{insert:"head",singleton:!1}),V.Z.locals;var te=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&J(e,t)}(c,e);var t,n,o,r,i,l,a,s=(l=c,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Q(l);if(a){var n=Q(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return K(this,e)});function c(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),ee($(t=s.call(this,e)),"handleLinkClicked",(function(e){if(e.match(/^\d+\-0\.html$/)){var n=Number(e.split("-")[0]);if(t.pageObjNumMap.has(n)){var o=t.pageObjNumMap.get(n);t.jumpToPage(o)}else{var r=function(){var e=X(regeneratorRuntime.mark((function e(){var o,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.setState({showSpinner:!0}),o=0;case 2:if(!(o<t.state.pages.length)){e.next=16;break}if(!t.state.pages[o].loaded){e.next=5;break}return e.abrupt("continue",13);case 5:return e.next=7,t.getPageContent(o,!0);case 7:if(r=e.sent,i=r.objNum,n!==i){e.next=13;break}return t.jumpToPage(o),t.setState({showSpinner:!1}),e.abrupt("break",16);case 13:o++,e.next=2;break;case 16:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();t.runPdfNetTask(r)}}else{var i;null===(i=window)||void 0===i||i.open(e)}})),ee($(t),"getViewerElement",(function(){return t.viewerElement})),ee($(t),"handleAddAnnotConfigUpdated",(function(e){})),t.state={pages:[],zoom:"1",showSpinner:!0,spinnerStyle:{},addAnnotConfig:void 0},t.initialized=!1,t.doc=void 0,t.pageObjNumMap=new Map,t.preloadPagesNum=1,t}return t=c,(n=[{key:"componentDidMount",value:function(){this.viewerElement=document.getElementById(D),this.resizeSpinner(),this.props.viewport.addEventListener(W,this.handlePageNumberUpdated),this.props.viewport.addEventListener(G,this.handleZoomUpdated),this.props.viewport.addEventListener(F,this.handleAddAnnotConfigUpdated),this.initialize()}},{key:"componentWillUnmount",value:function(){this.props.viewport.removeEventListener(W,this.handlePageNumberUpdated),this.props.viewport.removeEventListener(G,this.handleZoomUpdated),this.props.viewport.removeEventListener(F,this.handleAddAnnotConfigUpdated)}},{key:"runPdfNetTask",value:function(e){var t=this;this.props.pdfNet.initialize(void 0,"ems").then((function(){var n=function(){var t=X(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e();case 3:t.next=8;break;case 5:t.prev=5,t.t0=t.catch(0),console.log(t.t0);case 8:case"end":return t.stop()}}),t,null,[[0,5]])})));return function(){return t.apply(this,arguments)}}();t.props.pdfNet.runWithoutCleanup(n)}))}},{key:"initialize",value:function(){var e=this,t=function(){var t=X(regeneratorRuntime.mark((function t(){var n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.props.doc;case 2:return e.doc=t.sent,t.next=5,e.doc.initSecurityHandler();case 5:return t.next=7,e.doc.getPageCount();case 7:if(n=t.sent,e.props.options.pageCountHandler(n),0!==n){t.next=13;break}e.setState((function(e){return{pages:e.pages.concat({content:"There is no text content in this file.",loaded:!0})}})),t.next=15;break;case 13:return t.next=15,e.initializePages(n);case 15:e.initialized=!0,e.props.options.pageNum>1&&e.props.options.pageNum<=n&&e.jumpToPage(e.props.options.pageNum-1),e.setState({showSpinner:!1});case 18:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();this.runPdfNetTask(t)}},{key:"initializePages",value:(i=X(regeneratorRuntime.mark((function e(t){var n,o,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=regeneratorRuntime.mark((function e(n){var o,i,l;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o="",i=void 0,!(n<r.preloadPagesNum)){e.next=7;break}return e.next=4,r.getPageContent(n,!1,t);case 4:l=e.sent,o=l.htmlStr,i=l.pdfNetReflow;case 7:r.setState((function(e){return{pages:e.pages.concat({content:o,loaded:n<r.preloadPagesNum,pdfNetReflow:i})}}));case 8:case"end":return e.stop()}}),e)})),o=0;case 2:if(!(o<t)){e.next=7;break}return e.delegateYield(n(o),"t0",4);case 4:o++,e.next=2;break;case 7:case"end":return e.stop()}}),e)}))),function(e){return i.apply(this,arguments)})},{key:"getPageContent",value:(r=X(regeneratorRuntime.mark((function e(t){var n,o,r,i,l,a=arguments;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=a.length>1&&void 0!==a[1]&&a[1],a.length>2&&a[2],e.next=5,this.doc.getPage(t+1);case 5:return o=e.sent,e.next=8,o.getSDFObj();case 8:return e.next=10,e.sent.getObjNum();case 10:if(r=e.sent,this.pageObjNumMap.set(r,t),!this.isReflowSupported()){e.next=26;break}return e.next=15,this.props.pdfNet.Convert.createReflow(o,"");case 15:if(l=e.sent,!n){e.next=20;break}e.t0="",e.next=23;break;case 20:return e.next=22,l.getHtml();case 22:e.t0=e.sent;case 23:i=e.t0,e.next=34;break;case 26:if(!n){e.next=30;break}e.t1="",e.next=33;break;case 30:return e.next=32,this.props.pdfNet.Convert.pageToHtml(o);case 32:e.t1=e.sent;case 33:i=e.t1;case 34:return e.abrupt("return",{htmlStr:i,objNum:r,pdfNetReflow:l});case 35:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"loadPageByNum",value:(o=X(regeneratorRuntime.mark((function e(t){var n,o,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getPageContent(t);case 2:n=e.sent,o=n.htmlStr,r=n.pdfNetReflow,this.setState((function(e){var n,i=function(e){if(Array.isArray(e))return Z(e)}(n=e.pages)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||function(e,t){if(e){if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(e,t):void 0}}(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();return i[t]={content:o,loaded:!0,pdfNetReflow:r},{pages:i}}));case 6:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"resize",value:function(){this.resizeSpinner()}},{key:"handleZoomUpdated",value:function(e){var t=this;this.setState({zoom:e.detail.toString()},(function(){t.resize()}))}},{key:"resizeSpinner",value:function(){this.setState({spinnerStyle:{zIndex:10,height:this.viewerElement.clientHeight+"px"}})}},{key:"jumpToPage",value:function(e){}},{key:"handlePageNumberUpdated",value:function(e){}},{key:"isReflowSupported",value:function(){return!1}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(t.prototype,n),c}(t().PureComponent);function ne(e){return"read-mode-page-".concat(e)}function oe(){return navigator.userAgent.indexOf("Safari")>-1}function re(e){var t;return null==e||null===(t=e.document)||void 0===t?void 0:t.getElementsByTagName("html")[0]}function ie(e){var t;return null===(t=re(e))||void 0===t?void 0:t.getElementsByTagName("body")[0]}function le(e){var t;return null===(t=re(e))||void 0===t?void 0:t.getElementsByTagName("head")[0]}var ae=n(926),se=n(316);function ce(e){var t=e.getBoundingClientRect();return{width:t.width,height:t.height,top:t.top,right:t.right,bottom:t.bottom,left:t.left,x:t.left,y:t.top}}function ue(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function de(e){var t=ue(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function fe(e){return e instanceof ue(e).Element||e instanceof Element}function he(e){return e instanceof ue(e).HTMLElement||e instanceof HTMLElement}function pe(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ue(e).ShadowRoot||e instanceof ShadowRoot)}function me(e){return e?(e.nodeName||"").toLowerCase():null}function ge(e){return((fe(e)?e.ownerDocument:e.document)||window.document).documentElement}function ve(e){return ce(ge(e)).left+de(e).scrollLeft}function ye(e){return ue(e).getComputedStyle(e)}function be(e){var t=ye(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function _e(e,t,n){void 0===n&&(n=!1);var o,r,i=ge(t),l=ce(e),a=he(t),s={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(a||!a&&!n)&&(("body"!==me(t)||be(i))&&(s=(o=t)!==ue(o)&&he(o)?{scrollLeft:(r=o).scrollLeft,scrollTop:r.scrollTop}:de(o)),he(t)?((c=ce(t)).x+=t.clientLeft,c.y+=t.clientTop):i&&(c.x=ve(i))),{x:l.left+s.scrollLeft-c.x,y:l.top+s.scrollTop-c.y,width:l.width,height:l.height}}function we(e){var t=ce(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Se(e){return"html"===me(e)?e:e.assignedSlot||e.parentNode||(pe(e)?e.host:null)||ge(e)}function xe(e,t){var n;void 0===t&&(t=[]);var o=function e(t){return["html","body","#document"].indexOf(me(t))>=0?t.ownerDocument.body:he(t)&&be(t)?t:e(Se(t))}(e),r=o===(null==(n=e.ownerDocument)?void 0:n.body),i=ue(o),l=r?[i].concat(i.visualViewport||[],be(o)?o:[]):o,a=t.concat(l);return r?a:a.concat(xe(Se(l)))}function Ce(e){return["table","td","th"].indexOf(me(e))>=0}function Re(e){return he(e)&&"fixed"!==ye(e).position?e.offsetParent:null}function Oe(e){for(var t=ue(e),n=Re(e);n&&Ce(n)&&"static"===ye(n).position;)n=Re(n);return n&&("html"===me(n)||"body"===me(n)&&"static"===ye(n).position)?t:n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&he(e)&&"fixed"===ye(e).position)return null;for(var n=Se(e);he(n)&&["html","body"].indexOf(me(n))<0;){var o=ye(n);if("none"!==o.transform||"none"!==o.perspective||"paint"===o.contain||-1!==["transform","perspective"].indexOf(o.willChange)||t&&"filter"===o.willChange||t&&o.filter&&"none"!==o.filter)return n;n=n.parentNode}return null}(e)||t}var Te="top",ze="bottom",ke="right",Pe="left",Ie="auto",Me=[Te,ze,ke,Pe],Ee="start",Ae="viewport",Le="popper",je=Me.reduce((function(e,t){return e.concat([t+"-"+Ee,t+"-end"])}),[]),He=[].concat(Me,[Ie]).reduce((function(e,t){return e.concat([t,t+"-"+Ee,t+"-end"])}),[]),De=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Ne(e){var t=new Map,n=new Set,o=[];return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||function e(r){n.add(r.name),[].concat(r.requires||[],r.requiresIfExists||[]).forEach((function(o){if(!n.has(o)){var r=t.get(o);r&&e(r)}})),o.push(r)}(e)})),o}var We={placement:"bottom",modifiers:[],strategy:"absolute"};function Ge(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}var Fe={passive:!0};const Ue={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,r=o.scroll,i=void 0===r||r,l=o.resize,a=void 0===l||l,s=ue(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach((function(e){e.addEventListener("scroll",n.update,Fe)})),a&&s.addEventListener("resize",n.update,Fe),function(){i&&c.forEach((function(e){e.removeEventListener("scroll",n.update,Fe)})),a&&s.removeEventListener("resize",n.update,Fe)}},data:{}};function Be(e){return e.split("-")[0]}function Ve(e){return e.split("-")[1]}function qe(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ze(e){var t,n=e.reference,o=e.element,r=e.placement,i=r?Be(r):null,l=r?Ve(r):null,a=n.x+n.width/2-o.width/2,s=n.y+n.height/2-o.height/2;switch(i){case Te:t={x:a,y:n.y-o.height};break;case ze:t={x:a,y:n.y+n.height};break;case ke:t={x:n.x+n.width,y:s};break;case Pe:t={x:n.x-o.width,y:s};break;default:t={x:n.x,y:n.y}}var c=i?qe(i):null;if(null!=c){var u="y"===c?"height":"width";switch(l){case Ee:t[c]=t[c]-(n[u]/2-o[u]/2);break;case"end":t[c]=t[c]+(n[u]/2-o[u]/2)}}return t}const Ye={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Ze({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};var Xe=Math.max,Je=Math.min,Ke=Math.round,$e={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Qe(e){var t,n=e.popper,o=e.popperRect,r=e.placement,i=e.offsets,l=e.position,a=e.gpuAcceleration,s=e.adaptive,c=e.roundOffsets,u=!0===c?function(e){var t=e.x,n=e.y,o=window.devicePixelRatio||1;return{x:Ke(Ke(t*o)/o)||0,y:Ke(Ke(n*o)/o)||0}}(i):"function"==typeof c?c(i):i,d=u.x,f=void 0===d?0:d,h=u.y,p=void 0===h?0:h,m=i.hasOwnProperty("x"),g=i.hasOwnProperty("y"),v=Pe,y=Te,b=window;if(s){var _=Oe(n),w="clientHeight",S="clientWidth";_===ue(n)&&"static"!==ye(_=ge(n)).position&&(w="scrollHeight",S="scrollWidth"),_=_,r===Te&&(y=ze,p-=_[w]-o.height,p*=a?1:-1),r===Pe&&(v=ke,f-=_[S]-o.width,f*=a?1:-1)}var x,C=Object.assign({position:l},s&&$e);return a?Object.assign({},C,((x={})[y]=g?"0":"",x[v]=m?"0":"",x.transform=(b.devicePixelRatio||1)<2?"translate("+f+"px, "+p+"px)":"translate3d("+f+"px, "+p+"px, 0)",x)):Object.assign({},C,((t={})[y]=g?p+"px":"",t[v]=m?f+"px":"",t.transform="",t))}const et={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},o=t.attributes[e]||{},r=t.elements[e];he(r)&&me(r)&&(Object.assign(r.style,n),Object.keys(o).forEach((function(e){var t=o[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var o=t.elements[e],r=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});he(o)&&me(o)&&(Object.assign(o.style,i),Object.keys(r).forEach((function(e){o.removeAttribute(e)})))}))}},requires:["computeStyles"]};var tt={left:"right",right:"left",bottom:"top",top:"bottom"};function nt(e){return e.replace(/left|right|bottom|top/g,(function(e){return tt[e]}))}var ot={start:"end",end:"start"};function rt(e){return e.replace(/start|end/g,(function(e){return ot[e]}))}function it(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&pe(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function lt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function at(e,t){return t===Ae?lt(function(e){var t=ue(e),n=ge(e),o=t.visualViewport,r=n.clientWidth,i=n.clientHeight,l=0,a=0;return o&&(r=o.width,i=o.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(l=o.offsetLeft,a=o.offsetTop)),{width:r,height:i,x:l+ve(e),y:a}}(e)):he(t)?function(e){var t=ce(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):lt(function(e){var t,n=ge(e),o=de(e),r=null==(t=e.ownerDocument)?void 0:t.body,i=Xe(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),l=Xe(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),a=-o.scrollLeft+ve(e),s=-o.scrollTop;return"rtl"===ye(r||n).direction&&(a+=Xe(n.clientWidth,r?r.clientWidth:0)-i),{width:i,height:l,x:a,y:s}}(ge(e)))}function st(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function ct(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function ut(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=void 0===o?e.placement:o,i=n.boundary,l=void 0===i?"clippingParents":i,a=n.rootBoundary,s=void 0===a?Ae:a,c=n.elementContext,u=void 0===c?Le:c,d=n.altBoundary,f=void 0!==d&&d,h=n.padding,p=void 0===h?0:h,m=st("number"!=typeof p?p:ct(p,Me)),g=u===Le?"reference":Le,v=e.elements.reference,y=e.rects.popper,b=e.elements[f?g:u],_=function(e,t,n){var o="clippingParents"===t?function(e){var t=xe(Se(e)),n=["absolute","fixed"].indexOf(ye(e).position)>=0&&he(e)?Oe(e):e;return fe(n)?t.filter((function(e){return fe(e)&&it(e,n)&&"body"!==me(e)})):[]}(e):[].concat(t),r=[].concat(o,[n]),i=r[0],l=r.reduce((function(t,n){var o=at(e,n);return t.top=Xe(o.top,t.top),t.right=Je(o.right,t.right),t.bottom=Je(o.bottom,t.bottom),t.left=Xe(o.left,t.left),t}),at(e,i));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}(fe(b)?b:b.contextElement||ge(e.elements.popper),l,s),w=ce(v),S=Ze({reference:w,element:y,strategy:"absolute",placement:r}),x=lt(Object.assign({},y,S)),C=u===Le?x:w,R={top:_.top-C.top+m.top,bottom:C.bottom-_.bottom+m.bottom,left:_.left-C.left+m.left,right:C.right-_.right+m.right},O=e.modifiersData.offset;if(u===Le&&O){var T=O[r];Object.keys(R).forEach((function(e){var t=[ke,ze].indexOf(e)>=0?1:-1,n=[Te,ze].indexOf(e)>=0?"y":"x";R[e]+=T[n]*t}))}return R}function dt(e,t,n){return Xe(e,Je(t,n))}function ft(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function ht(e){return[Te,ke,ze,Pe].some((function(t){return e[t]>=0}))}var pt=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,o=void 0===n?[]:n,r=t.defaultOptions,i=void 0===r?We:r;return function(e,t,n){void 0===n&&(n=i);var r,l,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},We,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],c=!1,u={state:a,setOptions:function(n){d(),a.options=Object.assign({},i,a.options,n),a.scrollParents={reference:fe(e)?xe(e):e.contextElement?xe(e.contextElement):[],popper:xe(t)};var r,l,c=function(e){var t=Ne(e);return De.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((r=[].concat(o,a.options.modifiers),l=r.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(l).map((function(e){return l[e]}))));return a.orderedModifiers=c.filter((function(e){return e.enabled})),a.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,o=void 0===n?{}:n,r=e.effect;if("function"==typeof r){var i=r({state:a,name:t,instance:u,options:o});s.push(i||function(){})}})),u.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,n=e.popper;if(Ge(t,n)){a.rects={reference:_e(t,Oe(n),"fixed"===a.options.strategy),popper:we(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(e){return a.modifiersData[e.name]=Object.assign({},e.data)}));for(var o=0;o<a.orderedModifiers.length;o++)if(!0!==a.reset){var r=a.orderedModifiers[o],i=r.fn,l=r.options,s=void 0===l?{}:l,d=r.name;"function"==typeof i&&(a=i({state:a,options:s,name:d,instance:u})||a)}else a.reset=!1,o=-1}}},update:(r=function(){return new Promise((function(e){u.forceUpdate(),e(a)}))},function(){return l||(l=new Promise((function(e){Promise.resolve().then((function(){l=void 0,e(r())}))}))),l}),destroy:function(){d(),c=!0}};if(!Ge(e,t))return u;function d(){s.forEach((function(e){return e()})),s=[]}return u.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}({defaultModifiers:[Ue,Ye,{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=void 0===o||o,i=n.adaptive,l=void 0===i||i,a=n.roundOffsets,s=void 0===a||a,c={placement:Be(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Qe(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:l,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Qe(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},et,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.offset,i=void 0===r?[0,0]:r,l=He.reduce((function(e,n){return e[n]=function(e,t,n){var o=Be(e),r=[Pe,Te].indexOf(o)>=0?-1:1,i="function"==typeof n?n(Object.assign({},t,{placement:e})):n,l=i[0],a=i[1];return l=l||0,a=(a||0)*r,[Pe,ke].indexOf(o)>=0?{x:a,y:l}:{x:l,y:a}}(n,t.rects,i),e}),{}),a=l[t.placement],s=a.x,c=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=c),t.modifiersData[o]=l}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var r=n.mainAxis,i=void 0===r||r,l=n.altAxis,a=void 0===l||l,s=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,f=n.altBoundary,h=n.flipVariations,p=void 0===h||h,m=n.allowedAutoPlacements,g=t.options.placement,v=Be(g),y=s||(v!==g&&p?function(e){if(Be(e)===Ie)return[];var t=nt(e);return[rt(e),t,rt(t)]}(g):[nt(g)]),b=[g].concat(y).reduce((function(e,n){return e.concat(Be(n)===Ie?function(e,t){void 0===t&&(t={});var n=t,o=n.placement,r=n.boundary,i=n.rootBoundary,l=n.padding,a=n.flipVariations,s=n.allowedAutoPlacements,c=void 0===s?He:s,u=Ve(o),d=u?a?je:je.filter((function(e){return Ve(e)===u})):Me,f=d.filter((function(e){return c.indexOf(e)>=0}));0===f.length&&(f=d);var h=f.reduce((function(t,n){return t[n]=ut(e,{placement:n,boundary:r,rootBoundary:i,padding:l})[Be(n)],t}),{});return Object.keys(h).sort((function(e,t){return h[e]-h[t]}))}(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:p,allowedAutoPlacements:m}):n)}),[]),_=t.rects.reference,w=t.rects.popper,S=new Map,x=!0,C=b[0],R=0;R<b.length;R++){var O=b[R],T=Be(O),z=Ve(O)===Ee,k=[Te,ze].indexOf(T)>=0,P=k?"width":"height",I=ut(t,{placement:O,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),M=k?z?ke:Pe:z?ze:Te;_[P]>w[P]&&(M=nt(M));var E=nt(M),A=[];if(i&&A.push(I[T]<=0),a&&A.push(I[M]<=0,I[E]<=0),A.every((function(e){return e}))){C=O,x=!1;break}S.set(O,A)}if(x)for(var L=function(e){var t=b.find((function(t){var n=S.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return C=t,"break"},j=p?3:1;j>0&&"break"!==L(j);j--);t.placement!==C&&(t.modifiersData[o]._skip=!0,t.placement=C,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,r=n.mainAxis,i=void 0===r||r,l=n.altAxis,a=void 0!==l&&l,s=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,f=n.tether,h=void 0===f||f,p=n.tetherOffset,m=void 0===p?0:p,g=ut(t,{boundary:s,rootBoundary:c,padding:d,altBoundary:u}),v=Be(t.placement),y=Ve(t.placement),b=!y,_=qe(v),w="x"===_?"y":"x",S=t.modifiersData.popperOffsets,x=t.rects.reference,C=t.rects.popper,R="function"==typeof m?m(Object.assign({},t.rects,{placement:t.placement})):m,O={x:0,y:0};if(S){if(i||a){var T="y"===_?Te:Pe,z="y"===_?ze:ke,k="y"===_?"height":"width",P=S[_],I=S[_]+g[T],M=S[_]-g[z],E=h?-C[k]/2:0,A=y===Ee?x[k]:C[k],L=y===Ee?-C[k]:-x[k],j=t.elements.arrow,H=h&&j?we(j):{width:0,height:0},D=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},N=D[T],W=D[z],G=dt(0,x[k],H[k]),F=b?x[k]/2-E-G-N-R:A-G-N-R,U=b?-x[k]/2+E+G+W+R:L+G+W+R,B=t.elements.arrow&&Oe(t.elements.arrow),V=B?"y"===_?B.clientTop||0:B.clientLeft||0:0,q=t.modifiersData.offset?t.modifiersData.offset[t.placement][_]:0,Z=S[_]+F-q-V,Y=S[_]+U-q;if(i){var X=dt(h?Je(I,Z):I,P,h?Xe(M,Y):M);S[_]=X,O[_]=X-P}if(a){var J="x"===_?Te:Pe,K="x"===_?ze:ke,$=S[w],Q=$+g[J],ee=$-g[K],te=dt(h?Je(Q,Z):Q,$,h?Xe(ee,Y):ee);S[w]=te,O[w]=te-$}}t.modifiersData[o]=O}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,r=e.options,i=n.elements.arrow,l=n.modifiersData.popperOffsets,a=Be(n.placement),s=qe(a),c=[Pe,ke].indexOf(a)>=0?"height":"width";if(i&&l){var u=function(e,t){return st("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:ct(e,Me))}(r.padding,n),d=we(i),f="y"===s?Te:Pe,h="y"===s?ze:ke,p=n.rects.reference[c]+n.rects.reference[s]-l[s]-n.rects.popper[c],m=l[s]-n.rects.reference[s],g=Oe(i),v=g?"y"===s?g.clientHeight||0:g.clientWidth||0:0,y=p/2-m/2,b=u[f],_=v-d[c]-u[h],w=v/2-d[c]/2+y,S=dt(b,w,_),x=s;n.modifiersData[o]=((t={})[x]=S,t.centerOffset=S-w,t)}},effect:function(e){var t=e.state,n=e.options.element,o=void 0===n?"[data-popper-arrow]":n;null!=o&&("string"!=typeof o||(o=t.elements.popper.querySelector(o)))&&it(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,r=t.rects.popper,i=t.modifiersData.preventOverflow,l=ut(t,{elementContext:"reference"}),a=ut(t,{altBoundary:!0}),s=ft(l,o),c=ft(a,r,i),u=ht(s),d=ht(c);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}}]}),mt="tippy-content",gt="tippy-arrow",vt="tippy-svg-arrow",yt={passive:!0,capture:!0};function bt(e,t,n){if(Array.isArray(e)){var o=e[t];return null==o?Array.isArray(n)?n[t]:n:o}return e}function _t(e,t){var n={}.toString.call(e);return 0===n.indexOf("[object")&&n.indexOf(t+"]")>-1}function wt(e,t){return"function"==typeof e?e.apply(void 0,t):e}function St(e,t){return 0===t?e:function(o){clearTimeout(n),n=setTimeout((function(){e(o)}),t)};var n}function xt(e){return[].concat(e)}function Ct(e,t){-1===e.indexOf(t)&&e.push(t)}function Rt(e){return[].slice.call(e)}function Ot(){return document.createElement("div")}function Tt(e){return["Element","Fragment"].some((function(t){return _t(e,t)}))}function zt(e,t){e.forEach((function(e){e&&(e.style.transitionDuration=t+"ms")}))}function kt(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function Pt(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(t){e[o](t,n)}))}var It={isTouch:!1},Mt=0;function Et(){It.isTouch||(It.isTouch=!0,window.performance&&document.addEventListener("mousemove",At))}function At(){var e=performance.now();e-Mt<20&&(It.isTouch=!1,document.removeEventListener("mousemove",At)),Mt=e}function Lt(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var jt="undefined"!=typeof window&&"undefined"!=typeof document?navigator.userAgent:"",Ht=/MSIE |Trident\//.test(jt),Dt=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Nt=Object.keys(Dt);function Wt(e){var t=(e.plugins||[]).reduce((function(t,n){var o=n.name,r=n.defaultValue;return o&&(t[o]=void 0!==e[o]?e[o]:r),t}),{});return Object.assign({},e,{},t)}function Gt(e,t){var n=Object.assign({},t,{content:wt(t.content,[e])},t.ignoreAttributes?{}:function(e,t){return(t?Object.keys(Wt(Object.assign({},Dt,{plugins:t}))):Nt).reduce((function(t,n){var o=(e.getAttribute("data-tippy-"+n)||"").trim();if(!o)return t;if("content"===n)t[n]=o;else try{t[n]=JSON.parse(o)}catch(e){t[n]=o}return t}),{})}(e,t.plugins));return n.aria=Object.assign({},Dt.aria,{},n.aria),n.aria={expanded:"auto"===n.aria.expanded?t.interactive:n.aria.expanded,content:"auto"===n.aria.content?t.interactive?null:"describedby":n.aria.content},n}function Ft(e,t){e.innerHTML=t}function Ut(e){var t=Ot();return!0===e?t.className=gt:(t.className=vt,Tt(e)?t.appendChild(e):Ft(t,e)),t}function Bt(e,t){Tt(t.content)?(Ft(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?Ft(e,t.content):e.textContent=t.content)}function Vt(e){var t=e.firstElementChild,n=Rt(t.children);return{box:t,content:n.find((function(e){return e.classList.contains(mt)})),arrow:n.find((function(e){return e.classList.contains(gt)||e.classList.contains(vt)})),backdrop:n.find((function(e){return e.classList.contains("tippy-backdrop")}))}}function qt(e){var t=Ot(),n=Ot();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=Ot();function r(n,o){var r=Vt(t),i=r.box,l=r.content,a=r.arrow;o.theme?i.setAttribute("data-theme",o.theme):i.removeAttribute("data-theme"),"string"==typeof o.animation?i.setAttribute("data-animation",o.animation):i.removeAttribute("data-animation"),o.inertia?i.setAttribute("data-inertia",""):i.removeAttribute("data-inertia"),i.style.maxWidth="number"==typeof o.maxWidth?o.maxWidth+"px":o.maxWidth,o.role?i.setAttribute("role",o.role):i.removeAttribute("role"),n.content===o.content&&n.allowHTML===o.allowHTML||Bt(l,e.props),o.arrow?a?n.arrow!==o.arrow&&(i.removeChild(a),i.appendChild(Ut(o.arrow))):i.appendChild(Ut(o.arrow)):a&&i.removeChild(a)}return o.className=mt,o.setAttribute("data-state","hidden"),Bt(o,e.props),t.appendChild(n),n.appendChild(o),r(e.props,e.props),{popper:t,onUpdate:r}}qt.$$tippy=!0;var Zt=1,Yt=[],Xt=[];function Jt(e,t){void 0===t&&(t={});var n=Dt.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",Et,yt),window.addEventListener("blur",Lt);var o,r=Object.assign({},t,{plugins:n}),i=(o=e,Tt(o)?[o]:function(e){return _t(e,"NodeList")}(o)?Rt(o):Array.isArray(o)?o:Rt(document.querySelectorAll(o))).reduce((function(e,t){var n=t&&function(e,t){var n,o,r,i,l,a,s,c,u,d=Gt(e,Object.assign({},Dt,{},Wt((n=t,Object.keys(n).reduce((function(e,t){return void 0!==n[t]&&(e[t]=n[t]),e}),{}))))),f=!1,h=!1,p=!1,m=!1,g=[],v=St(Y,d.interactiveDebounce),y=Zt++,b=(u=d.plugins).filter((function(e,t){return u.indexOf(e)===t})),_={id:y,reference:e,popper:Ot(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:b,clearDelayTimeouts:function(){clearTimeout(o),clearTimeout(r),cancelAnimationFrame(i)},setProps:function(t){if(!_.state.isDestroyed){A("onBeforeUpdate",[_,t]),q();var n=_.props,o=Gt(e,Object.assign({},_.props,{},t,{ignoreAttributes:!0}));_.props=o,V(),n.interactiveDebounce!==o.interactiveDebounce&&(H(),v=St(Y,o.interactiveDebounce)),n.triggerTarget&&!o.triggerTarget?xt(n.triggerTarget).forEach((function(e){e.removeAttribute("aria-expanded")})):o.triggerTarget&&e.removeAttribute("aria-expanded"),j(),E(),x&&x(n,o),_.popperInstance&&($(),ee().forEach((function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)}))),A("onAfterUpdate",[_,t])}},setContent:function(e){_.setProps({content:e})},show:function(){var e=_.state.isVisible,t=_.state.isDestroyed,n=!_.state.isEnabled,o=It.isTouch&&!_.props.touch,r=bt(_.props.duration,0,Dt.duration);if(!(e||t||n||o||k().hasAttribute("disabled")||(A("onShow",[_],!1),!1===_.props.onShow(_)))){if(_.state.isVisible=!0,z()&&(S.style.visibility="visible"),E(),G(),_.state.isMounted||(S.style.transition="none"),z()){var i=I();zt([i.box,i.content],0)}var l,a,c;s=function(){var e;if(_.state.isVisible&&!m){if(m=!0,S.offsetHeight,S.style.transition=_.props.moveTransition,z()&&_.props.animation){var t=I(),n=t.box,o=t.content;zt([n,o],r),kt([n,o],"visible")}L(),j(),Ct(Xt,_),null==(e=_.popperInstance)||e.forceUpdate(),_.state.isMounted=!0,A("onMount",[_]),_.props.animation&&z()&&function(e,t){U(e,(function(){_.state.isShown=!0,A("onShown",[_])}))}(r)}},a=_.props.appendTo,c=k(),(l=_.props.interactive&&a===Dt.appendTo||"parent"===a?c.parentNode:wt(a,[c])).contains(S)||l.appendChild(S),$()}},hide:function(){var e=!_.state.isVisible,t=_.state.isDestroyed,n=!_.state.isEnabled,o=bt(_.props.duration,1,Dt.duration);if(!(e||t||n)&&(A("onHide",[_],!1),!1!==_.props.onHide(_))){if(_.state.isVisible=!1,_.state.isShown=!1,m=!1,f=!1,z()&&(S.style.visibility="hidden"),H(),F(),E(),z()){var r=I(),i=r.box,l=r.content;_.props.animation&&(zt([i,l],o),kt([i,l],"hidden"))}L(),j(),_.props.animation?z()&&function(e,t){U(e,(function(){!_.state.isVisible&&S.parentNode&&S.parentNode.contains(S)&&t()}))}(o,_.unmount):_.unmount()}},hideWithInteractivity:function(e){P().addEventListener("mousemove",v),Ct(Yt,v),v(e)},enable:function(){_.state.isEnabled=!0},disable:function(){_.hide(),_.state.isEnabled=!1},unmount:function(){_.state.isVisible&&_.hide(),_.state.isMounted&&(Q(),ee().forEach((function(e){e._tippy.unmount()})),S.parentNode&&S.parentNode.removeChild(S),Xt=Xt.filter((function(e){return e!==_})),_.state.isMounted=!1,A("onHidden",[_]))},destroy:function(){_.state.isDestroyed||(_.clearDelayTimeouts(),_.unmount(),q(),delete e._tippy,_.state.isDestroyed=!0,A("onDestroy",[_]))}};if(!d.render)return _;var w=d.render(_),S=w.popper,x=w.onUpdate;S.setAttribute("data-tippy-root",""),S.id="tippy-"+_.id,_.popper=S,e._tippy=_,S._tippy=_;var C=b.map((function(e){return e.fn(_)})),R=e.hasAttribute("aria-expanded");return V(),j(),E(),A("onCreate",[_]),d.showOnCreate&&te(),S.addEventListener("mouseenter",(function(){_.props.interactive&&_.state.isVisible&&_.clearDelayTimeouts()})),S.addEventListener("mouseleave",(function(e){_.props.interactive&&_.props.trigger.indexOf("mouseenter")>=0&&(P().addEventListener("mousemove",v),v(e))})),_;function O(){var e=_.props.touch;return Array.isArray(e)?e:[e,0]}function T(){return"hold"===O()[0]}function z(){var e;return!!(null==(e=_.props.render)?void 0:e.$$tippy)}function k(){return c||e}function P(){var e,t,n=k().parentNode;return n&&(null==(t=xt(n)[0])||null==(e=t.ownerDocument)?void 0:e.body)?t.ownerDocument:document}function I(){return Vt(S)}function M(e){return _.state.isMounted&&!_.state.isVisible||It.isTouch||l&&"focus"===l.type?0:bt(_.props.delay,e?0:1,Dt.delay)}function E(){S.style.pointerEvents=_.props.interactive&&_.state.isVisible?"":"none",S.style.zIndex=""+_.props.zIndex}function A(e,t,n){var o;void 0===n&&(n=!0),C.forEach((function(n){n[e]&&n[e].apply(void 0,t)})),n&&(o=_.props)[e].apply(o,t)}function L(){var t=_.props.aria;if(t.content){var n="aria-"+t.content,o=S.id;xt(_.props.triggerTarget||e).forEach((function(e){var t=e.getAttribute(n);if(_.state.isVisible)e.setAttribute(n,t?t+" "+o:o);else{var r=t&&t.replace(o,"").trim();r?e.setAttribute(n,r):e.removeAttribute(n)}}))}}function j(){!R&&_.props.aria.expanded&&xt(_.props.triggerTarget||e).forEach((function(e){_.props.interactive?e.setAttribute("aria-expanded",_.state.isVisible&&e===k()?"true":"false"):e.removeAttribute("aria-expanded")}))}function H(){P().removeEventListener("mousemove",v),Yt=Yt.filter((function(e){return e!==v}))}function D(e){if(!(It.isTouch&&(p||"mousedown"===e.type)||_.props.interactive&&S.contains(e.target))){if(k().contains(e.target)){if(It.isTouch)return;if(_.state.isVisible&&_.props.trigger.indexOf("click")>=0)return}else A("onClickOutside",[_,e]);!0===_.props.hideOnClick&&(_.clearDelayTimeouts(),_.hide(),h=!0,setTimeout((function(){h=!1})),_.state.isMounted||F())}}function N(){p=!0}function W(){p=!1}function G(){var e=P();e.addEventListener("mousedown",D,!0),e.addEventListener("touchend",D,yt),e.addEventListener("touchstart",W,yt),e.addEventListener("touchmove",N,yt)}function F(){var e=P();e.removeEventListener("mousedown",D,!0),e.removeEventListener("touchend",D,yt),e.removeEventListener("touchstart",W,yt),e.removeEventListener("touchmove",N,yt)}function U(e,t){var n=I().box;function o(e){e.target===n&&(Pt(n,"remove",o),t())}if(0===e)return t();Pt(n,"remove",a),Pt(n,"add",o),a=o}function B(t,n,o){void 0===o&&(o=!1),xt(_.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,o),g.push({node:e,eventType:t,handler:n,options:o})}))}function V(){var e;T()&&(B("touchstart",Z,{passive:!0}),B("touchend",X,{passive:!0})),(e=_.props.trigger,e.split(/\s+/).filter(Boolean)).forEach((function(e){if("manual"!==e)switch(B(e,Z),e){case"mouseenter":B("mouseleave",X);break;case"focus":B(Ht?"focusout":"blur",J);break;case"focusin":B("focusout",J)}}))}function q(){g.forEach((function(e){var t=e.node,n=e.eventType,o=e.handler,r=e.options;t.removeEventListener(n,o,r)})),g=[]}function Z(e){var t,n=!1;if(_.state.isEnabled&&!K(e)&&!h){var o="focus"===(null==(t=l)?void 0:t.type);l=e,c=e.currentTarget,j(),!_.state.isVisible&&_t(e,"MouseEvent")&&Yt.forEach((function(t){return t(e)})),"click"===e.type&&(_.props.trigger.indexOf("mouseenter")<0||f)&&!1!==_.props.hideOnClick&&_.state.isVisible?n=!0:te(e),"click"===e.type&&(f=!n),n&&!o&&ne(e)}}function Y(e){var t=e.target,n=k().contains(t)||S.contains(t);"mousemove"===e.type&&n||function(e,t){var n=t.clientX,o=t.clientY;return e.every((function(e){var t=e.popperRect,r=e.popperState,i=e.props.interactiveBorder,l=r.placement.split("-")[0],a=r.modifiersData.offset;if(!a)return!0;var s="bottom"===l?a.top.y:0,c="top"===l?a.bottom.y:0,u="right"===l?a.left.x:0,d="left"===l?a.right.x:0,f=t.top-o+s>i,h=o-t.bottom-c>i,p=t.left-n+u>i,m=n-t.right-d>i;return f||h||p||m}))}(ee().concat(S).map((function(e){var t,n=null==(t=e._tippy.popperInstance)?void 0:t.state;return n?{popperRect:e.getBoundingClientRect(),popperState:n,props:d}:null})).filter(Boolean),e)&&(H(),ne(e))}function X(e){K(e)||_.props.trigger.indexOf("click")>=0&&f||(_.props.interactive?_.hideWithInteractivity(e):ne(e))}function J(e){_.props.trigger.indexOf("focusin")<0&&e.target!==k()||_.props.interactive&&e.relatedTarget&&S.contains(e.relatedTarget)||ne(e)}function K(e){return!!It.isTouch&&T()!==e.type.indexOf("touch")>=0}function $(){Q();var t=_.props,n=t.popperOptions,o=t.placement,r=t.offset,i=t.getReferenceClientRect,l=t.moveTransition,a=z()?Vt(S).arrow:null,c=i?{getBoundingClientRect:i,contextElement:i.contextElement||k()}:e,u=[{name:"offset",options:{offset:r}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!l}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t=e.state;if(z()){var n=I().box;["placement","reference-hidden","escaped"].forEach((function(e){"placement"===e?n.setAttribute("data-placement",t.placement):t.attributes.popper["data-popper-"+e]?n.setAttribute("data-"+e,""):n.removeAttribute("data-"+e)})),t.attributes.popper={}}}}];z()&&a&&u.push({name:"arrow",options:{element:a,padding:3}}),u.push.apply(u,(null==n?void 0:n.modifiers)||[]),_.popperInstance=pt(c,S,Object.assign({},n,{placement:o,onFirstUpdate:s,modifiers:u}))}function Q(){_.popperInstance&&(_.popperInstance.destroy(),_.popperInstance=null)}function ee(){return Rt(S.querySelectorAll("[data-tippy-root]"))}function te(e){_.clearDelayTimeouts(),e&&A("onTrigger",[_,e]),G();var t=M(!0),n=O(),r=n[0],i=n[1];It.isTouch&&"hold"===r&&i&&(t=i),t?o=setTimeout((function(){_.show()}),t):_.show()}function ne(e){if(_.clearDelayTimeouts(),A("onUntrigger",[_,e]),_.state.isVisible){if(!(_.props.trigger.indexOf("mouseenter")>=0&&_.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(e.type)>=0&&f)){var t=M(!1);t?r=setTimeout((function(){_.state.isVisible&&_.hide()}),t):i=requestAnimationFrame((function(){_.hide()}))}}else F()}}(t,r);return n&&e.push(n),e}),[]);return Tt(e)?i[0]:i}Jt.defaultProps=Dt,Jt.setDefaultProps=function(e){Object.keys(e).forEach((function(t){Dt[t]=e[t]}))},Jt.currentInput=It,Object.assign({},et,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Jt.setDefaultProps({render:qt});const Kt=Jt;function $t(e){return($t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Qt(e,t){return(Qt=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function en(e,t){return!t||"object"!==$t(t)&&"function"!=typeof t?tn(e):t}function tn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nn(e){return(nn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function on(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var rn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Qt(e,t)}(l,e);var n,o,r,i=(o=l,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=nn(o);if(r){var n=nn(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return en(this,e)});function l(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),on(tn(n=i.call(this,e)),"delete",(function(e){e.preventDefault(),n.props.onDelete()})),on(tn(n),"editStyle",(function(e){e.preventDefault(),n.props.onEditStyle()})),n.tooltipContentRef=t().createRef(),n.editStyleRef=t().createRef(),n.deleteRef=t().createRef(),n}return(n=[{key:"componentDidMount",value:function(){Kt(this.editStyleRef.current,{content:"Style",appendTo:this.tooltipContentRef.current}),Kt(this.deleteRef.current,{content:"Delete",appendTo:this.tooltipContentRef.current}),this.editStyleRef.current.addEventListener("click",this.editStyle),this.deleteRef.current.addEventListener("click",this.delete)}},{key:"componentWillUnmount",value:function(){this.editStyleRef.current.removeEventListener("click",this.editStyle),this.deleteRef.current.removeEventListener("click",this.delete)}},{key:"render",value:function(){return t().createElement("div",{className:"rm-tooltip-content",ref:this.tooltipContentRef},this.props.showStyleButton&&t().createElement("div",{className:"rm-tooltip-icon",ref:this.editStyleRef,dangerouslySetInnerHTML:{__html:se}}),t().createElement("div",{className:"rm-tooltip-icon",ref:this.deleteRef,dangerouslySetInnerHTML:{__html:ae}}))}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(l.prototype,n),l}(t().PureComponent),ln=n(662),an=n(421),sn=n(370);function cn(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return un(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?un(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){a=!0,i=e},f:function(){try{l||null==n.return||n.return()}finally{if(a)throw i}}}}function un(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function dn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function fn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?dn(Object(n),!0).forEach((function(t){mn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function hn(e,t,n,o,r,i,l){try{var a=e[i](l),s=a.value}catch(e){return void n(e)}a.done?t(s):Promise.resolve(s).then(o,r)}function pn(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function l(e){hn(i,o,r,l,a,"next",e)}function a(e){hn(i,o,r,l,a,"throw",e)}l(void 0)}))}}function mn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var gn={Highlight:8,Underline:9,Strikeout:11,Squiggly:10},vn=Object.values(gn),yn="annot-id",bn="text-annot",_n="selected-annot",wn="selected-annot-start",Sn="selected-annot-end",xn=function(){function e(t,n,o,r){var i=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),mn(this,"cleanUpSelectedAnnot",(function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(i.selectedAnnot){if(e){var t=i.annotNodeMap.get(i.selectedAnnot.id)||[];t.forEach((function(e){var t=e.classList;t.remove(_n),t.remove(wn),t.remove(Sn)}))}i.selectedAnnot=void 0}})),mn(this,"removeSelectedAnnot",pn(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i.selectedAnnot){e.next=2;break}return e.abrupt("return");case 2:return t=i.selectedAnnot.id,e.next=5,i.pdfNetReflow.setAnnot(JSON.stringify({id:t}));case 5:e.sent===t?(i.cleanUpSelectedAnnot(!1),i.cleanUpTooltip(),(i.annotNodeMap.get(t)||[]).forEach(Pn),i.annotNodeMap.set(t,[])):console.error("Calling 'setAnnot()' to remove annotation failed.");case 7:case"end":return e.stop()}}),e)})))),mn(this,"setSelectedAnnotStyle",function(){var e=pn(regeneratorRuntime.mark((function e(t){var n,o,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.color,o=t.opacity,i.selectedAnnot){e.next=3;break}return e.abrupt("return");case 3:return r=i.selectedAnnot.id,e.next=6,i.pdfNetReflow.setAnnot(JSON.stringify({id:r,color:n.substring(1),opacity:o}));case 6:e.sent===r?(i.selectedAnnot.origAnnot.color=n,i.selectedAnnot.origAnnot.opacity=o,(i.annotNodeMap.get(r)||[]).forEach((function(e){return Ln(e,{color:n,opacity:o})}))):console.error("Calling 'setAnnot()' to change annotation style failed.");case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),mn(this,"onEditStyle",(function(){i.cleanUpTooltip(),i.editStyleHandler(fn(fn({},i.selectedAnnot),{},{position:i.getSelectedAnnotPos()}),i.setSelectedAnnotStyle,i.cleanUpSelectedAnnot)})),this.pageWindow=t,this.pdfNetReflow=n,this.editStyleHandler=o,this.getViewerElement=r,this.annotNodeMap=new Map,this.addAnnotConfig={},this.selectionStyle=void 0,this.selectedAnnot=void 0,this.currentSelectRange=void 0,this.loadAnnotations(),this.enableAddAnnotSupport(),this.setupTooltip()}var n,o,i,l;return n=e,(o=[{key:"loadAnnotations",value:(l=pn(regeneratorRuntime.mark((function e(){var t,n,o,r,i,l,a,s,c,u,d,f,h,p;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=ie(this.pageWindow)){e.next=3;break}return e.abrupt("return");case 3:n=t.getElementsByTagName("p"),o=[0],r=cn(n);try{for(r.s();!(i=r.n()).done;)l=i.value,o.push(o[o.length-1]+l.textContent.length)}catch(e){r.e(e)}finally{r.f()}return e.next=9,this.pdfNetReflow.getAnnot("");case 9:a=e.sent,s=JSON.parse(a).map(In).filter(Mn),c=cn(s),e.prev=13,c.s();case 15:if((u=c.n()).done){e.next=31;break}d=u.value,f=0;case 18:if(!(f<o.length-1)){e.next=29;break}if(!(d.endOffset<=o[f])){e.next=21;break}return e.abrupt("break",29);case 21:if(!(d.startOffset>=o[f+1])){e.next=23;break}return e.abrupt("continue",26);case 23:h=Math.max(o[f],d.startOffset),p=Math.min(o[f+1],d.endOffset),this.addAnnotToParagraph(n[f],fn(fn({},d),{},{startOffset:h-o[f],endOffset:p-o[f]}));case 26:f++,e.next=18;break;case 29:e.next=15;break;case 31:e.next=36;break;case 33:e.prev=33,e.t0=e.catch(13),c.e(e.t0);case 36:return e.prev=36,c.f(),e.finish(36);case 39:case"end":return e.stop()}}),e,this,[[13,33,36,39]])}))),function(){return l.apply(this,arguments)})},{key:"addAnnotToParagraph",value:function(e,t,n,o){var r=this,i=n||Cn(e,t.startOffset,!0,0),l=o||Cn(e,t.endOffset,!1,0),a=function(e,n){if(e.offset!==n.offset){var o=e.textNode.splitText(e.offset),i=r.insertAnnotBeforeNode(t,o);o.splitText(n.offset-e.offset),Rn(o,i,t.type)}};if(i.textNode===l.textNode)a(i,l);else if(i.textNode.parentNode===l.textNode.parentNode)for(var s=i.textNode.splitText(i.offset),c=this.insertAnnotBeforeNode(t,s),u=l.textNode.splitText(l.offset),d=s;d&&d!=u;){var f=d.nextSibling;Rn(d,c,t.type),d=f}else for(var h=function e(n,o){if(!n)return!1;var i=!1;if(n===l.textNode)return a({textNode:l.textNode,offset:0},l),!0;for(var s,c=(s=o?r.insertAnnotBeforeNode(t,n.childNodes[0]):r.insertAnnotBeforeNode(t,n)).nextSibling;c;)if(""!==c.textContent){if(On(c,l.textNode)){e(c,!0),i=!0;break}var u=c.nextSibling;Rn(c,s,t.type),c=u}else c=c.nextSibling;return""===s.textContent&&s.remove(),i},p=i.textNode.splitText(i.offset);p.previousSibling!==e;){for(var m=p.parentNode;!m.nextSibling&&m!==e;)m=m.parentNode;var g=m.nextSibling;if(h(p,!1))break;p=g}}},{key:"addAnnotFromRange",value:(i=pn(regeneratorRuntime.mark((function e(t,n){var o,r,i,l,a,s,c,u,d,f,h,p,m,g,v,y,b,_;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Mn(n)){e.next=3;break}return console.error("Invalid annotation."),e.abrupt("return");case 3:return o=t.startContainer,r=t.startOffset,i=t.endContainer,l=t.endOffset,a=An(o,r),s=An(i,l),c=fn(fn({},n),{},{color:n.color.substring(1),ranges:[a,s-1]}),e.next=13,this.pdfNetReflow.setAnnot(JSON.stringify(c));case 13:if((u=e.sent)&&""!==u)if(d=fn(fn({},n),{},{id:u}),f=Tn(o),h=Tn(i),f===h)this.addAnnotToParagraph(f,d,{textNode:o,offset:r},{textNode:i,offset:l});else{for(p=zn(h),this.addAnnotToParagraph(f,d,{textNode:o,offset:r},{textNode:p,offset:p.textContent.length}),m=f.nextSibling;m!==h;){if(En(m)&&m.textContent.length>0)for(g=m.firstChild,v=this.insertAnnotBeforeNode(d,g),y=v.nextSibling;y;)b=y.nextSibling,Rn(y,v,d.type),y=b;m=m.nextSibling}_=kn(h),this.addAnnotToParagraph(m,d,{textNode:_,offset:0},{textNode:i,offset:l})}else console.error("Calling 'setAnnot()' to create annotation failed.");case 15:case"end":return e.stop()}}),e,this)}))),function(e,t){return i.apply(this,arguments)})},{key:"insertAnnotBeforeNode",value:function(e,t){var n=this,o=function(e,t){var n,o=document.createElement("span");return e.type===gn.Highlight?(Ln(o,e),o.setAttribute(yn,e.id),o.className=bn,o):(o.style.color=t,e.type===gn.Underline?n=document.createElement("u"):e.type===gn.Strikeout?n=document.createElement("s"):e.type===gn.Squiggly&&((n=document.createElement("u")).style.textDecorationStyle="wavy"),Ln(n,e),n.setAttribute(yn,e.id),n.appendChild(o),n.className=bn,n)}(e,window.getComputedStyle(t.parentNode).color),r=e.id;return this.annotNodeMap.has(r)?this.annotNodeMap.get(r).push(o):this.annotNodeMap.set(r,[o]),o.addEventListener("click",(function(t){if(t.stopPropagation(),n.selectedAnnot){if(n.selectedAnnot.id===r)return void n.cleanUpSelectedAnnot();n.cleanUpSelectedAnnot(),n.cleanUpTooltip()}n.selectedAnnot=fn(fn({},e),{},{target:o,origAnnot:e});var i=n.annotNodeMap.get(r)||[];i.forEach((function(e,t){e.classList.add(_n),0===t&&e.classList.add(wn),t===i.length-1&&e.classList.add(Sn)})),n.tooltipContent||(n.tooltipContent=n.tooltip.firstChild),n.tippy=Kt(o,{content:n.tooltipContent,interactive:!0,trigger:"manual",theme:"light-border",arrow:!1,appendTo:ie(n.pageWindow),onClickOutside:function(){n.selectedAnnot&&(n.cleanUpSelectedAnnot(),n.cleanUpTooltip())}}),n.tippy.show()})),t.parentNode.insertBefore(o,t),o}},{key:"setAddAnnotConfig",value:function(e){this.pageWindow.getSelection().removeAllRanges(),this.addAnnotConfig=e,this.setTextSelectionStyle()}},{key:"isValidAddAnnotConfig",value:function(){return this.addAnnotConfig&&this.addAnnotConfig.type}},{key:"enableAddAnnotSupport",value:function(){var e=this,t=le(this.pageWindow);this.selectionStyle=document.createElement("style"),this.selectionStyle.type="text/css",t.appendChild(this.selectionStyle),this.setTextSelectionStyle();var n=function(e){return e&&e.toString()&&e.rangeCount>=1&&e.getRangeAt(0)},o=function(){var t=pn(regeneratorRuntime.mark((function t(n){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.addAnnotFromRange(n,e.addAnnotConfig);case 2:e.pageWindow.getSelection().removeAllRanges(),e.currentSelectRange=void 0;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();this.pageWindow.addEventListener("mouseup",pn(regeneratorRuntime.mark((function t(){var r,i;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.isValidAddAnnotConfig()){t.next=6;break}if(r=e.pageWindow.getSelection(),!n(r)){t.next=6;break}return i=r.getRangeAt(0),t.next=6,o(i);case 6:case"end":return t.stop()}}),t)})))),this.pageWindow.document.addEventListener("selectionchange",pn(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.isValidAddAnnotConfig()){t.next=9;break}if(r=e.pageWindow.getSelection(),!n(r)){t.next=6;break}e.currentSelectRange=r.getRangeAt(0),t.next=9;break;case 6:if(!e.currentSelectRange){t.next=9;break}return t.next=9,o(e.currentSelectRange);case 9:case"end":return t.stop()}}),t)}))))}},{key:"setTextSelectionStyle",value:function(){var e="";if(this.isValidAddAnnotConfig()){var t="",n=Hn(this.addAnnotConfig.color,this.addAnnotConfig.opacity);this.addAnnotConfig.type===gn.Highlight&&(t="background-color: ".concat(n,";")),e="::-moz-selection { ".concat(t," } ::selection { ").concat(t," }")}this.selectionStyle.innerHTML=e}},{key:"setupTooltip",value:function(){this.addSelectedStyle(),this.addTooltipStyle(),this.createTooltipContent()}},{key:"addSelectedStyle",value:function(){var e=le(this.pageWindow);if(e){var t=document.createElement("style");t.type="text/css";var n="1px solid #3183C8";t.innerHTML="\n      .".concat(bn,"{cursor:pointer}\n      .").concat(_n,"{border-top:").concat(n,";border-bottom:").concat(n,";z-index:10;position:relative;}\n      .").concat(wn,"{border-left:").concat(n,";margin-left:-1px;}\n      .").concat(Sn,"{border-right:").concat(n,";margin-right:-1px;}\n    "),e.appendChild(t)}}},{key:"addTooltipStyle",value:function(){var e=le(this.pageWindow);if(e){var t=document.createElement("style");t.type="text/css",t.innerHTML=ln+an+sn,e.appendChild(t)}}},{key:"createTooltipContent",value:function(){var e=ie(this.pageWindow);e&&(this.tooltip=document.createElement("div"),this.tooltip.className="rm-tooltip",this.tooltip.style.display="none",e.appendChild(this.tooltip),r().render(t().createElement(rn,{onDelete:this.removeSelectedAnnot,onEditStyle:this.onEditStyle,showStyleButton:!!this.editStyleHandler}),this.tooltip))}},{key:"cleanUpTooltip",value:function(){var e;null===(e=this.tippy)||void 0===e||e.destroy(),this.tippy=void 0}},{key:"getSelectedAnnotPos",value:function(){var e=this.getViewerElement(),t=re(this.pageWindow),n=e.scrollHeight/t.scrollHeight,o=this.selectedAnnot.target.getBoundingClientRect();return{top:o.top*n-e.scrollTop,bottom:o.bottom*n-e.scrollTop,left:o.left*n,right:o.right*n}}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(n.prototype,o),e}();function Cn(e,t,n,o){var r,i=cn(e.childNodes);try{for(i.s();!(r=i.n()).done;){var l=r.value;if(l.nodeType===Node.TEXT_NODE){var a=l.textContent.length;if(o+=a,n&&o>t||!n&&o>=t)return{textNode:l,offset:t-(o-a)}}else{var s=Cn(l,t,n,o);if(s)return s;o+=l.textContent.length}}}catch(e){i.e(e)}finally{i.f()}}function Rn(e,t,n){n===gn.Highlight?t.appendChild(e):t.firstChild.appendChild(e)}function On(e,t){if(0===e.childNodes.length)return e===t;var n,o=cn(e.childNodes);try{for(o.s();!(n=o.n()).done;)if(On(n.value,t))return!0}catch(e){o.e(e)}finally{o.f()}return!1}function Tn(e){return!e||En(e)?e:Tn(e.parentNode)}function zn(e){for(var t=e.childNodes||[],n=t.length-1;n>=0;n--){var o=t[n];if(o.nodeType===Node.TEXT_NODE&&o.textContent.length>0)return o;var r=zn(o);if(r)return r}}function kn(e){for(var t=e.childNodes||[],n=0;n<t.length;n++){var o=t[n];if(o.nodeType===Node.TEXT_NODE&&o.textContent.length>0)return o;var r=kn(o);if(r)return r}}function Pn(e){var t,n=e.nextSibling;t=jn(e)?e.childNodes:e.firstChild.childNodes;var o,r=cn(t=Array.from(t));try{for(r.s();!(o=r.n()).done;){var i=o.value;e.parentNode.insertBefore(i,n)}}catch(e){r.e(e)}finally{r.f()}e.remove()}function In(e){return fn(fn({},e),{},{color:"#".concat(e.color),startOffset:e.ranges[0],endOffset:e.ranges[e.ranges.length-1]+1})}function Mn(e){return vn.includes(e.type)}function En(e){return"P"===e.tagName}function An(e,t){if(!e)return t;var n=e.parentNode,o=e.previousSibling;return"BODY"!==n.tagName?o?An(o,t+o.textContent.length):An(n,t):o?En(o)?An(o,t+o.textContent.length):An(o,t):t}function Ln(e,t){var n=Hn(t.color,t.opacity);jn(e)?e.style.backgroundColor=n:e.style.color=n}function jn(e){return"SPAN"===e.tagName}function Hn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=parseInt(e.slice(1,3),16),o=parseInt(e.slice(3,5),16),r=parseInt(e.slice(5,7),16);return"rgba(".concat(n,",").concat(o,",").concat(r,",").concat(t,")")}var Dn=n(773);function Nn(e){return(Nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Wn(e,t){return(Wn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Gn(e,t){return!t||"object"!==Nn(t)&&"function"!=typeof t?Fn(e):t}function Fn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Un(e){return(Un=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Bn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Vn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Wn(e,t)}(l,e);var n,o,r,i=(o=l,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Un(o);if(r){var n=Un(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Gn(this,e)});function l(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),Bn(Fn(n=i.call(this,e)),"handleOnLoad",(function(){n.props.page.loaded&&(n.addCssStyle(),n.props.page.pdfNetReflow&&(n.reflow=new xn(n.getPageWindow(),n.props.page.pdfNetReflow,n.props.editStyleHandler,n.props.getViewerElement),n.props.addAnnotConfig&&n.reflow.setAddAnnotConfig(n.props.addAnnotConfig)),n.resetZoom(),n.getPageDoc().addEventListener("click",n.handleClickEvent),n.initialized||(n.pageIframe.current.addEventListener(N,n.resetHeight),n.initialized=!0),n.getPageDoc().addEventListener("mousedown",n.handleMouseDownEvent))})),Bn(Fn(n),"handleClickEvent",(function(e){for(var t=e.target;t&&"P"!==t.tagName&&"BODY"!==t.tagName&&"HTML"!==t.tagName;){if("A"===t.tagName&&t.getAttribute("href")){e.preventDefault(),n.props.clickLinkHandler(t.getAttribute("href"));break}t=t.parentNode}})),Bn(Fn(n),"handleMouseDownEvent",(function(){var e=new MouseEvent("mousedown",{bubbles:!0});n.props.getViewerElement().dispatchEvent(e)})),n.pageIframe=t().createRef(),n.initialized=!1,n.style=n.getStyle(),n.bindFunctions(),n}return(n=[{key:"componentDidMount",value:function(){this.props.page.loaded&&this.loadContent()}},{key:"componentDidUpdate",value:function(e){var t;this.props.page.loaded&&(e.page!==this.props.page&&this.loadContent(),e.zoom!==this.props.zoom&&this.resetZoom(),e.addAnnotConfig!==this.props.addAnnotConfig&&(null===(t=this.reflow)||void 0===t||t.setAddAnnotConfig(this.props.addAnnotConfig)))}},{key:"componentWillUnmount",value:function(){var e,t,n,o;null===(e=this.getPageDoc())||void 0===e||e.removeEventListener("click",this.handleClickEvent),null===(t=this.pageIframe)||void 0===t||null===(n=t.current)||void 0===n||n.removeEventListener(N,this.resetHeight),null===(o=this.getPageDoc())||void 0===o||o.removeEventListener("mousedown",this.handleMouseDownEvent)}},{key:"render",value:function(){return t().createElement("iframe",{ref:this.pageIframe,id:ne(this.props.index+1),style:this.style,onLoad:this.handleOnLoad})}},{key:"getStyle",value:function(){return{border:"none",width:"100%",height:"500px",backgroundColor:"white",display:"block"}}},{key:"bindFunctions",value:function(){this.resetHeight=_.throttle(this.resetHeight.bind(this),300,{leading:!1})}},{key:"getPageWindow",value:function(){var e,t;return null===(e=this.pageIframe)||void 0===e||null===(t=e.current)||void 0===t?void 0:t.contentWindow}},{key:"getPageDoc",value:function(){var e;return null===(e=this.getPageWindow())||void 0===e?void 0:e.document}},{key:"getPageDocHtml",value:function(){return re(this.getPageWindow())}},{key:"loadContent",value:function(){var e=this.getPageDoc();e.open(),e.write(this.props.page.content),e.close()}},{key:"resetZoom",value:function(){var e=this.getPageDocHtml();if(e){if(window.chrome||oe())e.style.zoom=this.props.zoom;else{var t=(100/this.props.zoom).toFixed(2);e.style.transform="scale(".concat(this.props.zoom,")"),e.style.transformOrigin="0 0",e.style.width="".concat(t,"%"),e.style.overflow="hidden"}this.resetHeight()}}},{key:"resetHeight",value:function(){this.getPageDocHtml()&&(this.pageIframe.current.style.height="1px",this.pageIframe.current.style.height=this.getActualScrollHeight()+"px",this.props.load&&this.props.load())}},{key:"getActualScrollHeight",value:function(){return this.getPageDocHtml()?Math.ceil(this.getPageDocHtml().scrollHeight*this.props.zoom)+1:void 0}},{key:"addCssStyle",value:function(){var e=le(this.getPageWindow());if(e){var t=document.createElement("style");t.type="text/css",t.innerHTML=Dn,e.appendChild(t)}}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(l.prototype,n),l}(t().PureComponent);function qn(e){return(qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Zn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Yn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Zn(Object(n),!0).forEach((function(t){Xn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Xn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Jn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Kn(e,t,n){return(Kn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=eo(e)););return e}(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(n):r.value}})(e,t,n||e)}function $n(e,t){return($n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Qn(e,t){return!t||"object"!==qn(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function eo(e){return(eo=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var to=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&$n(e,t)}(i,e);var t,n,o,r=(n=i,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=eo(n);if(o){var r=eo(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Qn(this,e)});function i(){return Jn(this,i),r.apply(this,arguments)}return(t=[{key:"getStyle",value:function(){return Yn(Yn({},Kn(eo(i.prototype),"getStyle",this).call(this)),{},{minHeight:"100%"})}},{key:"loadContent",value:function(){Kn(eo(i.prototype),"loadContent",this).call(this),this.props.getViewerElement().scrollTop=0}},{key:"resetHeight",value:function(){if(this.getPageDocHtml()){var e=this.props.getViewerElement().scrollTop/this.pageIframe.current.scrollHeight;this.pageIframe.current.style.height="1px",this.pageIframe.current.style.height=this.getActualScrollHeight()+"px",this.props.getViewerElement().scrollTop=e*this.pageIframe.current.scrollHeight}}},{key:"getActualScrollHeight",value:function(){if(this.getPageDocHtml()){var e=Math.ceil(this.getPageDocHtml().scrollHeight*this.props.zoom);return e===this.props.getViewerElement().offsetHeight+1?e-1:e}}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(i.prototype,t),i}(Vn);function no(e){return(no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oo(e,t,n,o,r,i,l){try{var a=e[i](l),s=a.value}catch(e){return void n(e)}a.done?t(s):Promise.resolve(s).then(o,r)}function ro(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function io(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ro(Object(n),!0).forEach((function(t){fo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ro(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function lo(e,t,n){return(lo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=uo(e)););return e}(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(n):r.value}})(e,t,n||e)}function ao(e,t){return(ao=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function so(e,t){return!t||"object"!==no(t)&&"function"!=typeof t?co(e):t}function co(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function uo(e){return(uo=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ho=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ao(e,t)}(l,e);var n,o,r,i=(o=l,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=uo(o);if(r){var n=uo(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return so(this,e)});function l(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),fo(co(n=i.call(this,e)),"handleAddAnnotConfigUpdated",(function(e){n.setState({addAnnotConfig:e.detail})})),n.state=io(io({},n.state),{},{pageNum:n.props.options.pageNum||1}),n.pageContent=t().createRef(),n.handlePageNumberUpdated=_.debounce(n.handlePageNumberUpdated.bind(co(n)),100),n.resize=_.throttle(n.resize.bind(co(n)),100),n.handleZoomUpdated=_.throttle(n.handleZoomUpdated.bind(co(n)),100),n}return(n=[{key:"render",value:function(){var e=this,n=this.state.pages[this.state.pageNum-1];return t().createElement(H,{onResize:this.resize},(function(o){var r=o.measureRef;return t().createElement("div",{id:D,style:{overflowY:"scroll"},ref:r},e.state.showSpinner&&t().createElement("div",{className:"reader-mode-spinner-wrapper",style:e.state.spinnerStyle},t().createElement("div",{className:"reader-mode-spinner"})),e.state.pages.length>0&&n&&t().createElement(to,{ref:e.pageContent,page:n,key:e.state.pageNum-1,index:e.state.pageNum-1,zoom:e.state.zoom,clickLinkHandler:e.handleLinkClicked,getViewerElement:e.getViewerElement,addAnnotConfig:e.state.addAnnotConfig,editStyleHandler:e.props.options.editStyleHandler}))}))}},{key:"resize",value:function(){var e,t,n,o;if(this.initialized){lo(uo(l.prototype),"resize",this).call(this);var r=new CustomEvent(N);null===(e=this.pageContent)||void 0===e||null===(t=e.current)||void 0===t||null===(n=t.pageIframe)||void 0===n||null===(o=n.current)||void 0===o||o.dispatchEvent(r)}}},{key:"jumpToPage",value:function(e){var t=this;if(e+1!==this.state.pageNum&&(this.setState({pageNum:e+1}),this.props.options.pageNumberUpdateHandler(e+1)),!this.state.pages[e].loaded){var n=function(){var n=function(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function l(e){oo(i,o,r,l,a,"next",e)}function a(e){oo(i,o,r,l,a,"throw",e)}l(void 0)}))}}(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,t.loadPageByNum(e);case 2:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}();this.runPdfNetTask(n)}}},{key:"handlePageNumberUpdated",value:function(e){var t=e.detail;t>this.state.pages.length||t===this.state.pageNum||(this.setState({pageNum:t}),this.jumpToPage(t-1))}},{key:"isReflowSupported",value:function(){return!0}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(l.prototype,n),l}(te);function po(e){return(po="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function go(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?mo(Object(n),!0).forEach((function(t){vo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function vo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yo(e,t,n){return(yo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=wo(e)););return e}(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(n):r.value}})(e,t,n||e)}function bo(e,t){return(bo=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function _o(e,t){return!t||"object"!==po(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function wo(e){return(wo=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var So=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&bo(e,t)}(i,e);var t,n,o,r=(n=i,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=wo(n);if(o){var r=wo(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return _o(this,e)});function i(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(t=r.call(this,e)).isResettingHeight=!1,t.isResetHeightNeeded=!1,t}return(t=[{key:"componentDidMount",value:function(){this.loadContent()}},{key:"componentDidUpdate",value:function(e){e.zoom!==this.props.zoom&&this.resetZoom()}},{key:"getStyle",value:function(){return go(go({},yo(wo(i.prototype),"getStyle",this).call(this)),{},{height:"100%"})}},{key:"bindFunctions",value:function(){this.resetHeight=this.resetHeight.bind(this)}},{key:"resetHeight",value:function(){this.isResettingHeight?this.isResetHeightNeeded||(this.isResetHeightNeeded=!0):this._resetHeight()}},{key:"_resetHeight",value:function(){var e=this,t=function(){e.isResetHeightNeeded?e._resetHeight():e.isResettingHeight=!1};this.isResetHeightNeeded=!1;var n=this.pageIframe.current;if(n){this.isResettingHeight=!0,n.style.height="1px";var o=function(){var o=e.getActualScrollHeight();o?(n.style.height="100%",e.props.onResetHeight(e.props.index+1,o,e.props.parent,t)):t()};oe()?setTimeout(o,500):o()}}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(i.prototype,t),i}(Vn);function xo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Co(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function Ro(e,t,n){return t&&Co(e.prototype,t),n&&Co(e,n),e}function Oo(e){return(Oo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function To(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zo(e,t){return!t||"object"!==Oo(t)&&"function"!=typeof t?To(e):t}function ko(e){return(ko=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Po(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&s(e,t)}function Io(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Mo(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function Eo(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!=n?n:null}.bind(this))}function Ao(e,t){try{var n=this.props,o=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,o)}finally{this.props=n,this.state=o}}function Lo(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var n=null,o=null,r=null;if("function"==typeof t.componentWillMount?n="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(n="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?o="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(o="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?r="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(r="UNSAFE_componentWillUpdate"),null!==n||null!==o||null!==r){var i=e.displayName||e.name,l="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+i+" uses "+l+" but also contains the following legacy lifecycles:"+(null!==n?"\n  "+n:"")+(null!==o?"\n  "+o:"")+(null!==r?"\n  "+r:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=Mo,t.componentWillReceiveProps=Eo),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=Ao;var a=t.componentDidUpdate;t.componentDidUpdate=function(e,t,n){var o=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;a.call(this,e,t,o)}}return e}function jo(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=jo(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function Ho(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=jo(e))&&(o&&(o+=" "),o+=t);return o}function Do(e){var t=e.cellCount,n=e.cellSize,o=e.computeMetadataCallback,r=e.computeMetadataCallbackProps,i=e.nextCellsCount,l=e.nextCellSize,a=e.nextScrollToIndex,s=e.scrollToIndex,c=e.updateScrollOffsetForScrollToIndex;t===i&&("number"!=typeof n&&"number"!=typeof l||n===l)||(o(r),s>=0&&s===a&&c())}function No(e,t){if(null==e)return{};var n,o,r=a(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}Mo.__suppressDeprecationWarning=!0,Eo.__suppressDeprecationWarning=!0,Ao.__suppressDeprecationWarning=!0;var Wo=function(){function e(t){var n=t.cellCount,o=t.cellSizeGetter,r=t.estimatedCellSize;xo(this,e),Io(this,"_cellSizeAndPositionData",{}),Io(this,"_lastMeasuredIndex",-1),Io(this,"_lastBatchedIndex",-1),Io(this,"_cellCount",void 0),Io(this,"_cellSizeGetter",void 0),Io(this,"_estimatedCellSize",void 0),this._cellSizeGetter=o,this._cellCount=n,this._estimatedCellSize=r}return Ro(e,[{key:"areOffsetsAdjusted",value:function(){return!1}},{key:"configure",value:function(e){var t=e.cellCount,n=e.estimatedCellSize,o=e.cellSizeGetter;this._cellCount=t,this._estimatedCellSize=n,this._cellSizeGetter=o}},{key:"getCellCount",value:function(){return this._cellCount}},{key:"getEstimatedCellSize",value:function(){return this._estimatedCellSize}},{key:"getLastMeasuredIndex",value:function(){return this._lastMeasuredIndex}},{key:"getOffsetAdjustment",value:function(){return 0}},{key:"getSizeAndPositionOfCell",value:function(e){if(e<0||e>=this._cellCount)throw Error("Requested index ".concat(e," is outside of range 0..").concat(this._cellCount));if(e>this._lastMeasuredIndex)for(var t=this.getSizeAndPositionOfLastMeasuredCell(),n=t.offset+t.size,o=this._lastMeasuredIndex+1;o<=e;o++){var r=this._cellSizeGetter({index:o});if(void 0===r||isNaN(r))throw Error("Invalid size returned for cell ".concat(o," of value ").concat(r));null===r?(this._cellSizeAndPositionData[o]={offset:n,size:0},this._lastBatchedIndex=e):(this._cellSizeAndPositionData[o]={offset:n,size:r},n+=r,this._lastMeasuredIndex=e)}return this._cellSizeAndPositionData[e]}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._lastMeasuredIndex>=0?this._cellSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}}},{key:"getTotalSize",value:function(){var e=this.getSizeAndPositionOfLastMeasuredCell();return e.offset+e.size+(this._cellCount-this._lastMeasuredIndex-1)*this._estimatedCellSize}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,n=void 0===t?"auto":t,o=e.containerSize,r=e.currentOffset,i=e.targetIndex;if(o<=0)return 0;var l,a=this.getSizeAndPositionOfCell(i),s=a.offset,c=s-o+a.size;switch(n){case"start":l=s;break;case"end":l=c;break;case"center":l=s-(o-a.size)/2;break;default:l=Math.max(c,Math.min(s,r))}var u=this.getTotalSize();return Math.max(0,Math.min(u-o,l))}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,n=e.offset;if(0===this.getTotalSize())return{};var o=n+t,r=this._findNearestCell(n),i=this.getSizeAndPositionOfCell(r);n=i.offset+i.size;for(var l=r;n<o&&l<this._cellCount-1;)l++,n+=this.getSizeAndPositionOfCell(l).size;return{start:r,stop:l}}},{key:"resetCell",value:function(e){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,e-1)}},{key:"_binarySearch",value:function(e,t,n){for(;t<=e;){var o=t+Math.floor((e-t)/2),r=this.getSizeAndPositionOfCell(o).offset;if(r===n)return o;r<n?t=o+1:r>n&&(e=o-1)}return t>0?t-1:0}},{key:"_exponentialSearch",value:function(e,t){for(var n=1;e<this._cellCount&&this.getSizeAndPositionOfCell(e).offset<t;)e+=n,n*=2;return this._binarySearch(Math.min(e,this._cellCount-1),Math.floor(e/2),t)}},{key:"_findNearestCell",value:function(e){if(isNaN(e))throw Error("Invalid offset ".concat(e," specified"));e=Math.max(0,e);var t=this.getSizeAndPositionOfLastMeasuredCell(),n=Math.max(0,this._lastMeasuredIndex);return t.offset>=e?this._binarySearch(n,0,e):this._exponentialSearch(n,e)}}]),e}(),Go=function(){function e(t){var n=t.maxScrollSize,o=void 0===n?"undefined"!=typeof window&&window.chrome?16777100:15e5:n,r=No(t,["maxScrollSize"]);xo(this,e),Io(this,"_cellSizeAndPositionManager",void 0),Io(this,"_maxScrollSize",void 0),this._cellSizeAndPositionManager=new Wo(r),this._maxScrollSize=o}return Ro(e,[{key:"areOffsetsAdjusted",value:function(){return this._cellSizeAndPositionManager.getTotalSize()>this._maxScrollSize}},{key:"configure",value:function(e){this._cellSizeAndPositionManager.configure(e)}},{key:"getCellCount",value:function(){return this._cellSizeAndPositionManager.getCellCount()}},{key:"getEstimatedCellSize",value:function(){return this._cellSizeAndPositionManager.getEstimatedCellSize()}},{key:"getLastMeasuredIndex",value:function(){return this._cellSizeAndPositionManager.getLastMeasuredIndex()}},{key:"getOffsetAdjustment",value:function(e){var t=e.containerSize,n=e.offset,o=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize(),i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:r});return Math.round(i*(r-o))}},{key:"getSizeAndPositionOfCell",value:function(e){return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(e)}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell()}},{key:"getTotalSize",value:function(){return Math.min(this._maxScrollSize,this._cellSizeAndPositionManager.getTotalSize())}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,n=void 0===t?"auto":t,o=e.containerSize,r=e.currentOffset,i=e.targetIndex;r=this._safeOffsetToOffset({containerSize:o,offset:r});var l=this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({align:n,containerSize:o,currentOffset:r,targetIndex:i});return this._offsetToSafeOffset({containerSize:o,offset:l})}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,n=e.offset;return n=this._safeOffsetToOffset({containerSize:t,offset:n}),this._cellSizeAndPositionManager.getVisibleCellRange({containerSize:t,offset:n})}},{key:"resetCell",value:function(e){this._cellSizeAndPositionManager.resetCell(e)}},{key:"_getOffsetPercentage",value:function(e){var t=e.containerSize,n=e.offset,o=e.totalSize;return o<=t?0:n/(o-t)}},{key:"_offsetToSafeOffset",value:function(e){var t=e.containerSize,n=e.offset,o=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize();if(o===r)return n;var i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:o});return Math.round(i*(r-t))}},{key:"_safeOffsetToOffset",value:function(e){var t=e.containerSize,n=e.offset,o=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize();if(o===r)return n;var i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:r});return Math.round(i*(o-t))}}]),e}();function Fo(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={};return function(n){var o=n.callback,r=n.indices,i=Object.keys(r),l=!e||i.every((function(e){var t=r[e];return Array.isArray(t)?t.length>0:t>=0})),a=i.length!==Object.keys(t).length||i.some((function(e){var n=t[e],o=r[e];return Array.isArray(o)?n.join(",")!==o.join(","):n!==o}));t=r,l&&a&&o(r)}}function Uo(e){var t=e.cellSize,n=e.cellSizeAndPositionManager,o=e.previousCellsCount,r=e.previousCellSize,i=e.previousScrollToAlignment,l=e.previousScrollToIndex,a=e.previousSize,s=e.scrollOffset,c=e.scrollToAlignment,u=e.scrollToIndex,d=e.size,f=e.sizeJustIncreasedFromZero,h=e.updateScrollIndexCallback,p=n.getCellCount(),m=u>=0&&u<p;m&&(d!==a||f||!r||"number"==typeof t&&t!==r||c!==i||u!==l)?h(u):!m&&p>0&&(d<a||p<o)&&s>n.getTotalSize()-d&&h(p-1)}const Bo=!("undefined"==typeof window||!window.document||!window.document.createElement);var Vo,qo;function Zo(e){if((!Vo&&0!==Vo||e)&&Bo){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),Vo=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return Vo}var Yo,Xo,Jo=(qo="undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).requestAnimationFrame||qo.webkitRequestAnimationFrame||qo.mozRequestAnimationFrame||qo.oRequestAnimationFrame||qo.msRequestAnimationFrame||function(e){return qo.setTimeout(e,1e3/60)},Ko=qo.cancelAnimationFrame||qo.webkitCancelAnimationFrame||qo.mozCancelAnimationFrame||qo.oCancelAnimationFrame||qo.msCancelAnimationFrame||function(e){qo.clearTimeout(e)},$o=Jo,Qo=Ko,er=function(e){return Qo(e.id)},tr=function(e,t){var n;Promise.resolve().then((function(){n=Date.now()}));var o={id:$o((function r(){Date.now()-n>=t?e.call():o.id=$o(r)}))};return o};function nr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function or(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?nr(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nr(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var rr="requested",ir=(Xo=Yo=function(t){function n(e){var t;xo(this,n),Io(To(t=zo(this,ko(n).call(this,e))),"_onGridRenderedMemoizer",Fo()),Io(To(t),"_onScrollMemoizer",Fo(!1)),Io(To(t),"_deferredInvalidateColumnIndex",null),Io(To(t),"_deferredInvalidateRowIndex",null),Io(To(t),"_recomputeScrollLeftFlag",!1),Io(To(t),"_recomputeScrollTopFlag",!1),Io(To(t),"_horizontalScrollBarSize",0),Io(To(t),"_verticalScrollBarSize",0),Io(To(t),"_scrollbarPresenceChanged",!1),Io(To(t),"_scrollingContainer",void 0),Io(To(t),"_childrenToDisplay",void 0),Io(To(t),"_columnStartIndex",void 0),Io(To(t),"_columnStopIndex",void 0),Io(To(t),"_rowStartIndex",void 0),Io(To(t),"_rowStopIndex",void 0),Io(To(t),"_renderedColumnStartIndex",0),Io(To(t),"_renderedColumnStopIndex",0),Io(To(t),"_renderedRowStartIndex",0),Io(To(t),"_renderedRowStopIndex",0),Io(To(t),"_initialScrollTop",void 0),Io(To(t),"_initialScrollLeft",void 0),Io(To(t),"_disablePointerEventsTimeoutId",void 0),Io(To(t),"_styleCache",{}),Io(To(t),"_cellCache",{}),Io(To(t),"_debounceScrollEndedCallback",(function(){t._disablePointerEventsTimeoutId=null,t.setState({isScrolling:!1,needToResetStyleCache:!1})})),Io(To(t),"_invokeOnGridRenderedHelper",(function(){var e=t.props.onSectionRendered;t._onGridRenderedMemoizer({callback:e,indices:{columnOverscanStartIndex:t._columnStartIndex,columnOverscanStopIndex:t._columnStopIndex,columnStartIndex:t._renderedColumnStartIndex,columnStopIndex:t._renderedColumnStopIndex,rowOverscanStartIndex:t._rowStartIndex,rowOverscanStopIndex:t._rowStopIndex,rowStartIndex:t._renderedRowStartIndex,rowStopIndex:t._renderedRowStopIndex}})})),Io(To(t),"_setScrollingContainerRef",(function(e){t._scrollingContainer=e})),Io(To(t),"_onScroll",(function(e){e.target===t._scrollingContainer&&t.handleScrollEvent(e.target)}));var o=new Go({cellCount:e.columnCount,cellSizeGetter:function(t){return n._wrapSizeGetter(e.columnWidth)(t)},estimatedCellSize:n._getEstimatedColumnSize(e)}),r=new Go({cellCount:e.rowCount,cellSizeGetter:function(t){return n._wrapSizeGetter(e.rowHeight)(t)},estimatedCellSize:n._getEstimatedRowSize(e)});return t.state={instanceProps:{columnSizeAndPositionManager:o,rowSizeAndPositionManager:r,prevColumnWidth:e.columnWidth,prevRowHeight:e.rowHeight,prevColumnCount:e.columnCount,prevRowCount:e.rowCount,prevIsScrolling:!0===e.isScrolling,prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow,scrollbarSize:0,scrollbarSizeMeasured:!1},isScrolling:!1,scrollDirectionHorizontal:1,scrollDirectionVertical:1,scrollLeft:0,scrollTop:0,scrollPositionChangeReason:null,needToResetStyleCache:!1},e.scrollToRow>0&&(t._initialScrollTop=t._getCalculatedScrollTop(e,t.state)),e.scrollToColumn>0&&(t._initialScrollLeft=t._getCalculatedScrollLeft(e,t.state)),t}return Po(n,t),Ro(n,[{key:"getOffsetForCell",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.alignment,n=void 0===t?this.props.scrollToAlignment:t,o=e.columnIndex,r=void 0===o?this.props.scrollToColumn:o,i=e.rowIndex,l=void 0===i?this.props.scrollToRow:i,a=or({},this.props,{scrollToAlignment:n,scrollToColumn:r,scrollToRow:l});return{scrollLeft:this._getCalculatedScrollLeft(a),scrollTop:this._getCalculatedScrollTop(a)}}},{key:"getTotalRowsHeight",value:function(){return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize()}},{key:"getTotalColumnsWidth",value:function(){return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize()}},{key:"handleScrollEvent",value:function(e){var t=e.scrollLeft,n=void 0===t?0:t,o=e.scrollTop,r=void 0===o?0:o;if(!(r<0)){this._debounceScrollEnded();var i=this.props,l=i.autoHeight,a=i.autoWidth,s=i.height,c=i.width,u=this.state.instanceProps,d=u.scrollbarSize,f=u.rowSizeAndPositionManager.getTotalSize(),h=u.columnSizeAndPositionManager.getTotalSize(),p=Math.min(Math.max(0,h-c+d),n),m=Math.min(Math.max(0,f-s+d),r);if(this.state.scrollLeft!==p||this.state.scrollTop!==m){var g={isScrolling:!0,scrollDirectionHorizontal:p!==this.state.scrollLeft?p>this.state.scrollLeft?1:-1:this.state.scrollDirectionHorizontal,scrollDirectionVertical:m!==this.state.scrollTop?m>this.state.scrollTop?1:-1:this.state.scrollDirectionVertical,scrollPositionChangeReason:"observed"};l||(g.scrollTop=m),a||(g.scrollLeft=p),g.needToResetStyleCache=!1,this.setState(g)}this._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:m,totalColumnsWidth:h,totalRowsHeight:f})}}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,t):t,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,n):n}},{key:"measureAllCells",value:function(){var e=this.props,t=e.columnCount,n=e.rowCount,o=this.state.instanceProps;o.columnSizeAndPositionManager.getSizeAndPositionOfCell(t-1),o.rowSizeAndPositionManager.getSizeAndPositionOfCell(n-1)}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o,i=this.props,l=i.scrollToColumn,a=i.scrollToRow,s=this.state.instanceProps;s.columnSizeAndPositionManager.resetCell(n),s.rowSizeAndPositionManager.resetCell(r),this._recomputeScrollLeftFlag=l>=0&&(1===this.state.scrollDirectionHorizontal?n<=l:n>=l),this._recomputeScrollTopFlag=a>=0&&(1===this.state.scrollDirectionVertical?r<=a:r>=a),this._styleCache={},this._cellCache={},this.forceUpdate()}},{key:"scrollToCell",value:function(e){var t=e.columnIndex,n=e.rowIndex,o=this.props.columnCount,r=this.props;o>1&&void 0!==t&&this._updateScrollLeftForScrollToColumn(or({},r,{scrollToColumn:t})),void 0!==n&&this._updateScrollTopForScrollToRow(or({},r,{scrollToRow:n}))}},{key:"componentDidMount",value:function(){var e=this.props,t=e.getScrollbarSize,o=e.height,r=e.scrollLeft,i=e.scrollToColumn,l=e.scrollTop,a=e.scrollToRow,s=e.width,c=this.state.instanceProps;if(this._initialScrollTop=0,this._initialScrollLeft=0,this._handleInvalidatedGridSize(),c.scrollbarSizeMeasured||this.setState((function(e){var n=or({},e,{needToResetStyleCache:!1});return n.instanceProps.scrollbarSize=t(),n.instanceProps.scrollbarSizeMeasured=!0,n})),"number"==typeof r&&r>=0||"number"==typeof l&&l>=0){var u=n._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:r,scrollTop:l});u&&(u.needToResetStyleCache=!1,this.setState(u))}this._scrollingContainer&&(this._scrollingContainer.scrollLeft!==this.state.scrollLeft&&(this._scrollingContainer.scrollLeft=this.state.scrollLeft),this._scrollingContainer.scrollTop!==this.state.scrollTop&&(this._scrollingContainer.scrollTop=this.state.scrollTop));var d=o>0&&s>0;i>=0&&d&&this._updateScrollLeftForScrollToColumn(),a>=0&&d&&this._updateScrollTopForScrollToRow(),this._invokeOnGridRenderedHelper(),this._invokeOnScrollMemoizer({scrollLeft:r||0,scrollTop:l||0,totalColumnsWidth:c.columnSizeAndPositionManager.getTotalSize(),totalRowsHeight:c.rowSizeAndPositionManager.getTotalSize()}),this._maybeCallOnScrollbarPresenceChange()}},{key:"componentDidUpdate",value:function(e,t){var n=this,o=this.props,r=o.autoHeight,i=o.autoWidth,l=o.columnCount,a=o.height,s=o.rowCount,c=o.scrollToAlignment,u=o.scrollToColumn,d=o.scrollToRow,f=o.width,h=this.state,p=h.scrollLeft,m=h.scrollPositionChangeReason,g=h.scrollTop,v=h.instanceProps;this._handleInvalidatedGridSize();var y=l>0&&0===e.columnCount||s>0&&0===e.rowCount;m===rr&&(!i&&p>=0&&(p!==this._scrollingContainer.scrollLeft||y)&&(this._scrollingContainer.scrollLeft=p),!r&&g>=0&&(g!==this._scrollingContainer.scrollTop||y)&&(this._scrollingContainer.scrollTop=g));var b=(0===e.width||0===e.height)&&a>0&&f>0;if(this._recomputeScrollLeftFlag?(this._recomputeScrollLeftFlag=!1,this._updateScrollLeftForScrollToColumn(this.props)):Uo({cellSizeAndPositionManager:v.columnSizeAndPositionManager,previousCellsCount:e.columnCount,previousCellSize:e.columnWidth,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToColumn,previousSize:e.width,scrollOffset:p,scrollToAlignment:c,scrollToIndex:u,size:f,sizeJustIncreasedFromZero:b,updateScrollIndexCallback:function(){return n._updateScrollLeftForScrollToColumn(n.props)}}),this._recomputeScrollTopFlag?(this._recomputeScrollTopFlag=!1,this._updateScrollTopForScrollToRow(this.props)):Uo({cellSizeAndPositionManager:v.rowSizeAndPositionManager,previousCellsCount:e.rowCount,previousCellSize:e.rowHeight,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToRow,previousSize:e.height,scrollOffset:g,scrollToAlignment:c,scrollToIndex:d,size:a,sizeJustIncreasedFromZero:b,updateScrollIndexCallback:function(){return n._updateScrollTopForScrollToRow(n.props)}}),this._invokeOnGridRenderedHelper(),p!==t.scrollLeft||g!==t.scrollTop){var _=v.rowSizeAndPositionManager.getTotalSize(),w=v.columnSizeAndPositionManager.getTotalSize();this._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:g,totalColumnsWidth:w,totalRowsHeight:_})}this._maybeCallOnScrollbarPresenceChange()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&er(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var t=this.props,n=t.autoContainerWidth,o=t.autoHeight,r=t.autoWidth,i=t.className,a=t.containerProps,s=t.containerRole,c=t.containerStyle,u=t.height,d=t.id,f=t.noContentRenderer,h=t.role,p=t.style,m=t.tabIndex,g=t.width,v=this.state,y=v.instanceProps,b=v.needToResetStyleCache,_=this._isScrolling(),w={boxSizing:"border-box",direction:"ltr",height:o?"auto":u,position:"relative",width:r?"auto":g,WebkitOverflowScrolling:"touch",willChange:"transform"};b&&(this._styleCache={}),this.state.isScrolling||this._resetStyleCache(),this._calculateChildrenToRender(this.props,this.state);var S=y.columnSizeAndPositionManager.getTotalSize(),x=y.rowSizeAndPositionManager.getTotalSize(),C=x>u?y.scrollbarSize:0,R=S>g?y.scrollbarSize:0;R===this._horizontalScrollBarSize&&C===this._verticalScrollBarSize||(this._horizontalScrollBarSize=R,this._verticalScrollBarSize=C,this._scrollbarPresenceChanged=!0),w.overflowX=S+C<=g?"hidden":"auto",w.overflowY=x+R<=u?"hidden":"auto";var O=this._childrenToDisplay,T=0===O.length&&u>0&&g>0;return e.createElement("div",l({ref:this._setScrollingContainerRef},a,{"aria-label":this.props["aria-label"],"aria-readonly":this.props["aria-readonly"],className:Ho("ReactVirtualized__Grid",i),id:d,onScroll:this._onScroll,role:h,style:or({},w,{},p),tabIndex:m}),O.length>0&&e.createElement("div",{className:"ReactVirtualized__Grid__innerScrollContainer",role:s,style:or({width:n?"auto":S,height:x,maxWidth:S,maxHeight:x,overflow:"hidden",pointerEvents:_?"none":"",position:"relative"},c)},O),T&&f())}},{key:"_calculateChildrenToRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,n=e.cellRenderer,o=e.cellRangeRenderer,r=e.columnCount,i=e.deferredMeasurementCache,l=e.height,a=e.overscanColumnCount,s=e.overscanIndicesGetter,c=e.overscanRowCount,u=e.rowCount,d=e.width,f=e.isScrollingOptOut,h=t.scrollDirectionHorizontal,p=t.scrollDirectionVertical,m=t.instanceProps,g=this._initialScrollTop>0?this._initialScrollTop:t.scrollTop,v=this._initialScrollLeft>0?this._initialScrollLeft:t.scrollLeft,y=this._isScrolling(e,t);if(this._childrenToDisplay=[],l>0&&d>0){var b=m.columnSizeAndPositionManager.getVisibleCellRange({containerSize:d,offset:v}),_=m.rowSizeAndPositionManager.getVisibleCellRange({containerSize:l,offset:g}),w=m.columnSizeAndPositionManager.getOffsetAdjustment({containerSize:d,offset:v}),S=m.rowSizeAndPositionManager.getOffsetAdjustment({containerSize:l,offset:g});this._renderedColumnStartIndex=b.start,this._renderedColumnStopIndex=b.stop,this._renderedRowStartIndex=_.start,this._renderedRowStopIndex=_.stop;var x=s({direction:"horizontal",cellCount:r,overscanCellsCount:a,scrollDirection:h,startIndex:"number"==typeof b.start?b.start:0,stopIndex:"number"==typeof b.stop?b.stop:-1}),C=s({direction:"vertical",cellCount:u,overscanCellsCount:c,scrollDirection:p,startIndex:"number"==typeof _.start?_.start:0,stopIndex:"number"==typeof _.stop?_.stop:-1}),R=x.overscanStartIndex,O=x.overscanStopIndex,T=C.overscanStartIndex,z=C.overscanStopIndex;if(i){if(!i.hasFixedHeight())for(var k=T;k<=z;k++)if(!i.has(k,0)){R=0,O=r-1;break}if(!i.hasFixedWidth())for(var P=R;P<=O;P++)if(!i.has(0,P)){T=0,z=u-1;break}}this._childrenToDisplay=o({cellCache:this._cellCache,cellRenderer:n,columnSizeAndPositionManager:m.columnSizeAndPositionManager,columnStartIndex:R,columnStopIndex:O,deferredMeasurementCache:i,horizontalOffsetAdjustment:w,isScrolling:y,isScrollingOptOut:f,parent:this,rowSizeAndPositionManager:m.rowSizeAndPositionManager,rowStartIndex:T,rowStopIndex:z,scrollLeft:v,scrollTop:g,styleCache:this._styleCache,verticalOffsetAdjustment:S,visibleColumnIndices:b,visibleRowIndices:_}),this._columnStartIndex=R,this._columnStopIndex=O,this._rowStartIndex=T,this._rowStopIndex=z}}},{key:"_debounceScrollEnded",value:function(){var e=this.props.scrollingResetTimeInterval;this._disablePointerEventsTimeoutId&&er(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=tr(this._debounceScrollEndedCallback,e)}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex&&"number"==typeof this._deferredInvalidateRowIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t})}}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,n=e.scrollLeft,o=e.scrollTop,r=e.totalColumnsWidth,i=e.totalRowsHeight;this._onScrollMemoizer({callback:function(e){var n=e.scrollLeft,o=e.scrollTop,l=t.props,a=l.height;(0,l.onScroll)({clientHeight:a,clientWidth:l.width,scrollHeight:i,scrollLeft:n,scrollTop:o,scrollWidth:r})},indices:{scrollLeft:n,scrollTop:o}})}},{key:"_isScrolling",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return Object.hasOwnProperty.call(e,"isScrolling")?Boolean(e.isScrolling):Boolean(t.isScrolling)}},{key:"_maybeCallOnScrollbarPresenceChange",value:function(){if(this._scrollbarPresenceChanged){var e=this.props.onScrollbarPresenceChange;this._scrollbarPresenceChanged=!1,e({horizontal:this._horizontalScrollBarSize>0,size:this.state.instanceProps.scrollbarSize,vertical:this._verticalScrollBarSize>0})}}},{key:"scrollToPosition",value:function(e){var t=e.scrollLeft,o=e.scrollTop,r=n._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:t,scrollTop:o});r&&(r.needToResetStyleCache=!1,this.setState(r))}},{key:"_getCalculatedScrollLeft",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return n._getCalculatedScrollLeft(e,t)}},{key:"_updateScrollLeftForScrollToColumn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,o=n._getScrollLeftForScrollToColumnStateUpdate(e,t);o&&(o.needToResetStyleCache=!1,this.setState(o))}},{key:"_getCalculatedScrollTop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return n._getCalculatedScrollTop(e,t)}},{key:"_resetStyleCache",value:function(){var e=this._styleCache,t=this._cellCache,n=this.props.isScrollingOptOut;this._cellCache={},this._styleCache={};for(var o=this._rowStartIndex;o<=this._rowStopIndex;o++)for(var r=this._columnStartIndex;r<=this._columnStopIndex;r++){var i="".concat(o,"-").concat(r);this._styleCache[i]=e[i],n&&(this._cellCache[i]=t[i])}}},{key:"_updateScrollTopForScrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,o=n._getScrollTopForScrollToRowStateUpdate(e,t);o&&(o.needToResetStyleCache=!1,this.setState(o))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var o={};0===e.columnCount&&0!==t.scrollLeft||0===e.rowCount&&0!==t.scrollTop?(o.scrollLeft=0,o.scrollTop=0):(e.scrollLeft!==t.scrollLeft&&e.scrollToColumn<0||e.scrollTop!==t.scrollTop&&e.scrollToRow<0)&&Object.assign(o,n._getScrollToPositionStateUpdate({prevState:t,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}));var r,i,l=t.instanceProps;return o.needToResetStyleCache=!1,e.columnWidth===l.prevColumnWidth&&e.rowHeight===l.prevRowHeight||(o.needToResetStyleCache=!0),l.columnSizeAndPositionManager.configure({cellCount:e.columnCount,estimatedCellSize:n._getEstimatedColumnSize(e),cellSizeGetter:n._wrapSizeGetter(e.columnWidth)}),l.rowSizeAndPositionManager.configure({cellCount:e.rowCount,estimatedCellSize:n._getEstimatedRowSize(e),cellSizeGetter:n._wrapSizeGetter(e.rowHeight)}),0!==l.prevColumnCount&&0!==l.prevRowCount||(l.prevColumnCount=0,l.prevRowCount=0),e.autoHeight&&!1===e.isScrolling&&!0===l.prevIsScrolling&&Object.assign(o,{isScrolling:!1}),Do({cellCount:l.prevColumnCount,cellSize:"number"==typeof l.prevColumnWidth?l.prevColumnWidth:null,computeMetadataCallback:function(){return l.columnSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.columnCount,nextCellSize:"number"==typeof e.columnWidth?e.columnWidth:null,nextScrollToIndex:e.scrollToColumn,scrollToIndex:l.prevScrollToColumn,updateScrollOffsetForScrollToIndex:function(){r=n._getScrollLeftForScrollToColumnStateUpdate(e,t)}}),Do({cellCount:l.prevRowCount,cellSize:"number"==typeof l.prevRowHeight?l.prevRowHeight:null,computeMetadataCallback:function(){return l.rowSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.rowCount,nextCellSize:"number"==typeof e.rowHeight?e.rowHeight:null,nextScrollToIndex:e.scrollToRow,scrollToIndex:l.prevScrollToRow,updateScrollOffsetForScrollToIndex:function(){i=n._getScrollTopForScrollToRowStateUpdate(e,t)}}),l.prevColumnCount=e.columnCount,l.prevColumnWidth=e.columnWidth,l.prevIsScrolling=!0===e.isScrolling,l.prevRowCount=e.rowCount,l.prevRowHeight=e.rowHeight,l.prevScrollToColumn=e.scrollToColumn,l.prevScrollToRow=e.scrollToRow,l.scrollbarSize=e.getScrollbarSize(),void 0===l.scrollbarSize?(l.scrollbarSizeMeasured=!1,l.scrollbarSize=0):l.scrollbarSizeMeasured=!0,o.instanceProps=l,or({},o,{},r,{},i)}},{key:"_getEstimatedColumnSize",value:function(e){return"number"==typeof e.columnWidth?e.columnWidth:e.estimatedColumnSize}},{key:"_getEstimatedRowSize",value:function(e){return"number"==typeof e.rowHeight?e.rowHeight:e.estimatedRowSize}},{key:"_getScrollToPositionStateUpdate",value:function(e){var t=e.prevState,n=e.scrollLeft,o=e.scrollTop,r={scrollPositionChangeReason:rr};return"number"==typeof n&&n>=0&&(r.scrollDirectionHorizontal=n>t.scrollLeft?1:-1,r.scrollLeft=n),"number"==typeof o&&o>=0&&(r.scrollDirectionVertical=o>t.scrollTop?1:-1,r.scrollTop=o),"number"==typeof n&&n>=0&&n!==t.scrollLeft||"number"==typeof o&&o>=0&&o!==t.scrollTop?r:{}}},{key:"_wrapSizeGetter",value:function(e){return"function"==typeof e?e:function(){return e}}},{key:"_getCalculatedScrollLeft",value:function(e,t){var n=e.columnCount,o=e.height,r=e.scrollToAlignment,i=e.scrollToColumn,l=e.width,a=t.scrollLeft,s=t.instanceProps;if(n>0){var c=n-1,u=i<0?c:Math.min(c,i),d=s.rowSizeAndPositionManager.getTotalSize(),f=s.scrollbarSizeMeasured&&d>o?s.scrollbarSize:0;return s.columnSizeAndPositionManager.getUpdatedOffsetForIndex({align:r,containerSize:l-f,currentOffset:a,targetIndex:u})}return 0}},{key:"_getScrollLeftForScrollToColumnStateUpdate",value:function(e,t){var o=t.scrollLeft,r=n._getCalculatedScrollLeft(e,t);return"number"==typeof r&&r>=0&&o!==r?n._getScrollToPositionStateUpdate({prevState:t,scrollLeft:r,scrollTop:-1}):{}}},{key:"_getCalculatedScrollTop",value:function(e,t){var n=e.height,o=e.rowCount,r=e.scrollToAlignment,i=e.scrollToRow,l=e.width,a=t.scrollTop,s=t.instanceProps;if(o>0){var c=o-1,u=i<0?c:Math.min(c,i),d=s.columnSizeAndPositionManager.getTotalSize(),f=s.scrollbarSizeMeasured&&d>l?s.scrollbarSize:0;return s.rowSizeAndPositionManager.getUpdatedOffsetForIndex({align:r,containerSize:n-f,currentOffset:a,targetIndex:u})}return 0}},{key:"_getScrollTopForScrollToRowStateUpdate",value:function(e,t){var o=t.scrollTop,r=n._getCalculatedScrollTop(e,t);return"number"==typeof r&&r>=0&&o!==r?n._getScrollToPositionStateUpdate({prevState:t,scrollLeft:-1,scrollTop:r}):{}}}]),n}(e.PureComponent),Io(Yo,"propTypes",null),Xo);Io(ir,"defaultProps",{"aria-label":"grid","aria-readonly":!0,autoContainerWidth:!1,autoHeight:!1,autoWidth:!1,cellRangeRenderer:function(e){for(var t=e.cellCache,n=e.cellRenderer,o=e.columnSizeAndPositionManager,r=e.columnStartIndex,i=e.columnStopIndex,l=e.deferredMeasurementCache,a=e.horizontalOffsetAdjustment,s=e.isScrolling,c=e.isScrollingOptOut,u=e.parent,d=e.rowSizeAndPositionManager,f=e.rowStartIndex,h=e.rowStopIndex,p=e.styleCache,m=e.verticalOffsetAdjustment,g=e.visibleColumnIndices,v=e.visibleRowIndices,y=[],b=o.areOffsetsAdjusted()||d.areOffsetsAdjusted(),_=!s&&!b,w=f;w<=h;w++)for(var S=d.getSizeAndPositionOfCell(w),x=r;x<=i;x++){var C=o.getSizeAndPositionOfCell(x),R=x>=g.start&&x<=g.stop&&w>=v.start&&w<=v.stop,O="".concat(w,"-").concat(x),T=void 0;_&&p[O]?T=p[O]:l&&!l.has(w,x)?T={height:"auto",left:0,position:"absolute",top:0,width:"auto"}:(T={height:S.size,left:C.offset+a,position:"absolute",top:S.offset+m,width:C.size},p[O]=T);var z={columnIndex:x,isScrolling:s,isVisible:R,key:O,parent:u,rowIndex:w,style:T},k=void 0;!c&&!s||a||m?k=n(z):(t[O]||(t[O]=n(z)),k=t[O]),null!=k&&!1!==k&&y.push(k)}return y},containerRole:"rowgroup",containerStyle:{},estimatedColumnSize:100,estimatedRowSize:30,getScrollbarSize:Zo,noContentRenderer:function(){return null},onScroll:function(){},onScrollbarPresenceChange:function(){},onSectionRendered:function(){},overscanColumnCount:0,overscanIndicesGetter:function(e){var t=e.cellCount,n=e.overscanCellsCount,o=e.scrollDirection,r=e.startIndex,i=e.stopIndex;return 1===o?{overscanStartIndex:Math.max(0,r),overscanStopIndex:Math.min(t-1,i+n)}:{overscanStartIndex:Math.max(0,r-n),overscanStopIndex:Math.min(t-1,i)}},overscanRowCount:10,role:"grid",scrollingResetTimeInterval:150,scrollToAlignment:"auto",scrollToColumn:-1,scrollToRow:-1,style:{},tabIndex:0,isScrollingOptOut:!1}),Lo(ir);const lr=ir;function ar(e){var t=e.cellCount,n=e.overscanCellsCount,o=e.scrollDirection,r=e.startIndex,i=e.stopIndex;return n=Math.max(1,n),1===o?{overscanStartIndex:Math.max(0,r-1),overscanStopIndex:Math.min(t-1,i+n)}:{overscanStartIndex:Math.max(0,r-n),overscanStopIndex:Math.min(t-1,i+1)}}var sr,cr;function ur(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}var dr,fr,hr=(cr=sr=function(t){function n(){var e,t;xo(this,n);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(t=zo(this,(e=ko(n)).call.apply(e,[this].concat(r)))),"state",{scrollToColumn:0,scrollToRow:0,instanceProps:{prevScrollToColumn:0,prevScrollToRow:0}}),Io(To(t),"_columnStartIndex",0),Io(To(t),"_columnStopIndex",0),Io(To(t),"_rowStartIndex",0),Io(To(t),"_rowStopIndex",0),Io(To(t),"_onKeyDown",(function(e){var n=t.props,o=n.columnCount,r=n.disabled,i=n.mode,l=n.rowCount;if(!r){var a=t._getScrollState(),s=a.scrollToColumn,c=a.scrollToRow,u=t._getScrollState(),d=u.scrollToColumn,f=u.scrollToRow;switch(e.key){case"ArrowDown":f="cells"===i?Math.min(f+1,l-1):Math.min(t._rowStopIndex+1,l-1);break;case"ArrowLeft":d="cells"===i?Math.max(d-1,0):Math.max(t._columnStartIndex-1,0);break;case"ArrowRight":d="cells"===i?Math.min(d+1,o-1):Math.min(t._columnStopIndex+1,o-1);break;case"ArrowUp":f="cells"===i?Math.max(f-1,0):Math.max(t._rowStartIndex-1,0)}d===s&&f===c||(e.preventDefault(),t._updateScrollState({scrollToColumn:d,scrollToRow:f}))}})),Io(To(t),"_onSectionRendered",(function(e){var n=e.columnStartIndex,o=e.columnStopIndex,r=e.rowStartIndex,i=e.rowStopIndex;t._columnStartIndex=n,t._columnStopIndex=o,t._rowStartIndex=r,t._rowStopIndex=i})),t}return Po(n,t),Ro(n,[{key:"setScrollIndexes",value:function(e){var t=e.scrollToColumn,n=e.scrollToRow;this.setState({scrollToRow:n,scrollToColumn:t})}},{key:"render",value:function(){var t=this.props,n=t.className,o=t.children,r=this._getScrollState(),i=r.scrollToColumn,l=r.scrollToRow;return e.createElement("div",{className:n,onKeyDown:this._onKeyDown},o({onSectionRendered:this._onSectionRendered,scrollToColumn:i,scrollToRow:l}))}},{key:"_getScrollState",value:function(){return this.props.isControlled?this.props:this.state}},{key:"_updateScrollState",value:function(e){var t=e.scrollToColumn,n=e.scrollToRow,o=this.props,r=o.isControlled,i=o.onScrollToChange;"function"==typeof i&&i({scrollToColumn:t,scrollToRow:n}),r||this.setState({scrollToColumn:t,scrollToRow:n})}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.isControlled?{}:e.scrollToColumn!==t.instanceProps.prevScrollToColumn||e.scrollToRow!==t.instanceProps.prevScrollToRow?function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ur(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ur(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t,{scrollToColumn:e.scrollToColumn,scrollToRow:e.scrollToRow,instanceProps:{prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow}}):{}}}]),n}(e.PureComponent),Io(sr,"propTypes",null),cr);function pr(e,t){var o,r=void 0!==(o=void 0!==t?t:"undefined"!=typeof window?window:"undefined"!=typeof self?self:n.g).document&&o.document.attachEvent;if(!r){var i=function(){var e=o.requestAnimationFrame||o.mozRequestAnimationFrame||o.webkitRequestAnimationFrame||function(e){return o.setTimeout(e,20)};return function(t){return e(t)}}(),l=function(){var e=o.cancelAnimationFrame||o.mozCancelAnimationFrame||o.webkitCancelAnimationFrame||o.clearTimeout;return function(t){return e(t)}}(),a=function(e){var t=e.__resizeTriggers__,n=t.firstElementChild,o=t.lastElementChild,r=n.firstElementChild;o.scrollLeft=o.scrollWidth,o.scrollTop=o.scrollHeight,r.style.width=n.offsetWidth+1+"px",r.style.height=n.offsetHeight+1+"px",n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight},s=function(e){if(!(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)){var t=this;a(this),this.__resizeRAF__&&l(this.__resizeRAF__),this.__resizeRAF__=i((function(){(function(e){return e.offsetWidth!=e.__resizeLast__.width||e.offsetHeight!=e.__resizeLast__.height})(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(n){n.call(t,e)})))}))}},c=!1,u="",d="animationstart",f="Webkit Moz O ms".split(" "),h="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),p=o.document.createElement("fakeelement");if(void 0!==p.style.animationName&&(c=!0),!1===c)for(var m=0;m<f.length;m++)if(void 0!==p.style[f[m]+"AnimationName"]){u="-"+f[m].toLowerCase()+"-",d=h[m],c=!0;break}var g="resizeanim",v="@"+u+"keyframes "+g+" { from { opacity: 0; } to { opacity: 0; } } ",y=u+"animation: 1ms "+g+"; "}return{addResizeListener:function(t,n){if(r)t.attachEvent("onresize",n);else{if(!t.__resizeTriggers__){var i=t.ownerDocument,l=o.getComputedStyle(t);l&&"static"==l.position&&(t.style.position="relative"),function(t){if(!t.getElementById("detectElementResize")){var n=(v||"")+".resize-triggers { "+(y||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',o=t.head||t.getElementsByTagName("head")[0],r=t.createElement("style");r.id="detectElementResize",r.type="text/css",null!=e&&r.setAttribute("nonce",e),r.styleSheet?r.styleSheet.cssText=n:r.appendChild(t.createTextNode(n)),o.appendChild(r)}}(i),t.__resizeLast__={},t.__resizeListeners__=[],(t.__resizeTriggers__=i.createElement("div")).className="resize-triggers";var c='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>';if(window.trustedTypes){var u=trustedTypes.createPolicy("react-virtualized-auto-sizer",{createHTML:function(){return c}});t.__resizeTriggers__.innerHTML=u.createHTML("")}else t.__resizeTriggers__.innerHTML=c;t.appendChild(t.__resizeTriggers__),a(t),t.addEventListener("scroll",s,!0),d&&(t.__resizeTriggers__.__animationListener__=function(e){e.animationName==g&&a(t)},t.__resizeTriggers__.addEventListener(d,t.__resizeTriggers__.__animationListener__))}t.__resizeListeners__.push(n)}},removeResizeListener:function(e,t){if(r)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",s,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(d,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}function mr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function gr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?mr(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mr(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}Io(hr,"defaultProps",{disabled:!1,isControlled:!1,mode:"edges",scrollToColumn:0,scrollToRow:0}),Lo(hr);var vr=(fr=dr=function(t){function n(){var e,t;xo(this,n);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(t=zo(this,(e=ko(n)).call.apply(e,[this].concat(r)))),"state",{height:t.props.defaultHeight||0,width:t.props.defaultWidth||0}),Io(To(t),"_parentNode",void 0),Io(To(t),"_autoSizer",void 0),Io(To(t),"_window",void 0),Io(To(t),"_detectElementResize",void 0),Io(To(t),"_onResize",(function(){var e=t.props,n=e.disableHeight,o=e.disableWidth,r=e.onResize;if(t._parentNode){var i=t._parentNode.offsetHeight||0,l=t._parentNode.offsetWidth||0,a=(t._window||window).getComputedStyle(t._parentNode)||{},s=parseInt(a.paddingLeft,10)||0,c=parseInt(a.paddingRight,10)||0,u=parseInt(a.paddingTop,10)||0,d=parseInt(a.paddingBottom,10)||0,f=i-u-d,h=l-s-c;(!n&&t.state.height!==f||!o&&t.state.width!==h)&&(t.setState({height:i-u-d,width:l-s-c}),r({height:i,width:l}))}})),Io(To(t),"_setRef",(function(e){t._autoSizer=e})),t}return Po(n,t),Ro(n,[{key:"componentDidMount",value:function(){var e=this.props.nonce;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,this._window=this._autoSizer.parentNode.ownerDocument.defaultView,this._detectElementResize=pr(e,this._window),this._detectElementResize.addResizeListener(this._parentNode,this._onResize),this._onResize())}},{key:"componentWillUnmount",value:function(){this._detectElementResize&&this._parentNode&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize)}},{key:"render",value:function(){var t=this.props,n=t.children,o=t.className,r=t.disableHeight,i=t.disableWidth,l=t.style,a=this.state,s=a.height,c=a.width,u={overflow:"visible"},d={};return r||(u.height=0,d.height=s),i||(u.width=0,d.width=c),e.createElement("div",{className:o,ref:this._setRef,style:gr({},u,{},l)},n(d))}}]),n}(e.Component),Io(dr,"propTypes",null),fr);Io(vr,"defaultProps",{onResize:function(){},disableHeight:!1,disableWidth:!1,style:{}});var yr,br,_r=(br=yr=function(e){function t(){var e,n;xo(this,t);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(n=zo(this,(e=ko(t)).call.apply(e,[this].concat(r)))),"_child",void 0),Io(To(n),"_measure",(function(){var e=n.props,t=e.cache,o=e.columnIndex,r=void 0===o?0:o,i=e.parent,l=e.rowIndex,a=void 0===l?n.props.index||0:l,s=n._getCellMeasurements(),c=s.height,u=s.width;c===t.getHeight(a,r)&&u===t.getWidth(a,r)||(t.set(a,r,u,c),i&&"function"==typeof i.recomputeGridSize&&i.recomputeGridSize({columnIndex:r,rowIndex:a}))})),Io(To(n),"_registerChild",(function(e){!e||e instanceof Element||console.warn("CellMeasurer registerChild expects to be passed Element or null"),n._child=e,e&&n._maybeMeasureCell()})),n}return Po(t,e),Ro(t,[{key:"componentDidMount",value:function(){this._maybeMeasureCell()}},{key:"componentDidUpdate",value:function(){this._maybeMeasureCell()}},{key:"render",value:function(){var e=this.props.children;return"function"==typeof e?e({measure:this._measure,registerChild:this._registerChild}):e}},{key:"_getCellMeasurements",value:function(){var e=this.props.cache,t=this._child||(0,o.findDOMNode)(this);if(t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){var n=t.style.width,r=t.style.height;e.hasFixedWidth()||(t.style.width="auto"),e.hasFixedHeight()||(t.style.height="auto");var i=Math.ceil(t.offsetHeight),l=Math.ceil(t.offsetWidth);return n&&(t.style.width=n),r&&(t.style.height=r),{height:i,width:l}}return{height:0,width:0}}},{key:"_maybeMeasureCell",value:function(){var e=this.props,t=e.cache,n=e.columnIndex,o=void 0===n?0:n,r=e.parent,i=e.rowIndex,l=void 0===i?this.props.index||0:i;if(!t.has(l,o)){var a=this._getCellMeasurements(),s=a.height,c=a.width;t.set(l,o,c,s),r&&"function"==typeof r.invalidateCellSizeAfterRender&&r.invalidateCellSizeAfterRender({columnIndex:o,rowIndex:l})}}}]),t}(e.PureComponent),Io(yr,"propTypes",null),br);Io(_r,"__internalCellMeasurerFlag",!1);var wr=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};xo(this,e),Io(this,"_cellHeightCache",{}),Io(this,"_cellWidthCache",{}),Io(this,"_columnWidthCache",{}),Io(this,"_rowHeightCache",{}),Io(this,"_defaultHeight",void 0),Io(this,"_defaultWidth",void 0),Io(this,"_minHeight",void 0),Io(this,"_minWidth",void 0),Io(this,"_keyMapper",void 0),Io(this,"_hasFixedHeight",void 0),Io(this,"_hasFixedWidth",void 0),Io(this,"_columnCount",0),Io(this,"_rowCount",0),Io(this,"columnWidth",(function(e){var n=e.index,o=t._keyMapper(0,n);return void 0!==t._columnWidthCache[o]?t._columnWidthCache[o]:t._defaultWidth})),Io(this,"rowHeight",(function(e){var n=e.index,o=t._keyMapper(n,0);return void 0!==t._rowHeightCache[o]?t._rowHeightCache[o]:t._defaultHeight}));var o=n.defaultHeight,r=n.defaultWidth,i=n.fixedHeight,l=n.fixedWidth,a=n.keyMapper,s=n.minHeight,c=n.minWidth;this._hasFixedHeight=!0===i,this._hasFixedWidth=!0===l,this._minHeight=s||0,this._minWidth=c||0,this._keyMapper=a||Sr,this._defaultHeight=Math.max(this._minHeight,"number"==typeof o?o:30),this._defaultWidth=Math.max(this._minWidth,"number"==typeof r?r:100)}return Ro(e,[{key:"clear",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this._keyMapper(e,t);delete this._cellHeightCache[n],delete this._cellWidthCache[n],this._updateCachedColumnAndRowSizes(e,t)}},{key:"clearAll",value:function(){this._cellHeightCache={},this._cellWidthCache={},this._columnWidthCache={},this._rowHeightCache={},this._rowCount=0,this._columnCount=0}},{key:"hasFixedHeight",value:function(){return this._hasFixedHeight}},{key:"hasFixedWidth",value:function(){return this._hasFixedWidth}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedHeight)return this._defaultHeight;var n=this._keyMapper(e,t);return void 0!==this._cellHeightCache[n]?Math.max(this._minHeight,this._cellHeightCache[n]):this._defaultHeight}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedWidth)return this._defaultWidth;var n=this._keyMapper(e,t);return void 0!==this._cellWidthCache[n]?Math.max(this._minWidth,this._cellWidthCache[n]):this._defaultWidth}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this._keyMapper(e,t);return void 0!==this._cellHeightCache[n]}},{key:"set",value:function(e,t,n,o){var r=this._keyMapper(e,t);t>=this._columnCount&&(this._columnCount=t+1),e>=this._rowCount&&(this._rowCount=e+1),this._cellHeightCache[r]=o,this._cellWidthCache[r]=n,this._updateCachedColumnAndRowSizes(e,t)}},{key:"_updateCachedColumnAndRowSizes",value:function(e,t){if(!this._hasFixedWidth){for(var n=0,o=0;o<this._rowCount;o++)n=Math.max(n,this.getWidth(o,t));var r=this._keyMapper(0,t);this._columnWidthCache[r]=n}if(!this._hasFixedHeight){for(var i=0,l=0;l<this._columnCount;l++)i=Math.max(i,this.getHeight(e,l));var a=this._keyMapper(e,0);this._rowHeightCache[a]=i}}},{key:"defaultHeight",get:function(){return this._defaultHeight}},{key:"defaultWidth",get:function(){return this._defaultWidth}}]),e}();function Sr(e,t){return"".concat(e,"-").concat(t)}function xr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Cr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xr(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xr(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Rr="observed",Or="requested",Tr=function(t){function n(){var e,t;xo(this,n);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(t=zo(this,(e=ko(n)).call.apply(e,[this].concat(r)))),"state",{isScrolling:!1,scrollLeft:0,scrollTop:0}),Io(To(t),"_calculateSizeAndPositionDataOnNextUpdate",!1),Io(To(t),"_onSectionRenderedMemoizer",Fo()),Io(To(t),"_onScrollMemoizer",Fo(!1)),Io(To(t),"_invokeOnSectionRenderedHelper",(function(){var e=t.props,n=e.cellLayoutManager,o=e.onSectionRendered;t._onSectionRenderedMemoizer({callback:o,indices:{indices:n.getLastRenderedIndices()}})})),Io(To(t),"_setScrollingContainerRef",(function(e){t._scrollingContainer=e})),Io(To(t),"_updateScrollPositionForScrollToCell",(function(){var e=t.props,n=e.cellLayoutManager,o=e.height,r=e.scrollToAlignment,i=e.scrollToCell,l=e.width,a=t.state,s=a.scrollLeft,c=a.scrollTop;if(i>=0){var u=n.getScrollPositionForCell({align:r,cellIndex:i,height:o,scrollLeft:s,scrollTop:c,width:l});u.scrollLeft===s&&u.scrollTop===c||t._setScrollPosition(u)}})),Io(To(t),"_onScroll",(function(e){if(e.target===t._scrollingContainer){t._enablePointerEventsAfterDelay();var n=t.props,o=n.cellLayoutManager,r=n.height,i=n.isScrollingChange,l=n.width,a=t._scrollbarSize,s=o.getTotalSize(),c=s.height,u=s.width,d=Math.max(0,Math.min(u-l+a,e.target.scrollLeft)),f=Math.max(0,Math.min(c-r+a,e.target.scrollTop));if(t.state.scrollLeft!==d||t.state.scrollTop!==f){var h=e.cancelable?Rr:Or;t.state.isScrolling||i(!0),t.setState({isScrolling:!0,scrollLeft:d,scrollPositionChangeReason:h,scrollTop:f})}t._invokeOnScrollMemoizer({scrollLeft:d,scrollTop:f,totalWidth:u,totalHeight:c})}})),t._scrollbarSize=Zo(),void 0===t._scrollbarSize?(t._scrollbarSizeMeasured=!1,t._scrollbarSize=0):t._scrollbarSizeMeasured=!0,t}return Po(n,t),Ro(n,[{key:"recomputeCellSizesAndPositions",value:function(){this._calculateSizeAndPositionDataOnNextUpdate=!0,this.forceUpdate()}},{key:"componentDidMount",value:function(){var e=this.props,t=e.cellLayoutManager,n=e.scrollLeft,o=e.scrollToCell,r=e.scrollTop;this._scrollbarSizeMeasured||(this._scrollbarSize=Zo(),this._scrollbarSizeMeasured=!0,this.setState({})),o>=0?this._updateScrollPositionForScrollToCell():(n>=0||r>=0)&&this._setScrollPosition({scrollLeft:n,scrollTop:r}),this._invokeOnSectionRenderedHelper();var i=t.getTotalSize(),l=i.height,a=i.width;this._invokeOnScrollMemoizer({scrollLeft:n||0,scrollTop:r||0,totalHeight:l,totalWidth:a})}},{key:"componentDidUpdate",value:function(e,t){var n=this.props,o=n.height,r=n.scrollToAlignment,i=n.scrollToCell,l=n.width,a=this.state,s=a.scrollLeft,c=a.scrollPositionChangeReason,u=a.scrollTop;c===Or&&(s>=0&&s!==t.scrollLeft&&s!==this._scrollingContainer.scrollLeft&&(this._scrollingContainer.scrollLeft=s),u>=0&&u!==t.scrollTop&&u!==this._scrollingContainer.scrollTop&&(this._scrollingContainer.scrollTop=u)),o===e.height&&r===e.scrollToAlignment&&i===e.scrollToCell&&l===e.width||this._updateScrollPositionForScrollToCell(),this._invokeOnSectionRenderedHelper()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var t=this.props,n=t.autoHeight,o=t.cellCount,r=t.cellLayoutManager,i=t.className,l=t.height,a=t.horizontalOverscanSize,s=t.id,c=t.noContentRenderer,u=t.style,d=t.verticalOverscanSize,f=t.width,h=this.state,p=h.isScrolling,m=h.scrollLeft,g=h.scrollTop;(this._lastRenderedCellCount!==o||this._lastRenderedCellLayoutManager!==r||this._calculateSizeAndPositionDataOnNextUpdate)&&(this._lastRenderedCellCount=o,this._lastRenderedCellLayoutManager=r,this._calculateSizeAndPositionDataOnNextUpdate=!1,r.calculateSizeAndPositionData());var v=r.getTotalSize(),y=v.height,b=v.width,_=Math.max(0,m-a),w=Math.max(0,g-d),S=Math.min(b,m+f+a),x=Math.min(y,g+l+d),C=l>0&&f>0?r.cellRenderers({height:x-w,isScrolling:p,width:S-_,x:_,y:w}):[],R={boxSizing:"border-box",direction:"ltr",height:n?"auto":l,position:"relative",WebkitOverflowScrolling:"touch",width:f,willChange:"transform"},O=y>l?this._scrollbarSize:0,T=b>f?this._scrollbarSize:0;return R.overflowX=b+O<=f?"hidden":"auto",R.overflowY=y+T<=l?"hidden":"auto",e.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:Ho("ReactVirtualized__Collection",i),id:s,onScroll:this._onScroll,role:"grid",style:Cr({},R,{},u),tabIndex:0},o>0&&e.createElement("div",{className:"ReactVirtualized__Collection__innerScrollContainer",style:{height:y,maxHeight:y,maxWidth:b,overflow:"hidden",pointerEvents:p?"none":"",width:b}},C),0===o&&c())}},{key:"_enablePointerEventsAfterDelay",value:function(){var e=this;this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=setTimeout((function(){(0,e.props.isScrollingChange)(!1),e._disablePointerEventsTimeoutId=null,e.setState({isScrolling:!1})}),150)}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,n=e.scrollLeft,o=e.scrollTop,r=e.totalHeight,i=e.totalWidth;this._onScrollMemoizer({callback:function(e){var n=e.scrollLeft,o=e.scrollTop,l=t.props,a=l.height;(0,l.onScroll)({clientHeight:a,clientWidth:l.width,scrollHeight:r,scrollLeft:n,scrollTop:o,scrollWidth:i})},indices:{scrollLeft:n,scrollTop:o}})}},{key:"_setScrollPosition",value:function(e){var t=e.scrollLeft,n=e.scrollTop,o={scrollPositionChangeReason:Or};t>=0&&(o.scrollLeft=t),n>=0&&(o.scrollTop=n),(t>=0&&t!==this.state.scrollLeft||n>=0&&n!==this.state.scrollTop)&&this.setState(o)}}],[{key:"getDerivedStateFromProps",value:function(e,t){return 0!==e.cellCount||0===t.scrollLeft&&0===t.scrollTop?e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop?e.scrollTop:t.scrollTop,scrollPositionChangeReason:Or}:null:{scrollLeft:0,scrollTop:0,scrollPositionChangeReason:Or}}}]),n}(e.PureComponent);Io(Tr,"defaultProps",{"aria-label":"grid",horizontalOverscanSize:0,noContentRenderer:function(){return null},onScroll:function(){return null},onSectionRendered:function(){return null},scrollToAlignment:"auto",scrollToCell:-1,style:{},verticalOverscanSize:0}),Tr.propTypes={},Lo(Tr);const zr=Tr;var kr=function(){function e(t){var n=t.height,o=t.width,r=t.x,i=t.y;xo(this,e),this.height=n,this.width=o,this.x=r,this.y=i,this._indexMap={},this._indices=[]}return Ro(e,[{key:"addCellIndex",value:function(e){var t=e.index;this._indexMap[t]||(this._indexMap[t]=!0,this._indices.push(t))}},{key:"getCellIndices",value:function(){return this._indices}},{key:"toString",value:function(){return"".concat(this.x,",").concat(this.y," ").concat(this.width,"x").concat(this.height)}}]),e}(),Pr=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;xo(this,e),this._sectionSize=t,this._cellMetadata=[],this._sections={}}return Ro(e,[{key:"getCellIndices",value:function(e){var t=e.height,n=e.width,o=e.x,r=e.y,i={};return this.getSections({height:t,width:n,x:o,y:r}).forEach((function(e){return e.getCellIndices().forEach((function(e){i[e]=e}))})),Object.keys(i).map((function(e){return i[e]}))}},{key:"getCellMetadata",value:function(e){var t=e.index;return this._cellMetadata[t]}},{key:"getSections",value:function(e){for(var t=e.height,n=e.width,o=e.x,r=e.y,i=Math.floor(o/this._sectionSize),l=Math.floor((o+n-1)/this._sectionSize),a=Math.floor(r/this._sectionSize),s=Math.floor((r+t-1)/this._sectionSize),c=[],u=i;u<=l;u++)for(var d=a;d<=s;d++){var f="".concat(u,".").concat(d);this._sections[f]||(this._sections[f]=new kr({height:this._sectionSize,width:this._sectionSize,x:u*this._sectionSize,y:d*this._sectionSize})),c.push(this._sections[f])}return c}},{key:"getTotalSectionCount",value:function(){return Object.keys(this._sections).length}},{key:"toString",value:function(){var e=this;return Object.keys(this._sections).map((function(t){return e._sections[t].toString()}))}},{key:"registerCell",value:function(e){var t=e.cellMetadatum,n=e.index;this._cellMetadata[n]=t,this.getSections(t).forEach((function(e){return e.addCellIndex({index:n})}))}}]),e}();function Ir(e){var t=e.align,n=void 0===t?"auto":t,o=e.cellOffset,r=e.cellSize,i=e.containerSize,l=e.currentOffset,a=o,s=a-i+r;switch(n){case"start":return a;case"end":return s;case"center":return a-(i-r)/2;default:return Math.max(s,Math.min(a,l))}}var Mr=function(t){function n(e,t){var o;return xo(this,n),(o=zo(this,ko(n).call(this,e,t)))._cellMetadata=[],o._lastRenderedCellIndices=[],o._cellCache=[],o._isScrollingChange=o._isScrollingChange.bind(To(o)),o._setCollectionViewRef=o._setCollectionViewRef.bind(To(o)),o}return Po(n,t),Ro(n,[{key:"forceUpdate",value:function(){void 0!==this._collectionView&&this._collectionView.forceUpdate()}},{key:"recomputeCellSizesAndPositions",value:function(){this._cellCache=[],this._collectionView.recomputeCellSizesAndPositions()}},{key:"render",value:function(){var t=l({},this.props);return e.createElement(zr,l({cellLayoutManager:this,isScrollingChange:this._isScrollingChange,ref:this._setCollectionViewRef},t))}},{key:"calculateSizeAndPositionData",value:function(){var e=this.props,t=function(e){for(var t=e.cellCount,n=e.cellSizeAndPositionGetter,o=[],r=new Pr(e.sectionSize),i=0,l=0,a=0;a<t;a++){var s=n({index:a});if(null==s.height||isNaN(s.height)||null==s.width||isNaN(s.width)||null==s.x||isNaN(s.x)||null==s.y||isNaN(s.y))throw Error("Invalid metadata returned for cell ".concat(a,":\n        x:").concat(s.x,", y:").concat(s.y,", width:").concat(s.width,", height:").concat(s.height));i=Math.max(i,s.y+s.height),l=Math.max(l,s.x+s.width),o[a]=s,r.registerCell({cellMetadatum:s,index:a})}return{cellMetadata:o,height:i,sectionManager:r,width:l}}({cellCount:e.cellCount,cellSizeAndPositionGetter:e.cellSizeAndPositionGetter,sectionSize:e.sectionSize});this._cellMetadata=t.cellMetadata,this._sectionManager=t.sectionManager,this._height=t.height,this._width=t.width}},{key:"getLastRenderedIndices",value:function(){return this._lastRenderedCellIndices}},{key:"getScrollPositionForCell",value:function(e){var t=e.align,n=e.cellIndex,o=e.height,r=e.scrollLeft,i=e.scrollTop,l=e.width,a=this.props.cellCount;if(n>=0&&n<a){var s=this._cellMetadata[n];r=Ir({align:t,cellOffset:s.x,cellSize:s.width,containerSize:l,currentOffset:r,targetIndex:n}),i=Ir({align:t,cellOffset:s.y,cellSize:s.height,containerSize:o,currentOffset:i,targetIndex:n})}return{scrollLeft:r,scrollTop:i}}},{key:"getTotalSize",value:function(){return{height:this._height,width:this._width}}},{key:"cellRenderers",value:function(e){var t=this,n=e.height,o=e.isScrolling,r=e.width,i=e.x,l=e.y,a=this.props,s=a.cellGroupRenderer,c=a.cellRenderer;return this._lastRenderedCellIndices=this._sectionManager.getCellIndices({height:n,width:r,x:i,y:l}),s({cellCache:this._cellCache,cellRenderer:c,cellSizeAndPositionGetter:function(e){var n=e.index;return t._sectionManager.getCellMetadata({index:n})},indices:this._lastRenderedCellIndices,isScrolling:o})}},{key:"_isScrollingChange",value:function(e){e||(this._cellCache=[])}},{key:"_setCollectionViewRef",value:function(e){this._collectionView=e}}]),n}(e.PureComponent);function Er(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function Ar(e,t){if(e){if("string"==typeof e)return Er(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Er(e,t):void 0}}Io(Mr,"defaultProps",{"aria-label":"grid",cellGroupRenderer:function(e){var t=e.cellCache,n=e.cellRenderer,o=e.cellSizeAndPositionGetter,r=e.indices,i=e.isScrolling;return r.map((function(e){var r=o({index:e}),l={index:e,isScrolling:i,key:e,style:{height:r.height,left:r.x,position:"absolute",top:r.y,width:r.width}};return i?(e in t||(t[e]=n(l)),t[e]):n(l)})).filter((function(e){return!!e}))}}),Mr.propTypes={},(function(e){function t(e,n){var o;return xo(this,t),(o=zo(this,ko(t).call(this,e,n)))._registerChild=o._registerChild.bind(To(o)),o}return Po(t,e),Ro(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.columnMaxWidth,o=t.columnMinWidth,r=t.columnCount,i=t.width;n===e.columnMaxWidth&&o===e.columnMinWidth&&r===e.columnCount&&i===e.width||this._registeredChild&&this._registeredChild.recomputeGridSize()}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.columnMaxWidth,o=e.columnMinWidth,r=e.columnCount,i=e.width,l=o||1,a=n?Math.min(n,i):i,s=i/r;return s=Math.max(l,s),s=Math.min(a,s),s=Math.floor(s),t({adjustedWidth:Math.min(i,s*r),columnWidth:s,getColumnWidth:function(){return s},registerChild:this._registerChild})}},{key:"_registerChild",value:function(e){if(e&&"function"!=typeof e.recomputeGridSize)throw Error("Unexpected child type registered; only Grid/MultiGrid children are supported.");this._registeredChild=e,this._registeredChild&&this._registeredChild.recomputeGridSize()}}]),t}(e.PureComponent)).propTypes={};var Lr=function(e){function t(e,n){var o;return xo(this,t),(o=zo(this,ko(t).call(this,e,n)))._loadMoreRowsMemoizer=Fo(),o._onRowsRendered=o._onRowsRendered.bind(To(o)),o._registerChild=o._registerChild.bind(To(o)),o}return Po(t,e),Ro(t,[{key:"resetLoadMoreRowsCache",value:function(e){this._loadMoreRowsMemoizer=Fo(),e&&this._doStuff(this._lastRenderedStartIndex,this._lastRenderedStopIndex)}},{key:"render",value:function(){return(0,this.props.children)({onRowsRendered:this._onRowsRendered,registerChild:this._registerChild})}},{key:"_loadUnloadedRanges",value:function(e){var t=this,n=this.props.loadMoreRows;e.forEach((function(e){var o=n(e);o&&o.then((function(){var n;(n={lastRenderedStartIndex:t._lastRenderedStartIndex,lastRenderedStopIndex:t._lastRenderedStopIndex,startIndex:e.startIndex,stopIndex:e.stopIndex}).startIndex>n.lastRenderedStopIndex||n.stopIndex<n.lastRenderedStartIndex||t._registeredChild&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="function"==typeof e.recomputeGridSize?e.recomputeGridSize:e.recomputeRowHeights;n?n.call(e,t):e.forceUpdate()}(t._registeredChild,t._lastRenderedStartIndex)}))}))}},{key:"_onRowsRendered",value:function(e){var t=e.startIndex,n=e.stopIndex;this._lastRenderedStartIndex=t,this._lastRenderedStopIndex=n,this._doStuff(t,n)}},{key:"_doStuff",value:function(e,t){var n,o,r=this,i=this.props,l=i.isRowLoaded,a=i.minimumBatchSize,s=i.rowCount,c=i.threshold,u=function(e){for(var t=e.isRowLoaded,n=e.minimumBatchSize,o=e.rowCount,r=e.stopIndex,i=[],l=null,a=null,s=e.startIndex;s<=r;s++)t({index:s})?null!==a&&(i.push({startIndex:l,stopIndex:a}),l=a=null):(a=s,null===l&&(l=s));if(null!==a){for(var c=Math.min(Math.max(a,l+n-1),o-1),u=a+1;u<=c&&!t({index:u});u++)a=u;i.push({startIndex:l,stopIndex:a})}if(i.length)for(var d=i[0];d.stopIndex-d.startIndex+1<n&&d.startIndex>0;){var f=d.startIndex-1;if(t({index:f}))break;d.startIndex=f}return i}({isRowLoaded:l,minimumBatchSize:a,rowCount:s,startIndex:Math.max(0,e-c),stopIndex:Math.min(s-1,t+c)}),d=(n=[]).concat.apply(n,function(e){if(Array.isArray(e))return Er(e)}(o=u.map((function(e){return[e.startIndex,e.stopIndex]})))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||Ar(o)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());this._loadMoreRowsMemoizer({callback:function(){r._loadUnloadedRanges(u)},indices:{squashedUnloadedRanges:d}})}},{key:"_registerChild",value:function(e){this._registeredChild=e}}]),t}(e.PureComponent);Io(Lr,"defaultProps",{minimumBatchSize:10,rowCount:0,threshold:15}),Lr.propTypes={};var jr,Hr,Dr=(Hr=jr=function(t){function n(){var e,t;xo(this,n);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(t=zo(this,(e=ko(n)).call.apply(e,[this].concat(r)))),"Grid",void 0),Io(To(t),"_cellRenderer",(function(e){var n=e.parent,o=e.rowIndex,r=e.style,i=e.isScrolling,l=e.isVisible,a=e.key,s=t.props.rowRenderer,c=Object.getOwnPropertyDescriptor(r,"width");return c&&c.writable&&(r.width="100%"),s({index:o,style:r,isScrolling:i,isVisible:l,key:a,parent:n})})),Io(To(t),"_setRef",(function(e){t.Grid=e})),Io(To(t),"_onScroll",(function(e){var n=e.clientHeight,o=e.scrollHeight,r=e.scrollTop;(0,t.props.onScroll)({clientHeight:n,scrollHeight:o,scrollTop:r})})),Io(To(t),"_onSectionRendered",(function(e){var n=e.rowOverscanStartIndex,o=e.rowOverscanStopIndex,r=e.rowStartIndex,i=e.rowStopIndex;(0,t.props.onRowsRendered)({overscanStartIndex:n,overscanStopIndex:o,startIndex:r,stopIndex:i})})),t}return Po(n,t),Ro(n,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,n=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:n,columnIndex:0}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o;this.Grid&&this.Grid.recomputeGridSize({rowIndex:r,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e,columnIndex:0})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"render",value:function(){var t=this.props,n=t.className,o=t.noRowsRenderer,r=t.scrollToIndex,i=t.width,a=Ho("ReactVirtualized__List",n);return e.createElement(lr,l({},this.props,{autoContainerWidth:!0,cellRenderer:this._cellRenderer,className:a,columnWidth:i,columnCount:1,noContentRenderer:o,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,scrollToRow:r}))}}]),n}(e.PureComponent),Io(jr,"propTypes",null),Hr);Io(Dr,"defaultProps",{autoHeight:!1,estimatedRowSize:30,onScroll:function(){},noRowsRenderer:function(){return null},onRowsRendered:function(){},overscanIndicesGetter:ar,overscanRowCount:10,scrollToAlignment:"auto",scrollToIndex:-1,style:{}});const Nr=function(e,t,n,o,r){return"function"==typeof n?function(e,t,n,o,r){for(var i=n+1;t<=n;){var l=t+n>>>1;r(e[l],o)>=0?(i=l,n=l-1):t=l+1}return i}(e,void 0===o?0:0|o,void 0===r?e.length-1:0|r,t,n):function(e,t,n,o){for(var r=n+1;t<=n;){var i=t+n>>>1;e[i]>=o?(r=i,n=i-1):t=i+1}return r}(e,void 0===n?0:0|n,void 0===o?e.length-1:0|o,t)};function Wr(e,t,n,o,r){this.mid=e,this.left=t,this.right=n,this.leftPoints=o,this.rightPoints=r,this.count=(t?t.count:0)+(n?n.count:0)+o.length}var Gr=Wr.prototype;function Fr(e,t){e.mid=t.mid,e.left=t.left,e.right=t.right,e.leftPoints=t.leftPoints,e.rightPoints=t.rightPoints,e.count=t.count}function Ur(e,t){var n=$r(t);e.mid=n.mid,e.left=n.left,e.right=n.right,e.leftPoints=n.leftPoints,e.rightPoints=n.rightPoints,e.count=n.count}function Br(e,t){var n=e.intervals([]);n.push(t),Ur(e,n)}function Vr(e,t){var n=e.intervals([]),o=n.indexOf(t);return o<0?0:(n.splice(o,1),Ur(e,n),1)}function qr(e,t,n){for(var o=0;o<e.length&&e[o][0]<=t;++o){var r=n(e[o]);if(r)return r}}function Zr(e,t,n){for(var o=e.length-1;o>=0&&e[o][1]>=t;--o){var r=n(e[o]);if(r)return r}}function Yr(e,t){for(var n=0;n<e.length;++n){var o=t(e[n]);if(o)return o}}function Xr(e,t){return e-t}function Jr(e,t){return e[0]-t[0]||e[1]-t[1]}function Kr(e,t){return e[1]-t[1]||e[0]-t[0]}function $r(e){if(0===e.length)return null;for(var t=[],n=0;n<e.length;++n)t.push(e[n][0],e[n][1]);t.sort(Xr);var o=t[t.length>>1],r=[],i=[],l=[];for(n=0;n<e.length;++n){var a=e[n];a[1]<o?r.push(a):o<a[0]?i.push(a):l.push(a)}var s=l,c=l.slice();return s.sort(Jr),c.sort(Kr),new Wr(o,$r(r),$r(i),s,c)}function Qr(e){this.root=e}Gr.intervals=function(e){return e.push.apply(e,this.leftPoints),this.left&&this.left.intervals(e),this.right&&this.right.intervals(e),e},Gr.insert=function(e){var t=this.count-this.leftPoints.length;if(this.count+=1,e[1]<this.mid)this.left?4*(this.left.count+1)>3*(t+1)?Br(this,e):this.left.insert(e):this.left=$r([e]);else if(e[0]>this.mid)this.right?4*(this.right.count+1)>3*(t+1)?Br(this,e):this.right.insert(e):this.right=$r([e]);else{var n=Nr(this.leftPoints,e,Jr),o=Nr(this.rightPoints,e,Kr);this.leftPoints.splice(n,0,e),this.rightPoints.splice(o,0,e)}},Gr.remove=function(e){var t=this.count-this.leftPoints;if(e[1]<this.mid)return this.left?4*(this.right?this.right.count:0)>3*(t-1)?Vr(this,e):2===(i=this.left.remove(e))?(this.left=null,this.count-=1,1):(1===i&&(this.count-=1),i):0;if(e[0]>this.mid)return this.right?4*(this.left?this.left.count:0)>3*(t-1)?Vr(this,e):2===(i=this.right.remove(e))?(this.right=null,this.count-=1,1):(1===i&&(this.count-=1),i):0;if(1===this.count)return this.leftPoints[0]===e?2:0;if(1===this.leftPoints.length&&this.leftPoints[0]===e){if(this.left&&this.right){for(var n=this,o=this.left;o.right;)n=o,o=o.right;if(n===this)o.right=this.right;else{var r=this.left,i=this.right;n.count-=o.count,n.right=o.left,o.left=r,o.right=i}Fr(this,o),this.count=(this.left?this.left.count:0)+(this.right?this.right.count:0)+this.leftPoints.length}else this.left?Fr(this,this.left):Fr(this,this.right);return 1}for(r=Nr(this.leftPoints,e,Jr);r<this.leftPoints.length&&this.leftPoints[r][0]===e[0];++r)if(this.leftPoints[r]===e)for(this.count-=1,this.leftPoints.splice(r,1),i=Nr(this.rightPoints,e,Kr);i<this.rightPoints.length&&this.rightPoints[i][1]===e[1];++i)if(this.rightPoints[i]===e)return this.rightPoints.splice(i,1),1;return 0},Gr.queryPoint=function(e,t){return e<this.mid?this.left&&(n=this.left.queryPoint(e,t))?n:qr(this.leftPoints,e,t):e>this.mid?this.right&&(n=this.right.queryPoint(e,t))?n:Zr(this.rightPoints,e,t):Yr(this.leftPoints,t);var n},Gr.queryInterval=function(e,t,n){var o;return e<this.mid&&this.left&&(o=this.left.queryInterval(e,t,n))||t>this.mid&&this.right&&(o=this.right.queryInterval(e,t,n))?o:t<this.mid?qr(this.leftPoints,t,n):e>this.mid?Zr(this.rightPoints,e,n):Yr(this.leftPoints,n)};var ei=Qr.prototype;ei.insert=function(e){this.root?this.root.insert(e):this.root=new Wr(e[0],null,null,[e],[e])},ei.remove=function(e){if(this.root){var t=this.root.remove(e);return 2===t&&(this.root=null),0!==t}return!1},ei.queryPoint=function(e,t){if(this.root)return this.root.queryPoint(e,t)},ei.queryInterval=function(e,t,n){if(e<=t&&this.root)return this.root.queryInterval(e,t,n)},Object.defineProperty(ei,"count",{get:function(){return this.root?this.root.count:0}}),Object.defineProperty(ei,"intervals",{get:function(){return this.root?this.root.intervals([]):[]}});var ti,ni,oi=function(){function e(){xo(this,e),Io(this,"_columnSizeMap",{}),Io(this,"_intervalTree",new Qr(null)),Io(this,"_leftMap",{})}return Ro(e,[{key:"estimateTotalHeight",value:function(e,t,n){var o=e-this.count;return this.tallestColumnSize+Math.ceil(o/t)*n}},{key:"range",value:function(e,t,n){var o=this;this._intervalTree.queryInterval(e,e+t,(function(e){var t,r=function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i=[],l=!0,a=!1;try{for(n=n.call(e);!(l=(o=n.next()).done)&&(i.push(o.value),3!==i.length);l=!0);}catch(e){a=!0,r=e}finally{try{l||null==n.return||n.return()}finally{if(a)throw r}}return i}}(t)||Ar(t,3)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=r[0],l=(r[1],r[2]);return n(l,o._leftMap[l],i)}))}},{key:"setPosition",value:function(e,t,n,o){this._intervalTree.insert([n,n+o,e]),this._leftMap[e]=t;var r=this._columnSizeMap,i=r[t];r[t]=void 0===i?n+o:Math.max(i,n+o)}},{key:"count",get:function(){return this._intervalTree.count}},{key:"shortestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var n in e){var o=e[n];t=0===t?o:Math.min(t,o)}return t}},{key:"tallestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var n in e){var o=e[n];t=Math.max(t,o)}return t}}]),e}();function ri(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ii(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ri(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ri(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var li=(ni=ti=function(t){function n(){var e,t;xo(this,n);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(t=zo(this,(e=ko(n)).call.apply(e,[this].concat(r)))),"state",{isScrolling:!1,scrollTop:0}),Io(To(t),"_debounceResetIsScrollingId",void 0),Io(To(t),"_invalidateOnUpdateStartIndex",null),Io(To(t),"_invalidateOnUpdateStopIndex",null),Io(To(t),"_positionCache",new oi),Io(To(t),"_startIndex",null),Io(To(t),"_startIndexMemoized",null),Io(To(t),"_stopIndex",null),Io(To(t),"_stopIndexMemoized",null),Io(To(t),"_debounceResetIsScrollingCallback",(function(){t.setState({isScrolling:!1})})),Io(To(t),"_setScrollingContainerRef",(function(e){t._scrollingContainer=e})),Io(To(t),"_onScroll",(function(e){var n=t.props.height,o=e.currentTarget.scrollTop,r=Math.min(Math.max(0,t._getEstimatedTotalHeight()-n),o);o===r&&(t._debounceResetIsScrolling(),t.state.scrollTop!==r&&t.setState({isScrolling:!0,scrollTop:r}))})),t}return Po(n,t),Ro(n,[{key:"clearCellPositions",value:function(){this._positionCache=new oi,this.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.rowIndex;null===this._invalidateOnUpdateStartIndex?(this._invalidateOnUpdateStartIndex=t,this._invalidateOnUpdateStopIndex=t):(this._invalidateOnUpdateStartIndex=Math.min(this._invalidateOnUpdateStartIndex,t),this._invalidateOnUpdateStopIndex=Math.max(this._invalidateOnUpdateStopIndex,t))}},{key:"recomputeCellPositions",value:function(){var e=this._positionCache.count-1;this._positionCache=new oi,this._populatePositionCache(0,e),this.forceUpdate()}},{key:"componentDidMount",value:function(){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback()}},{key:"componentDidUpdate",value:function(e,t){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback(),this.props.scrollTop!==e.scrollTop&&this._debounceResetIsScrolling()}},{key:"componentWillUnmount",value:function(){this._debounceResetIsScrollingId&&er(this._debounceResetIsScrollingId)}},{key:"render",value:function(){var t,n=this,o=this.props,r=o.autoHeight,i=o.cellCount,l=o.cellMeasurerCache,a=o.cellRenderer,s=o.className,c=o.height,u=o.id,d=o.keyMapper,f=o.overscanByPixels,h=o.role,p=o.style,m=o.tabIndex,g=o.width,v=o.rowDirection,y=this.state,b=y.isScrolling,_=y.scrollTop,w=[],S=this._getEstimatedTotalHeight(),x=this._positionCache.shortestColumnSize,C=this._positionCache.count,R=0;if(this._positionCache.range(Math.max(0,_-f),c+2*f,(function(e,o,r){var i;void 0===t?(R=e,t=e):(R=Math.min(R,e),t=Math.max(t,e)),w.push(a({index:e,isScrolling:b,key:d(e),parent:n,style:(i={height:l.getHeight(e)},Io(i,"ltr"===v?"left":"right",o),Io(i,"position","absolute"),Io(i,"top",r),Io(i,"width",l.getWidth(e)),i)}))})),x<_+c+f&&C<i)for(var O=Math.min(i-C,Math.ceil((_+c+f-x)/l.defaultHeight*g/l.defaultWidth)),T=C;T<C+O;T++)t=T,w.push(a({index:T,isScrolling:b,key:d(T),parent:this,style:{width:l.getWidth(T)}}));return this._startIndex=R,this._stopIndex=t,e.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:Ho("ReactVirtualized__Masonry",s),id:u,onScroll:this._onScroll,role:h,style:ii({boxSizing:"border-box",direction:"ltr",height:r?"auto":c,overflowX:"hidden",overflowY:S<c?"hidden":"auto",position:"relative",width:g,WebkitOverflowScrolling:"touch",willChange:"transform"},p),tabIndex:m},e.createElement("div",{className:"ReactVirtualized__Masonry__innerScrollContainer",style:{width:"100%",height:S,maxWidth:"100%",maxHeight:S,overflow:"hidden",pointerEvents:b?"none":"",position:"relative"}},w))}},{key:"_checkInvalidateOnUpdate",value:function(){if("number"==typeof this._invalidateOnUpdateStartIndex){var e=this._invalidateOnUpdateStartIndex,t=this._invalidateOnUpdateStopIndex;this._invalidateOnUpdateStartIndex=null,this._invalidateOnUpdateStopIndex=null,this._populatePositionCache(e,t),this.forceUpdate()}}},{key:"_debounceResetIsScrolling",value:function(){var e=this.props.scrollingResetTimeInterval;this._debounceResetIsScrollingId&&er(this._debounceResetIsScrollingId),this._debounceResetIsScrollingId=tr(this._debounceResetIsScrollingCallback,e)}},{key:"_getEstimatedTotalHeight",value:function(){var e=this.props,t=e.cellCount,n=e.cellMeasurerCache,o=e.width,r=Math.max(1,Math.floor(o/n.defaultWidth));return this._positionCache.estimateTotalHeight(t,r,n.defaultHeight)}},{key:"_invokeOnScrollCallback",value:function(){var e=this.props,t=e.height,n=e.onScroll,o=this.state.scrollTop;this._onScrollMemoized!==o&&(n({clientHeight:t,scrollHeight:this._getEstimatedTotalHeight(),scrollTop:o}),this._onScrollMemoized=o)}},{key:"_invokeOnCellsRenderedCallback",value:function(){this._startIndexMemoized===this._startIndex&&this._stopIndexMemoized===this._stopIndex||((0,this.props.onCellsRendered)({startIndex:this._startIndex,stopIndex:this._stopIndex}),this._startIndexMemoized=this._startIndex,this._stopIndexMemoized=this._stopIndex)}},{key:"_populatePositionCache",value:function(e,t){for(var n=this.props,o=n.cellMeasurerCache,r=n.cellPositioner,i=e;i<=t;i++){var l=r(i),a=l.left,s=l.top;this._positionCache.setPosition(i,a,s,o.getHeight(i))}}}],[{key:"getDerivedStateFromProps",value:function(e,t){return void 0!==e.scrollTop&&t.scrollTop!==e.scrollTop?{isScrolling:!0,scrollTop:e.scrollTop}:null}}]),n}(e.PureComponent),Io(ti,"propTypes",null),ni);function ai(){}Io(li,"defaultProps",{autoHeight:!1,keyMapper:function(e){return e},onCellsRendered:ai,onScroll:ai,overscanByPixels:20,role:"grid",scrollingResetTimeInterval:150,style:{},tabIndex:0,rowDirection:"ltr"}),Lo(li);var si=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};xo(this,e),Io(this,"_cellMeasurerCache",void 0),Io(this,"_columnIndexOffset",void 0),Io(this,"_rowIndexOffset",void 0),Io(this,"columnWidth",(function(e){var n=e.index;t._cellMeasurerCache.columnWidth({index:n+t._columnIndexOffset})})),Io(this,"rowHeight",(function(e){var n=e.index;t._cellMeasurerCache.rowHeight({index:n+t._rowIndexOffset})}));var o=n.cellMeasurerCache,r=n.columnIndexOffset,i=void 0===r?0:r,l=n.rowIndexOffset,a=void 0===l?0:l;this._cellMeasurerCache=o,this._columnIndexOffset=i,this._rowIndexOffset=a}return Ro(e,[{key:"clear",value:function(e,t){this._cellMeasurerCache.clear(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"clearAll",value:function(){this._cellMeasurerCache.clearAll()}},{key:"hasFixedHeight",value:function(){return this._cellMeasurerCache.hasFixedHeight()}},{key:"hasFixedWidth",value:function(){return this._cellMeasurerCache.hasFixedWidth()}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getHeight(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getWidth(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.has(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"set",value:function(e,t,n,o){this._cellMeasurerCache.set(e+this._rowIndexOffset,t+this._columnIndexOffset,n,o)}},{key:"defaultHeight",get:function(){return this._cellMeasurerCache.defaultHeight}},{key:"defaultWidth",get:function(){return this._cellMeasurerCache.defaultWidth}}]),e}();function ci(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ui(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ci(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ci(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var di=function(t){function n(t,o){var r;xo(this,n),Io(To(r=zo(this,ko(n).call(this,t,o))),"state",{scrollLeft:0,scrollTop:0,scrollbarSize:0,showHorizontalScrollbar:!1,showVerticalScrollbar:!1}),Io(To(r),"_deferredInvalidateColumnIndex",null),Io(To(r),"_deferredInvalidateRowIndex",null),Io(To(r),"_bottomLeftGridRef",(function(e){r._bottomLeftGrid=e})),Io(To(r),"_bottomRightGridRef",(function(e){r._bottomRightGrid=e})),Io(To(r),"_cellRendererBottomLeftGrid",(function(t){var n=t.rowIndex,o=No(t,["rowIndex"]),i=r.props,l=i.cellRenderer,a=i.fixedRowCount;return n===i.rowCount-a?e.createElement("div",{key:o.key,style:ui({},o.style,{height:20})}):l(ui({},o,{parent:To(r),rowIndex:n+a}))})),Io(To(r),"_cellRendererBottomRightGrid",(function(e){var t=e.columnIndex,n=e.rowIndex,o=No(e,["columnIndex","rowIndex"]),i=r.props,l=i.cellRenderer,a=i.fixedColumnCount,s=i.fixedRowCount;return l(ui({},o,{columnIndex:t+a,parent:To(r),rowIndex:n+s}))})),Io(To(r),"_cellRendererTopRightGrid",(function(t){var n=t.columnIndex,o=No(t,["columnIndex"]),i=r.props,l=i.cellRenderer,a=i.columnCount,s=i.fixedColumnCount;return n===a-s?e.createElement("div",{key:o.key,style:ui({},o.style,{width:20})}):l(ui({},o,{columnIndex:n+s,parent:To(r)}))})),Io(To(r),"_columnWidthRightGrid",(function(e){var t=e.index,n=r.props,o=n.columnCount,i=n.fixedColumnCount,l=n.columnWidth,a=r.state,s=a.scrollbarSize;return a.showHorizontalScrollbar&&t===o-i?s:"function"==typeof l?l({index:t+i}):l})),Io(To(r),"_onScroll",(function(e){var t=e.scrollLeft,n=e.scrollTop;r.setState({scrollLeft:t,scrollTop:n});var o=r.props.onScroll;o&&o(e)})),Io(To(r),"_onScrollbarPresenceChange",(function(e){var t=e.horizontal,n=e.size,o=e.vertical,i=r.state,l=i.showHorizontalScrollbar,a=i.showVerticalScrollbar;if(t!==l||o!==a){r.setState({scrollbarSize:n,showHorizontalScrollbar:t,showVerticalScrollbar:o});var s=r.props.onScrollbarPresenceChange;"function"==typeof s&&s({horizontal:t,size:n,vertical:o})}})),Io(To(r),"_onScrollLeft",(function(e){var t=e.scrollLeft;r._onScroll({scrollLeft:t,scrollTop:r.state.scrollTop})})),Io(To(r),"_onScrollTop",(function(e){var t=e.scrollTop;r._onScroll({scrollTop:t,scrollLeft:r.state.scrollLeft})})),Io(To(r),"_rowHeightBottomGrid",(function(e){var t=e.index,n=r.props,o=n.fixedRowCount,i=n.rowCount,l=n.rowHeight,a=r.state,s=a.scrollbarSize;return a.showVerticalScrollbar&&t===i-o?s:"function"==typeof l?l({index:t+o}):l})),Io(To(r),"_topLeftGridRef",(function(e){r._topLeftGrid=e})),Io(To(r),"_topRightGridRef",(function(e){r._topRightGrid=e}));var i=t.deferredMeasurementCache,l=t.fixedColumnCount,a=t.fixedRowCount;return r._maybeCalculateCachedStyles(!0),i&&(r._deferredMeasurementCacheBottomLeftGrid=a>0?new si({cellMeasurerCache:i,columnIndexOffset:0,rowIndexOffset:a}):i,r._deferredMeasurementCacheBottomRightGrid=l>0||a>0?new si({cellMeasurerCache:i,columnIndexOffset:l,rowIndexOffset:a}):i,r._deferredMeasurementCacheTopRightGrid=l>0?new si({cellMeasurerCache:i,columnIndexOffset:l,rowIndexOffset:0}):i),r}return Po(n,t),Ro(n,[{key:"forceUpdateGrids",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.forceUpdate(),this._bottomRightGrid&&this._bottomRightGrid.forceUpdate(),this._topLeftGrid&&this._topLeftGrid.forceUpdate(),this._topRightGrid&&this._topRightGrid.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,n):n,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,r):r}},{key:"measureAllCells",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.measureAllCells(),this._bottomRightGrid&&this._bottomRightGrid.measureAllCells(),this._topLeftGrid&&this._topLeftGrid.measureAllCells(),this._topRightGrid&&this._topRightGrid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o,i=this.props,l=i.fixedColumnCount,a=i.fixedRowCount,s=Math.max(0,n-l),c=Math.max(0,r-a);this._bottomLeftGrid&&this._bottomLeftGrid.recomputeGridSize({columnIndex:n,rowIndex:c}),this._bottomRightGrid&&this._bottomRightGrid.recomputeGridSize({columnIndex:s,rowIndex:c}),this._topLeftGrid&&this._topLeftGrid.recomputeGridSize({columnIndex:n,rowIndex:r}),this._topRightGrid&&this._topRightGrid.recomputeGridSize({columnIndex:s,rowIndex:r}),this._leftGridWidth=null,this._topGridHeight=null,this._maybeCalculateCachedStyles(!0)}},{key:"componentDidMount",value:function(){var e=this.props,t=e.scrollLeft,n=e.scrollTop;if(t>0||n>0){var o={};t>0&&(o.scrollLeft=t),n>0&&(o.scrollTop=n),this.setState(o)}this._handleInvalidatedGridSize()}},{key:"componentDidUpdate",value:function(){this._handleInvalidatedGridSize()}},{key:"render",value:function(){var t=this.props,n=t.onScroll,o=t.onSectionRendered,r=(t.onScrollbarPresenceChange,t.scrollLeft,t.scrollToColumn),i=(t.scrollTop,t.scrollToRow),l=No(t,["onScroll","onSectionRendered","onScrollbarPresenceChange","scrollLeft","scrollToColumn","scrollTop","scrollToRow"]);if(this._prepareForRender(),0===this.props.width||0===this.props.height)return null;var a=this.state,s=a.scrollLeft,c=a.scrollTop;return e.createElement("div",{style:this._containerOuterStyle},e.createElement("div",{style:this._containerTopStyle},this._renderTopLeftGrid(l),this._renderTopRightGrid(ui({},l,{onScroll:n,scrollLeft:s}))),e.createElement("div",{style:this._containerBottomStyle},this._renderBottomLeftGrid(ui({},l,{onScroll:n,scrollTop:c})),this._renderBottomRightGrid(ui({},l,{onScroll:n,onSectionRendered:o,scrollLeft:s,scrollToColumn:r,scrollToRow:i,scrollTop:c}))))}},{key:"_getBottomGridHeight",value:function(e){return e.height-this._getTopGridHeight(e)}},{key:"_getLeftGridWidth",value:function(e){var t=e.fixedColumnCount,n=e.columnWidth;if(null==this._leftGridWidth)if("function"==typeof n){for(var o=0,r=0;r<t;r++)o+=n({index:r});this._leftGridWidth=o}else this._leftGridWidth=n*t;return this._leftGridWidth}},{key:"_getRightGridWidth",value:function(e){return e.width-this._getLeftGridWidth(e)}},{key:"_getTopGridHeight",value:function(e){var t=e.fixedRowCount,n=e.rowHeight;if(null==this._topGridHeight)if("function"==typeof n){for(var o=0,r=0;r<t;r++)o+=n({index:r});this._topGridHeight=o}else this._topGridHeight=n*t;return this._topGridHeight}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t}),this.forceUpdate()}}},{key:"_maybeCalculateCachedStyles",value:function(e){var t=this.props,n=t.columnWidth,o=t.enableFixedColumnScroll,r=t.enableFixedRowScroll,i=t.height,l=t.fixedColumnCount,a=t.fixedRowCount,s=t.rowHeight,c=t.style,u=t.styleBottomLeftGrid,d=t.styleBottomRightGrid,f=t.styleTopLeftGrid,h=t.styleTopRightGrid,p=t.width,m=e||i!==this._lastRenderedHeight||p!==this._lastRenderedWidth,g=e||n!==this._lastRenderedColumnWidth||l!==this._lastRenderedFixedColumnCount,v=e||a!==this._lastRenderedFixedRowCount||s!==this._lastRenderedRowHeight;(e||m||c!==this._lastRenderedStyle)&&(this._containerOuterStyle=ui({height:i,overflow:"visible",width:p},c)),(e||m||v)&&(this._containerTopStyle={height:this._getTopGridHeight(this.props),position:"relative",width:p},this._containerBottomStyle={height:i-this._getTopGridHeight(this.props),overflow:"visible",position:"relative",width:p}),(e||u!==this._lastRenderedStyleBottomLeftGrid)&&(this._bottomLeftGridStyle=ui({left:0,overflowX:"hidden",overflowY:o?"auto":"hidden",position:"absolute"},u)),(e||g||d!==this._lastRenderedStyleBottomRightGrid)&&(this._bottomRightGridStyle=ui({left:this._getLeftGridWidth(this.props),position:"absolute"},d)),(e||f!==this._lastRenderedStyleTopLeftGrid)&&(this._topLeftGridStyle=ui({left:0,overflowX:"hidden",overflowY:"hidden",position:"absolute",top:0},f)),(e||g||h!==this._lastRenderedStyleTopRightGrid)&&(this._topRightGridStyle=ui({left:this._getLeftGridWidth(this.props),overflowX:r?"auto":"hidden",overflowY:"hidden",position:"absolute",top:0},h)),this._lastRenderedColumnWidth=n,this._lastRenderedFixedColumnCount=l,this._lastRenderedFixedRowCount=a,this._lastRenderedHeight=i,this._lastRenderedRowHeight=s,this._lastRenderedStyle=c,this._lastRenderedStyleBottomLeftGrid=u,this._lastRenderedStyleBottomRightGrid=d,this._lastRenderedStyleTopLeftGrid=f,this._lastRenderedStyleTopRightGrid=h,this._lastRenderedWidth=p}},{key:"_prepareForRender",value:function(){this._lastRenderedColumnWidth===this.props.columnWidth&&this._lastRenderedFixedColumnCount===this.props.fixedColumnCount||(this._leftGridWidth=null),this._lastRenderedFixedRowCount===this.props.fixedRowCount&&this._lastRenderedRowHeight===this.props.rowHeight||(this._topGridHeight=null),this._maybeCalculateCachedStyles(),this._lastRenderedColumnWidth=this.props.columnWidth,this._lastRenderedFixedColumnCount=this.props.fixedColumnCount,this._lastRenderedFixedRowCount=this.props.fixedRowCount,this._lastRenderedRowHeight=this.props.rowHeight}},{key:"_renderBottomLeftGrid",value:function(t){var n=t.enableFixedColumnScroll,o=t.fixedColumnCount,r=t.fixedRowCount,i=t.rowCount,a=t.hideBottomLeftGridScrollbar,s=this.state.showVerticalScrollbar;if(!o)return null;var c=s?1:0,u=this._getBottomGridHeight(t),d=this._getLeftGridWidth(t),f=this.state.showVerticalScrollbar?this.state.scrollbarSize:0,h=a?d+f:d,p=e.createElement(lr,l({},t,{cellRenderer:this._cellRendererBottomLeftGrid,className:this.props.classNameBottomLeftGrid,columnCount:o,deferredMeasurementCache:this._deferredMeasurementCacheBottomLeftGrid,height:u,onScroll:n?this._onScrollTop:void 0,ref:this._bottomLeftGridRef,rowCount:Math.max(0,i-r)+c,rowHeight:this._rowHeightBottomGrid,style:this._bottomLeftGridStyle,tabIndex:null,width:h}));return a?e.createElement("div",{className:"BottomLeftGrid_ScrollWrapper",style:ui({},this._bottomLeftGridStyle,{height:u,width:d,overflowY:"hidden"})},p):p}},{key:"_renderBottomRightGrid",value:function(t){var n=t.columnCount,o=t.fixedColumnCount,r=t.fixedRowCount,i=t.rowCount,a=t.scrollToColumn,s=t.scrollToRow;return e.createElement(lr,l({},t,{cellRenderer:this._cellRendererBottomRightGrid,className:this.props.classNameBottomRightGrid,columnCount:Math.max(0,n-o),columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheBottomRightGrid,height:this._getBottomGridHeight(t),onScroll:this._onScroll,onScrollbarPresenceChange:this._onScrollbarPresenceChange,ref:this._bottomRightGridRef,rowCount:Math.max(0,i-r),rowHeight:this._rowHeightBottomGrid,scrollToColumn:a-o,scrollToRow:s-r,style:this._bottomRightGridStyle,width:this._getRightGridWidth(t)}))}},{key:"_renderTopLeftGrid",value:function(t){var n=t.fixedColumnCount,o=t.fixedRowCount;return n&&o?e.createElement(lr,l({},t,{className:this.props.classNameTopLeftGrid,columnCount:n,height:this._getTopGridHeight(t),ref:this._topLeftGridRef,rowCount:o,style:this._topLeftGridStyle,tabIndex:null,width:this._getLeftGridWidth(t)})):null}},{key:"_renderTopRightGrid",value:function(t){var n=t.columnCount,o=t.enableFixedRowScroll,r=t.fixedColumnCount,i=t.fixedRowCount,a=t.scrollLeft,s=t.hideTopRightGridScrollbar,c=this.state,u=c.showHorizontalScrollbar,d=c.scrollbarSize;if(!i)return null;var f=u?1:0,h=this._getTopGridHeight(t),p=this._getRightGridWidth(t),m=u?d:0,g=h,v=this._topRightGridStyle;s&&(g=h+m,v=ui({},this._topRightGridStyle,{left:0}));var y=e.createElement(lr,l({},t,{cellRenderer:this._cellRendererTopRightGrid,className:this.props.classNameTopRightGrid,columnCount:Math.max(0,n-r)+f,columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheTopRightGrid,height:g,onScroll:o?this._onScrollLeft:void 0,ref:this._topRightGridRef,rowCount:i,scrollLeft:a,style:v,tabIndex:null,width:p}));return s?e.createElement("div",{className:"TopRightGrid_ScrollWrapper",style:ui({},this._topRightGridStyle,{height:h,width:p,overflowX:"hidden"})},y):y}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft&&e.scrollLeft>=0?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop&&e.scrollTop>=0?e.scrollTop:t.scrollTop}:null}}]),n}(e.PureComponent);function fi(t){var n=t.className,o=t.columns,r=t.style;return e.createElement("div",{className:n,role:"row",style:r},o)}Io(di,"defaultProps",{classNameBottomLeftGrid:"",classNameBottomRightGrid:"",classNameTopLeftGrid:"",classNameTopRightGrid:"",enableFixedColumnScroll:!1,enableFixedRowScroll:!1,fixedColumnCount:0,fixedRowCount:0,scrollToColumn:-1,scrollToRow:-1,style:{},styleBottomLeftGrid:{},styleBottomRightGrid:{},styleTopLeftGrid:{},styleTopRightGrid:{},hideTopRightGridScrollbar:!1,hideBottomLeftGridScrollbar:!1}),di.propTypes={},Lo(di),(function(e){function t(e,n){var o;return xo(this,t),(o=zo(this,ko(t).call(this,e,n))).state={clientHeight:0,clientWidth:0,scrollHeight:0,scrollLeft:0,scrollTop:0,scrollWidth:0},o._onScroll=o._onScroll.bind(To(o)),o}return Po(t,e),Ro(t,[{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.clientHeight,o=t.clientWidth,r=t.scrollHeight,i=t.scrollLeft,l=t.scrollTop,a=t.scrollWidth;return e({clientHeight:n,clientWidth:o,onScroll:this._onScroll,scrollHeight:r,scrollLeft:i,scrollTop:l,scrollWidth:a})}},{key:"_onScroll",value:function(e){var t=e.clientHeight,n=e.clientWidth,o=e.scrollHeight,r=e.scrollLeft,i=e.scrollTop,l=e.scrollWidth;this.setState({clientHeight:t,clientWidth:n,scrollHeight:o,scrollLeft:r,scrollTop:i,scrollWidth:l})}}]),t}(e.PureComponent)).propTypes={},fi.propTypes=null;const hi="ASC",pi="DESC";function mi(t){var n=t.sortDirection,o=Ho("ReactVirtualized__Table__sortableHeaderIcon",{"ReactVirtualized__Table__sortableHeaderIcon--ASC":n===hi,"ReactVirtualized__Table__sortableHeaderIcon--DESC":n===pi});return e.createElement("svg",{className:o,width:18,height:18,viewBox:"0 0 24 24"},n===hi?e.createElement("path",{d:"M7 14l5-5 5 5z"}):e.createElement("path",{d:"M7 10l5 5 5-5z"}),e.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))}function gi(t){var n=t.dataKey,o=t.label,r=t.sortBy,i=t.sortDirection,l=r===n,a=[e.createElement("span",{className:"ReactVirtualized__Table__headerTruncatedText",key:"label",title:"string"==typeof o?o:null},o)];return l&&a.push(e.createElement(mi,{key:"SortIndicator",sortDirection:i})),a}function vi(t){var n=t.className,o=t.columns,r=t.index,i=t.key,a=t.onRowClick,s=t.onRowDoubleClick,c=t.onRowMouseOut,u=t.onRowMouseOver,d=t.onRowRightClick,f=t.rowData,h=t.style,p={"aria-rowindex":r+1};return(a||s||c||u||d)&&(p["aria-label"]="row",p.tabIndex=0,a&&(p.onClick=function(e){return a({event:e,index:r,rowData:f})}),s&&(p.onDoubleClick=function(e){return s({event:e,index:r,rowData:f})}),c&&(p.onMouseOut=function(e){return c({event:e,index:r,rowData:f})}),u&&(p.onMouseOver=function(e){return u({event:e,index:r,rowData:f})}),d&&(p.onContextMenu=function(e){return d({event:e,index:r,rowData:f})})),e.createElement("div",l({},p,{className:n,key:i,role:"row",style:h}),o)}mi.propTypes={},gi.propTypes=null,vi.propTypes=null;var yi=function(e){function t(){return xo(this,t),zo(this,ko(t).apply(this,arguments))}return Po(t,e),t}(e.Component);function bi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bi(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bi(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}Io(yi,"defaultProps",{cellDataGetter:function(e){var t=e.dataKey,n=e.rowData;return"function"==typeof n.get?n.get(t):n[t]},cellRenderer:function(e){var t=e.cellData;return null==t?"":String(t)},defaultSortDirection:hi,flexGrow:0,flexShrink:1,headerRenderer:gi,style:{}}),yi.propTypes={};var wi=function(t){function n(e){var t;return xo(this,n),(t=zo(this,ko(n).call(this,e))).state={scrollbarWidth:0},t._createColumn=t._createColumn.bind(To(t)),t._createRow=t._createRow.bind(To(t)),t._onScroll=t._onScroll.bind(To(t)),t._onSectionRendered=t._onSectionRendered.bind(To(t)),t._setRef=t._setRef.bind(To(t)),t}return Po(n,t),Ro(n,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,n=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:n}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o;this.Grid&&this.Grid.recomputeGridSize({rowIndex:r,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"getScrollbarWidth",value:function(){if(this.Grid){var e=(0,o.findDOMNode)(this.Grid),t=e.clientWidth||0;return(e.offsetWidth||0)-t}return 0}},{key:"componentDidMount",value:function(){this._setScrollbarWidth()}},{key:"componentDidUpdate",value:function(){this._setScrollbarWidth()}},{key:"render",value:function(){var t=this,n=this.props,o=n.children,r=n.className,i=n.disableHeader,a=n.gridClassName,s=n.gridStyle,c=n.headerHeight,u=n.headerRowRenderer,d=n.height,f=n.id,h=n.noRowsRenderer,p=n.rowClassName,m=n.rowStyle,g=n.scrollToIndex,v=n.style,y=n.width,b=this.state.scrollbarWidth,_=i?d:d-c,w="function"==typeof p?p({index:-1}):p,S="function"==typeof m?m({index:-1}):m;return this._cachedColumnStyles=[],e.Children.toArray(o).forEach((function(e,n){var o=t._getFlexStyleForColumn(e,e.props.style);t._cachedColumnStyles[n]=_i({overflow:"hidden"},o)})),e.createElement("div",{"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-colcount":e.Children.toArray(o).length,"aria-rowcount":this.props.rowCount,className:Ho("ReactVirtualized__Table",r),id:f,role:"grid",style:v},!i&&u({className:Ho("ReactVirtualized__Table__headerRow",w),columns:this._getHeaderColumns(),style:_i({height:c,overflow:"hidden",paddingRight:b,width:y},S)}),e.createElement(lr,l({},this.props,{"aria-readonly":null,autoContainerWidth:!0,className:Ho("ReactVirtualized__Table__Grid",a),cellRenderer:this._createRow,columnWidth:y,columnCount:1,height:_,id:void 0,noContentRenderer:h,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,role:"rowgroup",scrollbarWidth:b,scrollToRow:g,style:_i({},s,{overflowX:"hidden"})})))}},{key:"_createColumn",value:function(t){var n=t.column,o=t.columnIndex,r=t.isScrolling,i=t.parent,l=t.rowData,a=t.rowIndex,s=this.props.onColumnClick,c=n.props,u=c.cellDataGetter,d=c.cellRenderer,f=c.className,h=c.columnData,p=c.dataKey,m=c.id,g=d({cellData:u({columnData:h,dataKey:p,rowData:l}),columnData:h,columnIndex:o,dataKey:p,isScrolling:r,parent:i,rowData:l,rowIndex:a}),v=this._cachedColumnStyles[o],y="string"==typeof g?g:null;return e.createElement("div",{"aria-colindex":o+1,"aria-describedby":m,className:Ho("ReactVirtualized__Table__rowColumn",f),key:"Row"+a+"-Col"+o,onClick:function(e){s&&s({columnData:h,dataKey:p,event:e})},role:"gridcell",style:v,title:y},g)}},{key:"_createHeader",value:function(t){var n,o,r,i,l,a=t.column,s=t.index,c=this.props,u=c.headerClassName,d=c.headerStyle,f=c.onHeaderClick,h=c.sort,p=c.sortBy,m=c.sortDirection,g=a.props,v=g.columnData,y=g.dataKey,b=g.defaultSortDirection,_=g.disableSort,w=g.headerRenderer,S=g.id,x=g.label,C=!_&&h,R=Ho("ReactVirtualized__Table__headerColumn",u,a.props.headerClassName,{ReactVirtualized__Table__sortableHeaderColumn:C}),O=this._getFlexStyleForColumn(a,_i({},d,{},a.props.headerStyle)),T=w({columnData:v,dataKey:y,disableSort:_,label:x,sortBy:p,sortDirection:m});if(C||f){var z=p!==y?b:m===pi?hi:pi,k=function(e){C&&h({defaultSortDirection:b,event:e,sortBy:y,sortDirection:z}),f&&f({columnData:v,dataKey:y,event:e})};l=a.props["aria-label"]||x||y,i="none",r=0,n=k,o=function(e){"Enter"!==e.key&&" "!==e.key||k(e)}}return p===y&&(i=m===hi?"ascending":"descending"),e.createElement("div",{"aria-label":l,"aria-sort":i,className:R,id:S,key:"Header-Col"+s,onClick:n,onKeyDown:o,role:"columnheader",style:O,tabIndex:r},T)}},{key:"_createRow",value:function(t){var n=this,o=t.rowIndex,r=t.isScrolling,i=t.key,l=t.parent,a=t.style,s=this.props,c=s.children,u=s.onRowClick,d=s.onRowDoubleClick,f=s.onRowRightClick,h=s.onRowMouseOver,p=s.onRowMouseOut,m=s.rowClassName,g=s.rowGetter,v=s.rowRenderer,y=s.rowStyle,b=this.state.scrollbarWidth,_="function"==typeof m?m({index:o}):m,w="function"==typeof y?y({index:o}):y,S=g({index:o}),x=e.Children.toArray(c).map((function(e,t){return n._createColumn({column:e,columnIndex:t,isScrolling:r,parent:l,rowData:S,rowIndex:o,scrollbarWidth:b})})),C=Ho("ReactVirtualized__Table__row",_),R=_i({},a,{height:this._getRowHeight(o),overflow:"hidden",paddingRight:b},w);return v({className:C,columns:x,index:o,isScrolling:r,key:i,onRowClick:u,onRowDoubleClick:d,onRowRightClick:f,onRowMouseOver:h,onRowMouseOut:p,rowData:S,style:R})}},{key:"_getFlexStyleForColumn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="".concat(e.props.flexGrow," ").concat(e.props.flexShrink," ").concat(e.props.width,"px"),o=_i({},t,{flex:n,msFlex:n,WebkitFlex:n});return e.props.maxWidth&&(o.maxWidth=e.props.maxWidth),e.props.minWidth&&(o.minWidth=e.props.minWidth),o}},{key:"_getHeaderColumns",value:function(){var t=this,n=this.props,o=n.children;return(n.disableHeader?[]:e.Children.toArray(o)).map((function(e,n){return t._createHeader({column:e,index:n})}))}},{key:"_getRowHeight",value:function(e){var t=this.props.rowHeight;return"function"==typeof t?t({index:e}):t}},{key:"_onScroll",value:function(e){var t=e.clientHeight,n=e.scrollHeight,o=e.scrollTop;(0,this.props.onScroll)({clientHeight:t,scrollHeight:n,scrollTop:o})}},{key:"_onSectionRendered",value:function(e){var t=e.rowOverscanStartIndex,n=e.rowOverscanStopIndex,o=e.rowStartIndex,r=e.rowStopIndex;(0,this.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:n,startIndex:o,stopIndex:r})}},{key:"_setRef",value:function(e){this.Grid=e}},{key:"_setScrollbarWidth",value:function(){var e=this.getScrollbarWidth();this.setState({scrollbarWidth:e})}}]),n}(e.PureComponent);Io(wi,"defaultProps",{disableHeader:!1,estimatedRowSize:30,headerHeight:0,headerStyle:{},noRowsRenderer:function(){return null},onRowsRendered:function(){return null},onScroll:function(){return null},overscanIndicesGetter:ar,overscanRowCount:10,rowRenderer:vi,headerRowRenderer:fi,rowStyle:{},scrollToAlignment:"auto",scrollToIndex:-1,style:{}}),wi.propTypes={};var Si=[],xi=null,Ci=null;function Ri(){Ci&&(Ci=null,document.body&&null!=xi&&(document.body.style.pointerEvents=xi),xi=null)}function Oi(){Ri(),Si.forEach((function(e){return e.__resetIsScrolling()}))}function Ti(e){e.currentTarget===window&&null==xi&&document.body&&(xi=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),function(){Ci&&er(Ci);var e=0;Si.forEach((function(t){e=Math.max(e,t.props.scrollingResetTimeInterval)})),Ci=tr(Oi,e)}(),Si.forEach((function(t){t.props.scrollElement===e.currentTarget&&t.__handleWindowScrollEvent()}))}function zi(e,t){Si.some((function(e){return e.props.scrollElement===t}))||t.addEventListener("scroll",Ti),Si.push(e)}function ki(e,t){(Si=Si.filter((function(t){return t!==e}))).length||(t.removeEventListener("scroll",Ti),Ci&&(er(Ci),Ri()))}var Pi,Ii,Mi=function(e){return e===window},Ei=function(e){return e.getBoundingClientRect()};function Ai(e,t){if(e){if(Mi(e)){var n=window,o=n.innerHeight,r=n.innerWidth;return{height:"number"==typeof o?o:0,width:"number"==typeof r?r:0}}return Ei(e)}return{height:t.serverHeight,width:t.serverWidth}}function Li(e,t){if(Mi(t)&&document.documentElement){var n=document.documentElement,o=Ei(e),r=Ei(n);return{top:o.top-r.top,left:o.left-r.left}}var i=ji(t),l=Ei(e),a=Ei(t);return{top:l.top+i.top-a.top,left:l.left+i.left-a.left}}function ji(e){return Mi(e)&&document.documentElement?{top:"scrollY"in window?window.scrollY:document.documentElement.scrollTop,left:"scrollX"in window?window.scrollX:document.documentElement.scrollLeft}:{top:e.scrollTop,left:e.scrollLeft}}function Hi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Di(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hi(n,!0).forEach((function(t){Io(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hi(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ni=function(){return"undefined"!=typeof window?window:void 0},Wi=(Ii=Pi=function(e){function t(){var e,n;xo(this,t);for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];return Io(To(n=zo(this,(e=ko(t)).call.apply(e,[this].concat(r)))),"_window",Ni()),Io(To(n),"_isMounted",!1),Io(To(n),"_positionFromTop",0),Io(To(n),"_positionFromLeft",0),Io(To(n),"_detectElementResize",void 0),Io(To(n),"_child",void 0),Io(To(n),"state",Di({},Ai(n.props.scrollElement,n.props),{isScrolling:!1,scrollLeft:0,scrollTop:0})),Io(To(n),"_registerChild",(function(e){!e||e instanceof Element||console.warn("WindowScroller registerChild expects to be passed Element or null"),n._child=e,n.updatePosition()})),Io(To(n),"_onChildScroll",(function(e){var t=e.scrollTop;if(n.state.scrollTop!==t){var o=n.props.scrollElement;o&&("function"==typeof o.scrollTo?o.scrollTo(0,t+n._positionFromTop):o.scrollTop=t+n._positionFromTop)}})),Io(To(n),"_registerResizeListener",(function(e){e===window?window.addEventListener("resize",n._onResize,!1):n._detectElementResize.addResizeListener(e,n._onResize)})),Io(To(n),"_unregisterResizeListener",(function(e){e===window?window.removeEventListener("resize",n._onResize,!1):e&&n._detectElementResize.removeResizeListener(e,n._onResize)})),Io(To(n),"_onResize",(function(){n.updatePosition()})),Io(To(n),"__handleWindowScrollEvent",(function(){if(n._isMounted){var e=n.props.onScroll,t=n.props.scrollElement;if(t){var o=ji(t),r=Math.max(0,o.left-n._positionFromLeft),i=Math.max(0,o.top-n._positionFromTop);n.setState({isScrolling:!0,scrollLeft:r,scrollTop:i}),e({scrollLeft:r,scrollTop:i})}}})),Io(To(n),"__resetIsScrolling",(function(){n.setState({isScrolling:!1})})),n}return Po(t,e),Ro(t,[{key:"updatePosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollElement,t=this.props.onResize,n=this.state,r=n.height,i=n.width,l=this._child||o.findDOMNode(this);if(l instanceof Element&&e){var a=Li(l,e);this._positionFromTop=a.top,this._positionFromLeft=a.left}var s=Ai(e,this.props);r===s.height&&i===s.width||(this.setState({height:s.height,width:s.width}),t({height:s.height,width:s.width}))}},{key:"componentDidMount",value:function(){var e=this.props.scrollElement;this._detectElementResize=pr(),this.updatePosition(e),e&&(zi(this,e),this._registerResizeListener(e)),this._isMounted=!0}},{key:"componentDidUpdate",value:function(e,t){var n=this.props.scrollElement,o=e.scrollElement;o!==n&&null!=o&&null!=n&&(this.updatePosition(n),ki(this,o),zi(this,n),this._unregisterResizeListener(o),this._registerResizeListener(n))}},{key:"componentWillUnmount",value:function(){var e=this.props.scrollElement;e&&(ki(this,e),this._unregisterResizeListener(e)),this._isMounted=!1}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.isScrolling,o=t.scrollTop,r=t.scrollLeft,i=t.height,l=t.width;return e({onChildScroll:this._onChildScroll,registerChild:this._registerChild,height:i,isScrolling:n,scrollLeft:r,scrollTop:o,width:l})}}]),t}(e.PureComponent),Io(Pi,"propTypes",null),Ii);function Gi(e){return(Gi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Fi(e,t,n,o,r,i,l){try{var a=e[i](l),s=a.value}catch(e){return void n(e)}a.done?t(s):Promise.resolve(s).then(o,r)}function Ui(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function l(e){Fi(i,o,r,l,a,"next",e)}function a(e){Fi(i,o,r,l,a,"throw",e)}l(void 0)}))}}function Bi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Vi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bi(Object(n),!0).forEach((function(t){Ji(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function qi(e,t){return(qi=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Zi(e,t){return!t||"object"!==Gi(t)&&"function"!=typeof t?Yi(e):t}function Yi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xi(e){return(Xi=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ji(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Io(Wi,"defaultProps",{onResize:function(){},onScroll:function(){},scrollingResetTimeInterval:150,scrollElement:Ni(),serverHeight:0,serverWidth:0});var Ki="reader-mode-list",$i=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&qi(e,t)}(c,e);var n,o,r,i,l,a,s=(l=c,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Xi(l);if(a){var n=Xi(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Zi(this,e)});function c(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),Ji(Yi(n=s.call(this,e)),"rowRenderer",(function(e){var o=e.key,r=e.index,i=e.style,l=e.parent;return t().createElement(_r,{cache:n.cache,columnIndex:0,key:o,parent:l,rowIndex:r},(function(e){var o,a=e.registerChild;return t().createElement("div",{ref:a,style:i,id:(o=r+1,"rm-page-".concat(o))},n.state.pages[r].loaded&&t().createElement(So,{page:n.state.pages[r],index:r,zoom:n.state.zoom,clickLinkHandler:n.handleLinkClicked,parent:l,onResetHeight:n.onResetHeight,getViewerElement:n.getViewerElement}))}))})),Ji(Yi(n),"setListRef",(function(e,t){e&&!n.listRef&&(n.listRef=e,t(e))})),Ji(Yi(n),"onResize",(function(e){var t=e.bounds;n.setState({dimensions:t}),n.initialized&&(n.resizeSpinner(),n.resize())})),Ji(Yi(n),"isRowLoaded",(function(e){var t=e.index;return t<n.state.pages.length&&n.state.pages[t].loaded})),Ji(Yi(n),"loadMoreRows",(function(e){var t=e.startIndex,o=e.stopIndex;return n.nextLoadTask={startIndex:t,stopIndex:o},n.isLoading||(n.loadPromise=n.loadRows(),n.isLoading=!0),n.loadPromise})),Ji(Yi(n),"onResetHeight",(function(e,t,o,r){if(n.isResettingHeight)setTimeout((function(){n.onResetHeight(e,t,o,r)}),50);else{n.isResettingHeight=!0;var i=n.cache.getHeight(e-1,0);if(t&&t!==i){var l;n.spinnerTimer?n._stopSpinnerTimer():n.setState({showSpinner:!0});var a=0,s=document.getElementById(Ki);for(l=0;l<n.state.pages.length&&s.scrollTop>0;l++){var c=n.cache.getHeight(l);if(a<=s.scrollTop&&a+c>=s.scrollTop){l++;break}a+=c}var u=-1;0===l?u=0:l>e?u=s.scrollTop-i+t:l===e&&(u=a+(s.scrollTop-a)/i*t),n.cache.set(e-1,0,n.cache.getWidth(e-1,0),t),o&&"function"==typeof o.recomputeGridSize&&o.recomputeGridSize({columnIndex:0,rowIndex:e-1}),u>=0?setTimeout((function(){n.listRef.scrollToPosition(u),setTimeout((function(){n._finishResetHeight(r)}),50)}),50):n._finishResetHeight(r)}else n._finishResetHeight(r,!1)}})),n.state=Vi(Vi({},n.state),{},{dimensions:{width:0,height:0}}),n.isLoading=!1,n.pageNum=1,n.nextLoadTask=void 0,n.loadPromise=void 0,n.listRef=void 0,n.cache=new wr({defaultHeight:800,fixedWidth:!0}),n.isResettingHeight=!1,n.spinnerTimer=void 0,n.handlePageNumberUpdated=_.debounce(n.handlePageNumberUpdated.bind(Yi(n)),300),n.resize=_.throttle(n.resize.bind(Yi(n)),300),n.handleZoomUpdated=_.throttle(n.handleZoomUpdated.bind(Yi(n)),300),n.onScroll=_.throttle(n.onScroll.bind(Yi(n)),300,{leading:!1}),n}return n=c,(o=[{key:"render",value:function(){var e=this;return t().createElement(H,{bounds:!0,onResize:this.onResize},(function(n){var o=n.measureRef;return t().createElement("div",{id:D,style:{overflow:"hidden"},ref:o},t().createElement("div",{className:"reader-mode-spinner-wrapper "+(e.state.showSpinner?"":"hidden"),style:e.state.spinnerStyle},t().createElement("div",{className:"reader-mode-spinner"})),e.state.pages.length>0&&t().createElement(Lr,{isRowLoaded:e.isRowLoaded,loadMoreRows:e.loadMoreRows,rowCount:e.state.pages.length,threshold:1,minimumBatchSize:1},(function(n){var o=n.onRowsRendered,r=n.registerChild;return t().createElement(Dr,{onRowsRendered:o,ref:function(t){return e.setListRef(t,r)},width:e.state.dimensions.width,height:e.state.dimensions.height,rowCount:e.state.pages.length,rowRenderer:e.rowRenderer,rowHeight:e.cache.rowHeight,deferredMeasurementCache:e.cache,zoom:e.state.zoom,onScroll:e.onScroll,id:Ki})})))}))}},{key:"initializePages",value:(i=Ui(regeneratorRuntime.mark((function e(t){var n,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(n=[],o=0;o<t;o++)n.push({content:"",loaded:!1}),this.cache.set(o,0,this.cache.getWidth(o,0),800);this.setState({pages:n});case 3:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"loadRows",value:(r=Ui(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.nextLoadTask){e.next=3;break}return this.isLoading=!1,e.abrupt("return");case 3:return e.next=5,new Promise((function(e){var n=function(){var n=Ui(regeneratorRuntime.mark((function n(){var o,r,i,l,a,s;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:o=t.nextLoadTask,r=o.startIndex,i=o.stopIndex,t.nextLoadTask=void 0,l=r;case 3:if(!(l<=i)){n.next=14;break}if(t.state.pages[l].loaded){n.next=11;break}return n.next=7,t.getPageContent(l);case 7:a=n.sent,s=a.htmlStr,t.state.pages[l].content=s,t.state.pages[l].loaded=!0;case 11:l++,n.next=3;break;case 14:e();case 15:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}();t.runPdfNetTask(n)}));case 5:return e.next=7,this.loadRows();case 7:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"resize",value:function(){this._startSpinnerTimer(),this.state.showSpinner||this.setState({showSpinner:!0});for(var e=0;e<this.state.pages.length;e++)if(this.state.pages[e].loaded){var t=(o=e,document.getElementById(ne(o+1)));if(t){var n=new CustomEvent(N);t.dispatchEvent(n)}}var o}},{key:"jumpToPage",value:function(e){this.setPageNumber(e+1);for(var t=0,n=0;n<e;n++)t+=this.cache.getHeight(n,0);this.listRef.scrollToPosition(t)}},{key:"handlePageNumberUpdated",value:function(e){var t=e.detail;t>this.state.pages.length||t===this.pageNum||this.jumpToPage(t-1)}},{key:"setPageNumber",value:function(e){e!==this.pageNum&&(this.pageNum=e,this.props.options.pageNumberUpdateHandler(e))}},{key:"onScroll",value:function(e){var t=e.clientHeight,n=e.scrollHeight,o=e.scrollTop;if(this.state.pages.length>0){if(0===o)return void this.setPageNumber(1);if(n===t+o)return void this.setPageNumber(this.state.pages.length);for(var r=o+t/2,i=0,l=0;l<this.state.pages.length;l++){var a=this.cache.getHeight(l);if(i<r&&i+a>=r){this.setPageNumber(l+1);break}i+=a}}}},{key:"_finishResetHeight",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this._startSpinnerTimer(),e(),this.isResettingHeight=!1}},{key:"_startSpinnerTimer",value:function(){var e=this;this._stopSpinnerTimer(),this.spinnerTimer=setTimeout((function(){e.spinnerTimer=void 0,e.setState({showSpinner:!1})}),500)}},{key:"_stopSpinnerTimer",value:function(){this.spinnerTimer&&(clearTimeout(this.spinnerTimer),this.spinnerTimer=void 0)}}])&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(n.prototype,o),c}(te);function Qi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function el(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qi(Object(n),!0).forEach((function(t){tl(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function tl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(){if("function"==typeof window.CustomEvent)return!1;window.CustomEvent=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:null};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}}();var nl={initialize:function(e){return{pdfNet:e,viewerElement:void 0,render:function(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=el({pageNumberUpdateHandler:function(e){},pageNum:1,isSinglePageMode:!0,pageCountHandler:function(e){},editStyleHandler:void 0},o);this.viewerElement=n,this.unmount(),i.isSinglePageMode?r().render(t().createElement(ho,{doc:e,pdfNet:this.pdfNet,viewport:this.viewerElement,options:i}),this.viewerElement):r().render(t().createElement($i,{doc:e,pdfNet:this.pdfNet,viewport:this.viewerElement,options:i}),this.viewerElement)},goToPage:function(e){var t=new CustomEvent(W,{detail:e});this.viewerElement.dispatchEvent(t)},setZoom:function(e){var t=new CustomEvent(G,{detail:e});this.viewerElement.dispatchEvent(t)},setAddAnnotConfig:function(e){var t=new CustomEvent(F,{detail:e});this.viewerElement.dispatchEvent(t)},unmount:function(){this.viewerElement&&r().unmountComponentAtNode(this.viewerElement)}}}};nl.AnnotationType=gn;const ol=nl;window.WebViewerReadingMode=nl})(),i})())}}]);