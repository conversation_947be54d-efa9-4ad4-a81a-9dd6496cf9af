//Q360 Start Block
var previewData = null;
$(document).ready(function () {
  $('.q360_level_chip:first').addClass('active');
  $(document).on('click', '.q360_level_chip', function () {
    $('.q360_level_chip').removeClass('active');
    const text = $(this).text();
    $(this).addClass('active');

    // $('.q360-data-append').children().remove();

    $('.academic-years-append').val('').trigger('change');
    // if ($('.forms-append').data('select2')) {
    //   $('.forms-append').select2('destroy'); // Destroy the existing select2 instance
    // }
    // $('.forms-append').select2(); // Reinitialize select2
    // $('.forms-append').val('').trigger('change');
    // $('.forms-append').children().remove();
    // if ($('.forms-append').data('select2')) {
    //   $('.forms-append').select2('destroy');
    // }
    // $('.academic-years-append option:first').prop('selected', true);
    // var formsHtml = '<option value="">Select Forms</option>';
    // $('.forms-append').html(formsHtml).val('').trigger();
    // $('#q360-data-label').text('Select ' + text);
    // var q360DataHtml = '<option value="">Select ' + text + '</option>';
    // $('.q360-data-append').html(q360DataHtml).val('').trigger();
    document.getElementById('preview-form').style.display = 'none';
    $('.chat-appender').children().remove();
    forChipNameChange();

    if ($('.forms-append').data('select2')) {
      $('.forms-append').select2('destroy');
    }
    $('.forms-append').select2();
    $('.forms-append').val('').trigger('change');

    if ($('.q360-data-append').data('select2')) {
      $('.q360-data-append').select2('destroy');
    }
    $('.q360-data-append').select2();
    $('.q360-data-append').val('').trigger('change');
  });

  $(document).on('change', '.academic-years-append', function () {
    const selectedOption = $(this).find(':selected');
    const optionId = selectedOption.attr('id');
    // const optionText = selectedOption.text();
    document.getElementById('preview-form').style.display = 'none';
    // if ($('.forms-append').data('select2')) {
    //   $('.forms-append').select2('destroy');
    // }
    if ($('.forms-append').data('select2')) {
      $('.forms-append').select2('destroy'); // Destroy the existing select2 instance
    }
    $('.forms-append').select2(); // Reinitialize select2
    $('.forms-append').val('').trigger('change');
    if (optionId !== undefined && optionId !== '') {
      showLoading();
      // if ($('.forms-append').data('select2')) {
      //   $('.forms-append').select2('destroy'); // Destroy the current instance
      // }
      setTimeout(() => {
        renderForms(optionId);
        hideLoading();
        // $('.forms-append').select2({
        //   width: 'resolve',
        // });
      }, 2000); // 2000 milliseconds = 2 seconds
    }
    // $('.forms-append').select2({
    //   width: 'resolve',
    // });
  });

  $(document).on('change', '.forms-append', function () {
    const selectedOption = $(this).find(':selected');
    const optionId = selectedOption.attr('id');
    const optionText = selectedOption.text();
    console.log(optionId, optionText);
    document.getElementById('preview-form').style.display = 'none';
    if ($('.q360-data-append').data('select2')) {
      $('.q360-data-append').select2('destroy');
    }
    $('.q360-data-append').select2();
    $('.q360-data-append').val('').trigger('change');
    if (optionId !== undefined && optionId !== '') {
      renderQ360Data(optionId);
      forChipNameChange(optionText);
    } else {
      forChipNameChange();
    }
  });

  function forChipNameChange(optionText = '') {
    if (optionText !== '') {
      // const form = optionText.replace(/form/i, '').trim();
      // $('.div-center-suggestion:first > div > .digi-question:first').attr(
      //   'real-prompt',
      //   'provide the summary of the ' + form + ' form'
      // );
      $('.div-center-suggestion:first > div > .digi-question:first').html(
        'Summary of the ' + optionText
      );
    } else {
      // $('.div-center-suggestion:first > div > .digi-question:first').attr(
      //   'real-prompt',
      //   'provide the summary of the form'
      // );
      $('.div-center-suggestion:first > div > .digi-question:first').html(
        'Summary of the form'
      );
    }
  }

  $(document).on('change', '.q360-data-append', function () {
    const selectedOption = $(this).find(':selected');
    const optionId = selectedOption.attr('value');
    // const optionText = selectedOption.text();
    console.log(optionId);
    document.getElementById('preview-form').style.display = 'none';
    if (optionId !== undefined && optionId !== '') {
      renderViewForm(optionId);
    }
  });

  $(document).on('click', '.modal-close', function () {
    document.getElementById('myModal').style.display = 'none';
  });

  $(document).on('click', function (event) {
    const modal = document.getElementById('myModal');
    const modalContent = document.querySelector('.modal-content');

    if (event.target === modal && !modalContent.contains(event.target)) {
      modal.style.display = 'none';
    }
  });
});

function renderAcademicYear() {
  var academicYearHtml = "<option id='' value=''>Select Academic Year</option>";
  $('.academic-years-append').children().remove();
  const q360Payload = currentActiveMenu;
  // q360Payload.headers = {
  //   authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkaWdpcHJvZHVjdHNhZG1pbiIsImlhdCI6MTczMDc4NDU1NCwiZXhwIjoxNzMwODIwNTU0fQ.IBbeTvSyE9eT_eQB1rJplKp26xYXwVw5o4QIp4yV3hk`,
  // };
  showLoading();
  genericAjax(q360Payload, 'academicYearEndPoint', function (data) {
    data.data.forEach(function (i, k) {
      academicYearHtml +=
        '<option value=' +
        i._id +
        ' id=' +
        i._id +
        '>' +
        i.calendar_name +
        '</option>';
    });
    $('.academic-years-append').append(academicYearHtml);
    hideLoading();
  });
}

function renderForms(optionId) {
  var formsHtml = '<option value="">Select Forms</option>';
  // $('.forms-append').children().remove();
  const text = $('.q360_level_chip.active').text().toLowerCase();
  const q360Payload = currentActiveMenu;
  // q360Payload.headers = {
  //   authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkaWdpcHJvZHVjdHNhZG1pbiIsImlhdCI6MTczMDc4NDU1NCwiZXhwIjoxNzMwODIwNTU0fQ.IBbeTvSyE9eT_eQB1rJplKp26xYXwVw5o4QIp4yV3hk`,
  // };
  const appendInUrl =
    '?formLevel=' + text + '&institutionCalenderId=' + optionId;
  showLoading();
  genericAjax(
    q360Payload,
    'formsListEndPoint',
    function (data) {
      data.data.forEach(function (i, k) {
        formsHtml +=
          '<option id=' +
          i.formInitiatedIds +
          ' categoryFormType=' +
          i.categoryFormId.categoryFormType +
          ' value=' +
          i.formInitiatedIds +
          '>' +
          i.formName +
          '</option>';
      });
      // if ($('.forms-append').data('select2')) {
      //   $('.forms-append').select2('destroy'); // Destroy the current instance
      // }
      $('.forms-append').html(formsHtml).val('').trigger('change');

      // $('.forms-append').select2({
      //   width: 'resolve',
      // });
      hideLoading();
    },
    appendInUrl
  );
}

function renderQ360Data(optionId) {
  const labelName = $('.q360_level_chip.active').text();
  var q360DataHtml = '<option value="">Select ' + labelName + '</option>';
  // $('.q360-data-append').children().remove();
  const text = labelName.toLowerCase();
  const q360Payload = currentActiveMenu;
  // q360Payload.headers = {
  //   authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkaWdpcHJvZHVjdHNhZG1pbiIsImlhdCI6MTczMDc4NDU1NCwiZXhwIjoxNzMwODIwNTU0fQ.IBbeTvSyE9eT_eQB1rJplKp26xYXwVw5o4QIp4yV3hk`,
  // };

  const queryParams = optionId
    .split(',')
    .map((id) => `formInitiatedIds[]=${id}`)
    .join('&');
  showLoading();
  const appendInUrl = `?${queryParams}`;
  genericAjax(
    q360Payload,
    'q360DataEndPoint',
    function (data) {
      data.data.forEach(function (i, k) {
        if (text === 'course') {
          q360DataHtml +=
            '<option value=' +
            i._id +
            '>' +
            (i.courseCode +
              ' - ' +
              i.courseName +
              ' (Program Name : ' +
              i.programName +
              ')') +
            '</option>';
        } else if (text === 'program') {
          q360DataHtml +=
            '<option value=' +
            i._id +
            '>' +
            (i.programName + ' (Curriculum Name : ' + i.curriculumName + ')') +
            '</option>';
        } else if (text === 'institution') {
          q360DataHtml +=
            '<option value=' + i._id + '>' + i.institutionName + '</option>';
        }
      });
      $('.q360-data-append').html(q360DataHtml).trigger('change');
      hideLoading();
    },
    appendInUrl
  );
}

function renderViewForm(optionId) {
  const q360Payload = currentActiveMenu;
  // q360Payload.headers = {
  //   authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkaWdpcHJvZHVjdHNhZG1pbiIsImlhdCI6MTczMDc4NDU1NCwiZXhwIjoxNzMwODIwNTU0fQ.IBbeTvSyE9eT_eQB1rJplKp26xYXwVw5o4QIp4yV3hk`,
  // };
  const appendInUrl = '?formInitiatorId=' + optionId;
  showLoading();
  genericAjax(
    q360Payload,
    'viewForm',
    function (data) {
      previewData = data.data;
      document.getElementById('preview-form').style.display = 'block';
      document.getElementById('preview-content').innerHTML = '';
      viewFormModal((modal = false));
      hideLoading();
    },
    appendInUrl
  );
}

function removeHTMLTags(html) {
  var tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  // Remove HTML tags and trim newlines
  return (tempDiv.textContent || tempDiv.innerText || '')
    .replace(/\n/g, '')
    .trim();
}

function handleIframeLoad(iframe, formTemplate) {
  const iframeWindow = iframe.contentWindow;
  const message = {
    values: formTemplate, // Example data
    from: 'fromDC',
  };
  iframeWindow.postMessage(message, '*');

  // Listen for a message back from the iframe
  window.addEventListener('message', function (event) {
    if (event.data && event.data.source !== 'react-devtools-content-script') {
      const q360Payload = currentActiveMenu;
      currentContext = event.data;
      q360Payload.fetchedData.data = event.data;
      console.log('Data received from iframe:', event.data);
    }
  });
}

const abbreviations = {
  Program: 'pgm',
  ANNUAL: 'ann',
  Report: 'rpt',
  'Saudi Arabia': 'ksa',
  National: 'ntl',
  Commission: 'cms',
  college: 'clg',
  department: 'dpt',
  Academic: 'acad',
  Accreditation: 'accr',
  Assessment: 'asst',
  MEDICAL: 'med',
  title: 'ttl',
  code: 'cd',
  Date: 'dt',
  and: '&',
  new: 'nw',
  of: 'f',
  this: 'ts',
  list: 'lt',
  Information: 'info',
  are: 'r',
  student: 'st',
  result: 'rst',
  learning: 'lng',
  cohort: 'cht',
  Management: 'mgmt',
  year: 'yr',
};

function loadPdf(pdfLoadUrl, modal, q360Payload) {
  const htmlContent =
    "<iframe id='myIframe' style='height:100vh' src='" +
    pdfLoadUrl +
    "' title='PDF ATTACHMENT' width='100%'></iframe>";
  document.getElementById('preview-content').innerHTML = htmlContent;

  const loadingTask = pdfjsLib.getDocument(pdfLoadUrl);
  showLoading();
  loadingTask.promise
    .then((pdf) => {
      console.log(`PDF loaded: ${pdf.numPages} pages`);
      const pagePromises = [];
      for (let pageNumber = 1; pageNumber <= 3; pageNumber++) {
        pagePromises.push(
          pdf.getPage(pageNumber).then((page) =>
            page.getTextContent().then((textContent) => {
              let text = textContent.items.map((item) => item.str).join(' ');

              // Replace words with abbreviations
              for (const [word, abbreviation] of Object.entries(
                abbreviations
              )) {
                const regex = new RegExp(`\\b${word}\\b`, 'gi'); // Match whole words, case-insensitive
                text = text.replace(regex, abbreviation);
              }

              return { pageNumber, text }; // Return modified text with page number
            })
          )
        );
      }
      // Process all pages concurrently
      return Promise.all(pagePromises);
    })
    .then((pagesText) => {
      // Process extracted text from all pages
      let concatContent =
        'use this below abbreviations ' +
        JSON.stringify(abbreviations) +
        '  to the content. ';
      pagesText.forEach(({ pageNumber, text }) => {
        // console.log(`Page ${pageNumber} Content:`, text);
        concatContent += text;
      });
      console.log(concatContent, concatContent.length);
      currentContext = concatContent;
      q360Payload.fetchedData.data = concatContent;
      hideLoading();
    })
    .catch((error) => {
      console.error('Error loading or processing PDF:', error);
      hideLoading();
    });
  if (modal) {
    document.getElementById('myModal').style.display = 'block';
  }
  return;
}

function viewFormModal(modal = true) {
  const selectedOption = $('.forms-append').find(':selected');
  const categoryFormType = selectedOption.attr('categoryFormType');
  const q360Payload = currentActiveMenu;

  const pdfAttachment = previewData && previewData.pdfAttachment;
  const pdfUrl =
    pdfAttachment && pdfAttachment.length > 0 ? pdfAttachment[0]?.url : '';
  if (pdfUrl && pdfUrl != '') {
    $.ajax({
      url: `${q360Payload.getSignedUrl}${pdfUrl}`,
      method: 'GET',
      headers: q360Payload.headers,
      success: function (data) {
        loadPdf(data.data, modal, q360Payload);
      },
      error: function (xhr, status, error) {
        console.error(`Error fetching data for:`, error);
      },
    });
    return;
  }

  if (categoryFormType === 'form') {
    const htmlContent = previewData.sectionAttachments
      .map((item) => item.description)
      .join('');
    document.getElementById('preview-content').innerHTML = htmlContent;
    currentContext = removeHTMLTags(htmlContent);
    q360Payload.fetchedData.data = removeHTMLTags(htmlContent);
  } else if (categoryFormType === 'template') {
    const text = $('.q360_level_chip.active').text().toLowerCase();
    const fileName =
      text === 'course'
        ? 'course-spec/course_spec.html'
        : 'program-report/program_report.html';
    const htmlContent =
      "<iframe id='myIframe' style='height:100vh' src='https://digival-staging-nginx-ds-yk25kmkzeq-el.a.run.app/dev-dsweb/" +
      fileName +
      "' title='course spec' width='100%'></iframe>";
    document.getElementById('preview-content').innerHTML = htmlContent;
    let formTemplate = previewData.formTemplate;
    formTemplate.isReadOnly = true;
    const iframe = document.getElementById('myIframe');
    iframe.addEventListener('load', function () {
      handleIframeLoad(iframe, formTemplate);
    });
  }

  if (modal) {
    document.getElementById('myModal').style.display = 'block';
    const tables = document.querySelectorAll('#preview-content figure.table');
    tables.forEach((table) => {
      const parent = table.parentElement;
      if (table.scrollWidth > parent.clientWidth) {
        table.classList.add('scrollable-table');
      } else {
        table.classList.remove('scrollable-table');
      }
    });
  }
}

function showLoading() {
  const backdrop = document.getElementById('loading-backdrop');
  backdrop.style.display = 'flex'; // Show the loading screen
}

function hideLoading() {
  const backdrop = document.getElementById('loading-backdrop');
  backdrop.style.display = 'none'; // Hide the loading screen
}
//Q360 End Block
