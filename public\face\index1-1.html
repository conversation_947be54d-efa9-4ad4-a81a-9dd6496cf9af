<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Verification</title>
    <link
      rel="stylesheet"
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
    />
    <script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="./js/face-api.js"></script>
    <script src="./utils.js"></script>
    <style>
      #detection {
        color: red;
      }
      #camera-container {
        position: relative; /* Make the container relative for absolute positioning of children */
        width: 500px;
        height: 375px;
        /* margin: auto; */
      }

      #cameraSelect {
        margin-bottom: 6px;
        padding: 5px;
      }

      .mtop {
        margin-top: 3rem;
      }
      /* #camera {
        width: 100%;
        height: 100%;
        transform: scaleX(-1);
      } */
      #overlay {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none; /* Prevent canvas from blocking interactions with other elements */
      }
      .preview-box {
        width: 100px;
        height: 100px;
        margin: 5px;
        overflow: hidden;
        border: 2px solid #ddd;
        float: left;
      }
      .preview-box img {
        width: 100%;
        height: 100px;
      }
      .oval-image {
        position: absolute;
        top: 0; /* Position it at the top of the container */
        left: 0;
        width: 100%;
        height: 100%; /* Make it fit the container */
        object-fit: contain; /* Adjust image scaling */
        /* z-index: 1; */
      }

      @media (min-width: 768px) {
        .mtop {
          margin-top: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="row">
        <div class="col text-center">
          <div id="camera-container" class="mb-3" style="position: relative">
            <img src="./images/oval.png" alt="Oval shape" class="oval-image" />
            <video id="camera" width="500" height="375" autoplay muted></video>
            <canvas id="overlay"></canvas>
            <button
              class="btn btn-primary"
              id="startCaptureBtn"
              onclick="startCapture()"
              style="display: none"
            >
              Start Capture
            </button>
          </div>
        </div>
        <div class="col col-lg-5 text-center mtop">
          <div id="preview" class="justify-content-center mt-1">
            <div style="display: inline-block">
              <div class="preview-box" id="img1">
                <img src="./images/straight.svg" alt="Straight Image" />
              </div>
              <div class="preview-box" id="img2">
                <img src="./images/left.svg" alt="Left Image" />
              </div>
            </div>
            <div style="display: inline-block">
              <div class="preview-box" id="img3">
                <img src="./images/right.svg" alt="Right Image" />
              </div>
              <div class="preview-box" id="img4">
                <img src="./images/up.svg" alt="Up Image" />
              </div>
            </div>
          </div>
          <select id="cameraSelect"></select>
          <p id="instruction">Position 1: Please look straight at the camera</p>
          <p id="msg"></p>
          <p id="detection" class="mt-2"></p>
        </div>
      </div>
    </div>
    <div class="container text-center mt-1">
      <!-- <div id="camera-container" class="mb-3" style="position: relative">
        <img src="./images/oval.png" alt="Oval shape" class="oval-image" />
        <video id="camera" width="500" height="375" autoplay muted></video>
        <canvas id="overlay"></canvas>
      </div>
      <button
        class="btn btn-primary"
        id="startCaptureBtn"
        onclick="startCapture()"
        style="display: none"
      >
        Start Capture
      </button> -->
      <!-- <p id="instruction">Position 1: Please look straight at the camera</p>
      <p id="msg"></p>
      <p id="detection" class="mt-2"></p>
      <div id="preview" class="d-flex justify-content-center mt-1">
        <div class="preview-box" id="img1">
          <img src="./images/straight.svg" alt="Straight Image" />
        </div>
        <div class="preview-box" id="img2">
          <img src="./images/left.svg" alt="Left Image" />
        </div>
        <div class="preview-box" id="img3">
          <img src="./images/right.svg" alt="Right Image" />
        </div>
        <div class="preview-box" id="img4">
          <img src="./images/up.svg" alt="Up Image" />
        </div>
      </div> -->
      <!-- <audio
        id="instruction0"
        src="./audio/instruction0.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction1"
        src="./audio/instruction1.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction2"
        src="./audio/instruction2.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction3"
        src="./audio/instruction3.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction4"
        src="./audio/instruction4.mp3"
        preload="auto"
      ></audio> -->
    </div>

    <script type="module">
      import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
      const { FaceLandmarker, FilesetResolver } = vision;
      const SIMILARITY_THRESHOLD = 0.47;
      let currentStream = null;
      let video = document.getElementById('camera');
      const overlay = document.getElementById('overlay');
      let currentStep = 1;
      let isModelLoaded = false;
      let straightFaceDescriptor = null;
      // const minConfidence = 0.85;
      let inputSize = 416;
      let scoreThreshold = 0.5;
      const instructions = [
        'Position 1: Please look straight at the camera',
        'Position 2: Please turn slightly to the left.',
        'Position 3: Please turn slightly to the right.',
        'Position 4: Please look straight up',
      ];
      let faceLandmarker;
      const descriptors = [];

      async function initializeFaceLandmarker() {
        const filesetResolver = await vision.FilesetResolver.forVisionTasks(
          'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
        );
        faceLandmarker = await vision.FaceLandmarker.createFromOptions(
          filesetResolver,
          {
            baseOptions: {
              modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
            },
            outputFaceBlendshapes: true,
            runningMode: 'VIDEO',
            numFaces: 1,
          }
        );
      }

      // Function to get the list of video input devices and populate the dropdown
      async function getCameraOptions() {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(
          (device) => device.kind === 'videoinput'
        );
        const cameraSelect = document.getElementById('cameraSelect');

        // Clear existing options
        cameraSelect.innerHTML = '';

        videoDevices.forEach((device, index) => {
          const option = document.createElement('option');
          option.value = device.deviceId;
          option.textContent = device.label || `Camera ${index + 1}`;
          cameraSelect.appendChild(option);
        });

        if (videoDevices.length > 0) {
          // Set the first device as default
          cameraSelect.selectedIndex = 0;

          // Start the default camera initially
          startVideo(videoDevices[0].deviceId);
        } else {
          alert('No cameras found on this device.');
        }
      }

      function sendMessageToPlatform(messageType, data = null) {
        const message = {
          type: messageType,
          payload: data,
        };
        window.parent.postMessage(message, '*');
      }

      const userId = getQueryParam('userId') || '';
      const URL = getQueryParam('url') || '';
      const URL_TOKEN = getQueryParam('token') || '';
      const token = URL_TOKEN || getCookie('DC-access_token');

      const audioCache = {};

      function preloadAudio(audioPath) {
        if (!audioCache[audioPath]) {
          const audio = new Audio(audioPath);
          audio.preload = 'auto'; // Preload the audio file
          audioCache[audioPath] = audio; // Cache it for later use
          console.log(`Preloaded: ${audioPath}`);
        } else {
          console.log(`Audio already cached: ${audioPath}`);
        }
      }

      async function startVideo(deviceId) {
        if (currentStream) {
          // Stop all tracks of the current stream before starting a new one
          currentStream.getTracks().forEach((track) => track.stop());
        }

        try {
          currentStream = await navigator.mediaDevices.getUserMedia({
            video: {
              deviceId: deviceId ? { exact: deviceId } : undefined,
            },
          });
          video.srcObject = currentStream;
        } catch (err) {
          console.error('Error accessing the camera:', err);
          alert(
            'Could not access the camera. Please check your device permissions.'
          );
        }
      }

      // Event listener for switching cameras
      document.getElementById('cameraSelect').addEventListener('click', () => {
        const selectedDeviceId = document.getElementById('cameraSelect').value;
        startVideo(selectedDeviceId);
      });

      function updateInstruction() {
        if (currentStep <= instructions.length) {
          document.getElementById('instruction').innerText =
            instructions[currentStep - 1];
          eyeBlinkLeftCount = 0;
          eyeBlinkRightCount = 0;
          eyeLookDownLeftCount = 0;
          eyeLookDownRightCount = 0;
        } else {
          document.getElementById('instruction').innerText =
            'All photos captured!';
          registerFacial();
        }
      }

      function playPreloadedAudio(audioPath) {
        const audio = audioCache[audioPath];

        const audioCallback = () => {
          if (audioPath === './audio/instruction0.mp3') {
            isModelLoaded = true;
            document.getElementById('msg').innerText = ``;
            document.getElementById('startCaptureBtn').style.display =
              'inline-block';
            detectFaces();
          }
        };
        if (audio) {
          audio
            .play()
            .catch((err) => console.error('Error playing audio:', err));

          audio.onplay = () => {
            console.log('Audio Started');
            setTimeout(() => {
              if (!isModelLoaded) {
                audioCallback();
              }
            }, 5000);
          };
          audio.onended = () => {
            if (!isModelLoaded) {
              audioCallback();
            }
            console.log('Audio ended');
          };
        } else {
          console.error('Audio not preloaded:', audioPath);
          audioCallback();
        }
      }

      function processFaceDetection(box, videoElement, landmarks) {
        const { x, y, width, height } = box;
        const videoWidth = videoElement.videoWidth;
        const videoHeight = videoElement.videoHeight;
        // Define thresholds
        const MIN_FACE_WIDTH = videoWidth * 0.3; // Minimum face width
        const MIN_FACE_HEIGHT = videoHeight * 0.3; // Minimum face height
        const MAX_MARGIN = 10; // Maximum margin from the edges

        // Validate face size and position
        const isFaceCentered =
          x > MAX_MARGIN &&
          y > MAX_MARGIN &&
          x + width < videoWidth - MAX_MARGIN &&
          y + height < videoHeight - MAX_MARGIN;
        const isFaceLargeEnough =
          width >= MIN_FACE_WIDTH && height >= MIN_FACE_HEIGHT;

        // Check key landmarks for visibility
        const leftEye = landmarks.getLeftEye();
        const rightEye = landmarks.getRightEye();
        const nose = landmarks.getNose();
        const mouth = landmarks.getMouth();

        const areLandmarksVisible =
          leftEye.length > 0 &&
          rightEye.length > 0 &&
          nose.length > 0 &&
          mouth.length > 0;

        // Validate unobstructed face
        if (isFaceCentered && isFaceLargeEnough && areLandmarksVisible) {
          // Validate distances to ensure landmarks are not obstructed
          const leftEyeToRightEyeDistance = Math.hypot(
            rightEye[0].x - leftEye[0].x,
            rightEye[0].y - leftEye[0].y
          );
          const noseToMouthDistance = Math.hypot(
            nose[0].x - mouth[0].x,
            nose[0].y - mouth[0].y
          );

          if (
            leftEyeToRightEyeDistance > width * 0.2 &&
            noseToMouthDistance > height * 0.1
          ) {
            console.log('Full unobstructed face detected.');
            return true;
          } else {
            console.log('Face is obstructed or incomplete. Please adjust.');
            document.getElementById('detection').innerText =
              'Face is partially covered. Please ensure your full face is visible.';
            return false;
          }
        } else {
          console.log('Face is not fully visible or landmarks are missing.');
          document.getElementById('detection').innerText =
            'Please make sure your full face is visible and unobstructed.';
          return false;
        }
      }

      let leftEyeClosed = false;
      let rightEyeClosed = false;
      let mouthClosed = false;

      let eyeBlinkLeftCount = 0;
      let eyeBlinkRightCount = 0;
      let eyeLookDownLeftCount = 0;
      let eyeLookDownRightCount = 0;

      function analyzeLandmarks(landmarks) {
        if (
          landmarks &&
          landmarks.faceLandmarks &&
          landmarks.faceLandmarks.length > 0
        ) {
          const eyeBlinkLeft = drawBlendShapes(
            landmarks.faceBlendshapes,
            'eyeBlinkLeft'
          );
          const eyeBlinkRight = drawBlendShapes(
            landmarks.faceBlendshapes,
            'eyeBlinkRight'
          );
          const eyeLookDownLeft = drawBlendShapes(
            landmarks.faceBlendshapes,
            'eyeLookDownLeft'
          );
          const eyeLookDownRight = drawBlendShapes(
            landmarks.faceBlendshapes,
            'eyeLookInRight'
          );
          console.log({
            eyeBlinkLeft,
            eyeBlinkRight,
            eyeLookDownLeft,
            eyeLookDownRight,
          });
          eyeBlinkLeftCount += eyeBlinkLeft > 0.4;
          eyeBlinkRightCount += eyeBlinkRight > 0.4;
          eyeLookDownLeftCount += eyeLookDownLeft > 0.58;
          eyeLookDownRightCount += eyeLookDownRight > 0.48;
        }
      }

      function getLiveDetection(orientation) {
        if (orientation === 'Head straight') {
          return (
            eyeBlinkLeftCount > 0 ||
            eyeBlinkRightCount > 0 ||
            eyeLookDownLeftCount > 0 ||
            eyeLookDownRightCount > 0
          );
        } else {
          return true;
        }
      }

      function isMouthClosed(faceData) {
        // Thresholds to determine mouth status
        const jawOpenThreshold = 0.02; // A small value indicates a closed mouth
        const mouthCloseThreshold = 0.01; // A non-zero value indicates a closed mouth
        const mouthPressThreshold = 0.05; // Pressed lips suggest a closed mouth
        const mouthPuckerThreshold = 0.02; // Minimal pucker value for a closed mouth

        // Extract relevant parameters from face data
        const {
          jawOpen,
          mouthClose,
          mouthPressLeft,
          mouthPressRight,
          mouthPucker,
        } = faceData;

        // Evaluate mouth state
        const isJawClosed = jawOpen < jawOpenThreshold;
        const isMouthPressed =
          mouthPressLeft > mouthPressThreshold ||
          mouthPressRight > mouthPressThreshold;
        const isMouthCloseValueHigh = mouthClose > mouthCloseThreshold;
        const isMouthNotPuckered = mouthPucker < mouthPuckerThreshold;

        // Combine conditions to determine if the mouth is closed
        return (
          isJawClosed &&
          (isMouthCloseValueHigh || isMouthPressed) &&
          isMouthNotPuckered
        );
      }

      async function capturePosition(orientation) {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const context = canvas.getContext('2d');
        context.filter = 'brightness(1.2) contrast(1.2)';
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        const detections = await faceapi
          .detectSingleFace(
            video,
            // new faceapi.SsdMobilenetv1Options({ minConfidence })
            new faceapi.TinyFaceDetectorOptions({ inputSize, scoreThreshold })
          )
          .withFaceLandmarks()
          .withFaceDescriptor();
        const results = faceLandmarker.detectForVideo(video, performance.now());
        if (orientation !== 'Head tilted up') {
          if (
            results &&
            results.faceLandmarks &&
            results.faceLandmarks.length > 0
          ) {
            const visionLandmarks = results.faceLandmarks[0];
            const eyeBlinkLeft = drawBlendShapes(
              results.faceBlendshapes,
              'eyeBlinkLeft'
            );
            const eyeBlinkRight = drawBlendShapes(
              results.faceBlendshapes,
              'eyeBlinkRight'
            );

            // const exampleFaceData = {
            //   jawOpen: drawBlendShapes(results.faceBlendshapes, 'jawOpen'),
            //   mouthClose: drawBlendShapes(
            //     results.faceBlendshapes,
            //     'mouthClose'
            //   ),
            //   mouthPressLeft: drawBlendShapes(
            //     results.faceBlendshapes,
            //     'mouthPressLeft'
            //   ),
            //   mouthPressRight: drawBlendShapes(
            //     results.faceBlendshapes,
            //     'mouthPressRight'
            //   ),
            //   mouthPucker: drawBlendShapes(
            //     results.faceBlendshapes,
            //     'mouthPucker'
            //   ),
            // };
            // mouthClosed = isMouthClosed(exampleFaceData);
            leftEyeClosed = eyeBlinkLeft > 0.4;
            rightEyeClosed = eyeBlinkRight > 0.4;
          } else {
            leftEyeClosed = true;
            rightEyeClosed = true;
          }
        }
        if (leftEyeClosed || rightEyeClosed) {
          document.getElementById(
            'detection'
          ).innerText = `Both eyes should be opened`;
        }

        // if (mouthClosed) {
        //   document.getElementById('detection').innerText = `Mouth is closed`;
        // }

        // const liveDetection = analyzeLandmarks(results);

        // console.log({
        //   eyeBlinkLeftCount,
        //   eyeBlinkRightCount,
        //   eyeLookDownLeftCount,
        //   eyeLookDownRightCount,
        // });
        if (
          detections &&
          detections.landmarks &&
          detections.landmarks._positions.length > 0 &&
          detections.detection &&
          detections.detection.score > 0.75 &&
          !leftEyeClosed &&
          !rightEyeClosed
          // &&
          // !mouthClosed &&
          // getLiveDetection(orientation)
        ) {
          const box = detections.detection.box; // Bounding box of the face
          const landmarks = detections.landmarks; // 68-point landmarks
          const detectedOrientation = getNewHeaderPosition(landmarks);
          // console.log({ detectedOrientation });
          // const visibleFace = processFaceDetection(box, video, landmarks);
          // console.log({
          //   visibleFace,
          // });
          // Extract dimensions from the box
          let { x: minX, y: minY, width: boxWidth, height: boxHeight } = box;

          // Add padding to the bounding box
          const padding = 0.4; // Adjust padding as needed (e.g., 40%)
          const paddedWidth = boxWidth * (1 + padding);
          const paddedHeight = boxHeight * (1 + 0.7);

          // Adjust `minX` and `minY` to apply padding
          minX -= (paddedWidth - boxWidth) / 2;
          minY -= (paddedHeight - boxHeight) / 2;

          // Ensure the boundaries stay within the canvas dimensions
          minX = Math.max(0, minX);
          minY = Math.max(0, minY);
          const maxWidth = Math.min(canvas.width - minX, paddedWidth);
          const maxHeight = Math.min(canvas.height - minY, paddedHeight);

          // Create a new canvas for the cropped face
          const croppedCanvas = document.createElement('canvas');
          croppedCanvas.width = maxWidth;
          croppedCanvas.height = maxHeight;

          // Draw the cropped face onto the new canvas
          const croppedCtx = croppedCanvas.getContext('2d');
          croppedCtx.drawImage(
            canvas, // Source canvas
            minX, // Source x
            minY, // Source y
            maxWidth, // Source width
            maxHeight, // Source height
            0, // Destination x
            0, // Destination y
            maxWidth, // Destination width
            maxHeight // Destination height
          );

          const croppedImage = new Image();
          croppedImage.src = croppedCanvas.toDataURL();
          await croppedImage.decode();

          if (detectedOrientation === orientation) {
            if (
              detectedOrientation === 'Head straight' &&
              orientation === 'Head straight'
            ) {
              straightFaceDescriptor = detections?.descriptor
                ? detections?.descriptor
                : null;
            }
            let distance = 0;
            if (
              detectedOrientation !== 'Head straight' &&
              orientation !== 'Head straight' &&
              straightFaceDescriptor !== null
            ) {
              distance = await faceapi.euclideanDistance(
                detections?.descriptor,
                straightFaceDescriptor
              );
            }
            console.log({ distance });
            if (
              orientation === 'Head straight' ||
              (orientation !== 'Head straight' && distance <= 0.4)
            ) {
              descriptors.push([...detections?.descriptor]);
              document.getElementById('detection').innerText = ``;
              document.getElementById(
                `img${currentStep}`
              ).innerHTML = `<img src="${croppedImage.src}" alt="Captured Image">`;
              currentStep++;
              updateInstruction();
            }
            countdownAndCapture(); // Move to the next position countdown
          } else {
            // Retry if orientation does not match
            setTimeout(() => capturePosition(orientation), 1000);
            // document.getElementById('detection').innerText =
            //   `Detection Orientation : ` +
            //   detectedOrientation +
            //   `. Make sure to Correct Orientation : ` +
            //   orientation;
          }
        } else {
          // Retry if no detections were found
          // console.log('No face detected, retrying...');
          setTimeout(() => capturePosition(orientation), 1000);
        }
      }

      function countdownAndCapture() {
        let countdown = 3;
        const countdownInterval = setInterval(() => {
          if (countdown > 0) {
            if (currentStep > 4) {
              clearInterval(countdownInterval);
              // document.getElementById('instruction').innerText = ``;
              // endCapture();
              return;
            }
            document.getElementById(
              'msg'
            ).innerText = `Capturing in ${countdown}...`;
            countdown--;
          } else {
            clearInterval(countdownInterval);
            // console.log({ countdownInterval, currentStep });
            const findStep = [
              'Head straight',
              'Head turned left',
              'Head turned right',
              'Head tilted up',
            ][currentStep - 1];
            if (findStep) {
              // speakInstruction();
              // playPreRecordedAudio(`./audio/instruction${currentStep}.mp3`);
              // playAudioById(`instruction${currentStep}`);
              playPreloadedAudio(`./audio/instruction${currentStep}.mp3`);
              capturePosition(findStep);
            } else {
              currentStep = 1;
              clearInterval(countdownInterval);
              // document.getElementById('instruction').innerText = ``;
              // endCapture();
              return;
            }
          }
        }, 200);
      }

      function startCapture() {
        currentStep = 1;
        updateInstruction();
        countdownAndCapture();
        document.getElementById('startCaptureBtn').style.display = 'none';
      }

      function endCapture() {
        document.getElementById('startCaptureBtn').style.display =
          'inline-block';
      }

      function dString(name) {
        if (name !== '') {
          return window.atob(name);
        }
        return '';
      }

      function dataURLtoFile(dataurl, filename) {
        var arr = dataurl.split(','),
          mime = arr[0].match(/:(.*?);/)[1],
          bstr = dString(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      }

      function getCookie(name) {
        // Create a regular expression to find the cookie by name
        const cookieName = name + '=';
        const decodedCookie = decodeURIComponent(document.cookie); // Decode cookie value
        const cookieArray = decodedCookie.split(';'); // Split cookies into an array

        // Loop through all cookies
        for (let i = 0; i < cookieArray.length; i++) {
          let cookie = cookieArray[i].trim(); // Trim spaces
          // If cookie name matches, return its value
          if (cookie.indexOf(cookieName) === 0) {
            return cookie.substring(cookieName.length, cookie.length);
          }
        }
        // Return null if cookie not found
        return null;
      }

      function registerFacial() {
        const centerImgElement = document.querySelector('#img1 img');
        const centerImgSrc = centerImgElement.getAttribute('src');

        const leftImgElement = document.querySelector('#img2 img');
        const leftImgSrc = leftImgElement.getAttribute('src');

        const rightImgElement = document.querySelector('#img3 img');
        const rightImgSrc = rightImgElement.getAttribute('src');

        const upImgElement = document.querySelector('#img4 img');
        const upImgSrc = upImgElement.getAttribute('src');

        const center = dataURLtoFile(centerImgSrc, 'center.png');
        const left = dataURLtoFile(leftImgSrc, 'left.png');
        const right = dataURLtoFile(rightImgSrc, 'right.png');
        const up = dataURLtoFile(upImgSrc, 'up.png');

        let formData = new FormData();
        formData.append('userId', userId);
        formData.append('center', center);
        formData.append('left', left);
        formData.append('right', right);
        formData.append('up', up);
        formData.append(
          'faceDescriptors',
          JSON.stringify([
            {
              face: 'center',
              descriptor: descriptors[0],
            },
            {
              face: 'right',
              descriptor: descriptors[2],
            },
            {
              face: 'left',
              descriptor: descriptors[1],
            },
            {
              face: 'up',
              descriptor: descriptors[3],
            },
          ])
        );
        document.getElementById('msg').innerText = 'Face Data Saving...';
        $.ajax({
          type: 'POST',
          url: URL,
          data: formData,
          cache: false,
          processData: false,
          contentType: false,
          headers: {
            'Access-Control-Allow-Origin': '*',
            authorization: 'Bearer ' + token,
          },
          success: function (data) {
            endCapture();
            document.getElementById('instruction').innerText =
              'Face Registration Successfully.';
            sendMessageToPlatform('success', {
              centerImgSrc,
              leftImgSrc,
              rightImgSrc,
              upImgSrc,
            });
            document.getElementById('msg').innerText = '';
          },
          error: function () {
            console.log('facial request failed');
            document.getElementById('instruction').innerText =
              'Facial Request Failed.';
            sendMessageToPlatform('failed');
            document.getElementById('msg').innerText = '';
          },
        });
      }

      async function loadLibraries() {
        const MODEL_URL = 'models';
        document.getElementById(
          'msg'
        ).innerText = `Loading Face Models. Please wait...`;
        // await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
        await faceapi.loadFaceLandmarkModel(MODEL_URL);
        await faceapi.loadFaceRecognitionModel(MODEL_URL);
        //  await faceapi.loadFaceLandmarkTinyModel(MODEL_URL);
        await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
        console.log('load facial Libraries done');
        // isModelLoaded = true;
        // document.getElementById('msg').innerText = ``;
        // document.getElementById('startCaptureBtn').style.display =
        //   'inline-block';
      }

      function drawBlendShapes(blendShapes, type = 'eyeBlinkLeft') {
        if (!blendShapes.length) {
          return;
        }

        const result = blendShapes[0].categories
          .filter((shape) => shape.categoryName === type)
          .map((shape) => (+shape.score).toFixed(4));

        return result && result.length ? result[0] : 0;
      }

      async function detectFaces() {
        if (!isModelLoaded) return;

        const displaySize = { width: video.width, height: video.height };
        faceapi.matchDimensions(overlay, displaySize);
        // speakInstruction('sit straight and get ready for face registration.');
        setInterval(async () => {
          const detections = await faceapi
            .detectSingleFace(
              video,
              // new faceapi.SsdMobilenetv1Options({ minConfidence })
              new faceapi.TinyFaceDetectorOptions({ inputSize, scoreThreshold })
            )
            .withFaceLandmarks();

          // Clear previous drawings
          const context = overlay.getContext('2d');
          context.clearRect(0, 0, overlay.width, overlay.height);

          // Resize and draw the detected boxes
          if (detections) {
            const resizedDetections = faceapi.resizeResults(
              detections,
              displaySize
            );

            faceapi.draw.drawDetections(overlay, resizedDetections);
            faceapi.draw.drawFaceLandmarks(overlay, resizedDetections);

            // const results = faceLandmarker.detectForVideo(
            //   video,
            //   performance.now()
            // );
            // if (results.faceLandmarks.length > 0) {
            //   const visionLandmarks = results.faceLandmarks[0];
            //   const eyeBlinkLeft = drawBlendShapes(
            //     results.faceBlendshapes,
            //     'eyeBlinkLeft'
            //   );
            //   const eyeBlinkRight = drawBlendShapes(
            //     results.faceBlendshapes,
            //     'eyeBlinkRight'
            //   );
            //   leftEyeClosed = eyeBlinkLeft > 0.4;
            //   rightEyeClosed = eyeBlinkRight > 0.4;
            // } else {
            //   leftEyeClosed = true;
            //   rightEyeClosed = true;
            // }

            const landmarks = detections.landmarks;
            const direction = getNewHeaderPosition(landmarks);
            const instructionPosition =
              instructions[currentStep - 1] !== undefined
                ? instructions[currentStep - 1]
                : '';
            context.font = '18px Arial';
            let brightnessHtml = '';
            const box = detections.detection.box;
            // processFaceDetection(box, video, landmarks);

            if (detections.detection.score < 0.75) {
              brightnessHtml =
                'Face detection confidence is low. Please adjust lighting or move closer to the camera.';
            }
            // const brightness = calculateBrightness(overlay);
            // if (brightness > 200) {
            //   brightnessHtml =
            //     'Background light is too strong. Please adjust lighting conditions.';
            // } else if (brightness < 50) {
            //   brightnessHtml = 'Lighting is too dim. Please increase lighting.';
            // }

            const lines =
              `${instructionPosition}\n                      ${direction}\n${brightnessHtml}`.split(
                '\n'
              ); // Split text into lines
            const x = resizedDetections.detection.box.x - 100;
            let y = resizedDetections.detection.box.y - 60;

            lines.forEach((line, i) => {
              context.fillText(line, x, y);
              context.fillStyle = i != 0 ? 'white' : 'red';
              y += 35; // Adjust this value based on your desired line spacing
            });
            document.getElementById('detection').innerText = '';
            // context.fillText(
            //   `${direction} \n ${direction}`,
            //   resizedDetections.detection.box.x + 50,
            //   resizedDetections.detection.box.y - 5
            // );
          } else {
            document.getElementById('detection').innerText =
              'Face detection confidence is low. Please adjust lighting or move closer to the camera.';
          }
        }, 1000); // Detect every 100ms
      }

      // Thresholds (normalized values)
      const horizontalThreshold = 0.1;
      const EAR_THRESHOLD = 0.25;

      function getNewHeaderPosition(landmarks) {
        const leftEye = landmarks.getLeftEye();
        const rightEye = landmarks.getRightEye();
        const nose = landmarks.getNose();
        const chin = landmarks.getJawOutline().pop(); // Last point in jaw outline
        const leftEyeMid = {
          x: (leftEye[0].x + leftEye[3].x) / 2,
          y: (leftEye[0].y + leftEye[3].y) / 2,
        };
        const rightEyeMid = {
          x: (rightEye[0].x + rightEye[3].x) / 2,
          y: (rightEye[0].y + rightEye[3].y) / 2,
        };

        // Calculate the key distances
        const noseTipX = nose[0].x; // Nose tip
        const leftEyeMidX = (leftEye[0].x + leftEye[3].x) / 2; // Midpoint of left eye
        const rightEyeMidX = (rightEye[0].x + rightEye[3].x) / 2; // Midpoint of right eye
        const eyeMidDistance = Math.abs(leftEyeMidX - rightEyeMidX);
        // Example: Horizontal turn detection
        const noseShift = noseTipX - (leftEyeMidX + rightEyeMidX) / 2;
        // if (noseShift > SIMILARITY_THRESHOLD) {
        // 	console.log('Head turned left');
        // } else if (noseShift < -SIMILARITY_THRESHOLD) {
        // 	console.log('Head turned right');
        // }

        // Eye-to-chin distance
        const eyeMidY = (leftEyeMid.y + rightEyeMid.y) / 2;
        const eyeToChinDistance = chin.y - eyeMidY;

        // Angle calculation
        const deltaY = rightEyeMid.y - leftEyeMid.y;
        const deltaX = rightEyeMid.x - leftEyeMid.x;
        const slope = deltaY / deltaX;
        const angle = Math.atan(slope) * (180 / Math.PI); // Convert to degrees

        // Normalize distances
        const normalizedNoseShift = noseShift / eyeMidDistance;
        const eyeToChinRatio = eyeToChinDistance / eyeMidDistance;
        let headDirection = '';
        console.log({ currentStep });
        // Calculate EAR for both eyes

        if (normalizedNoseShift > horizontalThreshold) {
          headDirection = 'Head turned left';
        } else if (normalizedNoseShift < -horizontalThreshold) {
          headDirection = 'Head turned right';
        } else {
          headDirection = 'Head straight';
        }

        // Check vertical position
        if (eyeToChinRatio > 0.25) {
          headDirection = 'Head tilted up';
        }
        // else if (eyeToChinRatio < 0.1) {
        // 	headDirection = 'Head tilted down';
        // }

        return headDirection;
      }

      let baselineEAR = null; // To store the baseline EAR

      function calculateEAR(landmarks, eyeIndices) {
        // Get the required points from landmarks for the specified eye
        const p1 = landmarks[eyeIndices[0]];
        const p2 = landmarks[eyeIndices[1]];
        const p3 = landmarks[eyeIndices[2]];
        const p4 = landmarks[eyeIndices[3]];
        const p5 = landmarks[eyeIndices[4]];
        const p6 = landmarks[eyeIndices[5]];
        const vertical1 = Math.hypot(p2.x - p6.x, p2.y - p6.y); // ||p2 - p6||
        const vertical2 = Math.hypot(p3.x - p5.x, p3.y - p5.y); // ||p3 - p5||
        const horizontal = Math.hypot(p1.x - p4.x, p1.y - p4.y); // ||p1 - p4||

        return (vertical1 + vertical2) / (2.0 * horizontal);
      }

      // Initialize everything when the page loads
      window.onload = async () => {
        await initializeFaceLandmarker();
        await loadLibraries();
        // await startVideo();
        getCameraOptions();
        console.log('FaceLandmarker initialized and video started');

        preloadAudio('./audio/instruction0.mp3');
        preloadAudio('./audio/instruction1.mp3');
        preloadAudio('./audio/instruction2.mp3');
        preloadAudio('./audio/instruction3.mp3');
        preloadAudio('./audio/instruction4.mp3');

        setTimeout(() => {
          playPreloadedAudio(`./audio/instruction0.mp3`);
        }, 3000);

        getCameraOptions();
      };
      window.startCapture = startCapture;
    </script>
  </body>
</html>
