//local url http://localhost:3001/chat/index1.html#
currentActiveMenu = {};
currentlevel = { level1: {}, level2: {} };
currentContext = '';
heba_user_name = 'MOHAMMED Ali';
noTabData = [
  'policy',
  'schedule',
  'plo_analysis',
  'monitoring',
  'q360',
  'absence_leave',
];
staffSuggestionModuels = [];
//hebaBaseAPi = 'https://gemini-pro-chatbot-yk25kmkzeq-el.a.run.app/gemini-pro';
// hebaBaseAPi =
//   'https://gemini-pro-chatbot-yk25kmkzeq-el.a.run.app/vertexai_chat_bot';
const hebaUrl = getUnifyData('DC-unifyData', 'hebaUrl');
hebaBaseAPi =
  hebaUrl ||
  'https://gemini-pro-chatbot-1030409224402.asia-south1.run.app/vertexai_custom_model/chat_bot?userModel=gemini-1.5-pro-002';
data_mapper = {
  schedule: schedules_data,
  plo_analysis: q360_data,
  general_chat: '',
  monitoring: curriculam_monitor_data,
  q360: q360_data,
  absence_leave: leave_data,
};

// Function to get the value of a query parameter by name
function getQueryParam(name) {
  const params = new URLSearchParams(window.location.search);
  return params.get(name); // Returns the value of the parameter, or null if not found
}

const userRole = getQueryParam('role') || 'admin';
const activeTab = getQueryParam('activeTab') || '';
heba_user_name = userRole == 'student' ? heba_user_name : 'Mohammed';
console.log('userRole', userRole);
console.log(activeTab);
$(document).ready(function () {
  $('.chat-loader').hide();
  // Assuming there is a <div class="chat-support"></div> element in your HTML
  $('.heba-sidebar').append(generateMenu(heba_main_object));
  initSideMenuSelection();
  if (activeTab !== undefined && activeTab != '') {
    $('.' + activeTab).trigger('click');
  } else {
    $('.learning_resources').trigger('click');
  }
  $('.heba-user-name').text(heba_user_name);
});

function initSideMenuSelection() {
  var closeSidebarItems = [
    'session_topic_summary',
    'grades_and_outcomes',
    'grades_and_outcomes_staff',
    'learning_resources',
    'policy',
    'absence_leave',
    'general_chat',
    'schedule',
    'q360',
  ];

  $('.heba-sidebar li').on('click', function () {
    $(this).toggleClass('active');

    $('.heba-sidebar li a').removeClass('active');

    if ($(this).hasClass('active')) {
      $(this).find('.digi-default-icon').hide();
      $(this).find('.digi-active-icon').show();

      $(this).find('a').addClass('active');

      $('.heba-sidebar li')
        .not($(this))
        .removeClass('active')
        .find('.digi-default-icon')
        .show();
      $('.heba-sidebar li').not($(this)).find('.digi-active-icon').hide();
    } else {
      $(this).find('.digi-default-icon').show();
      $(this).find('.digi-active-icon').hide();
    }

    $('.heba-sidebar li a').removeClass('active');

    $(this).find('a').addClass('active');

    currentActiveMenu = findByTechName($(this).attr('data-tech-name'));

    $('.responce-chip,.common-selector').hide();
    $('.' + $(this).attr('data-tech-name')).show();

    let bigIconUrl = $(this).find('.side-nav-icon').attr('src');
    let newBigUrl = bigIconUrl.replace(/\.svg$/, '-big.svg');
    $('.main_pic').attr('src', newBigUrl);

    if (!noTabData.includes(currentActiveMenu.techName)) {
      fetchMenuItemData(currentActiveMenu.techName, function (data) {
        handleTabBasedData(currentActiveMenu, data);
        console.log(`Data for ${currentActiveMenu.techName}:`, data);
      });
    } else {
      currentActiveMenu = {
        ...currentActiveMenu,
        fetchedData: { data: data_mapper[currentActiveMenu.techName] },
      };
    }

    // Update selected title text
    $('.selected-title h3').text($(this).text());
    console.log(currentActiveMenu);

    // Hide all digi-questions and update with chip suggestions
    $('.digi-question').fadeOut('fast');
    $('.digi-question').each(function (index) {
      if (index >= currentActiveMenu.chipSuggestions.length) {
        return false;
      }
      var $this = $(this);
      var suggestion =
        currentActiveMenu && currentActiveMenu.chipSuggestions[index].name;

      if (suggestion) {
        $this.fadeOut('fast', function () {
          $this.text(suggestion).fadeIn('fast');
          $this.attr(
            'real-prompt',
            currentActiveMenu.chipSuggestions[index].prompt
          );
        });
      } else {
        $this.fadeOut('fast');
      }
    });

    // Handle sidebar visibility
    var techName = $(this).attr('data-tech-name');
    if (closeSidebarItems.includes(techName)) {
      $('#check').prop('checked', false);
      $('.content').css('opacity', 1);
    } else {
      // $('.content').css('opacity', 0.1);
    }

    if (currentActiveMenu.name === 'Q360') {
      renderAcademicYear();
    }
  });

  $('.digi-question').on('click', function () {
    let displayName = $(this).text();
    prepareToGemini($(this).attr('real-prompt'), displayName);
  });

  $('.send')
    .off('click')
    .on('click', function () {
      $(this).hide();
      prepareToGemini($('.rounded-input').val());
    });

  $('.rounded-input').keypress(function (event) {
    var keycode = event.keyCode ? event.keyCode : event.which;
    if (keycode == '13') {
      if ($(this).val().length > 0) {
        prepareToGemini($('.rounded-input').val());
      }
    }
  });

  initEventAttachments();
}

function initEventAttachments() {
  handlePolicyChip();
  grades_outcomes_staff_chip();
}

function prepareToGemini(input, realname = '') {
  currentContext = JSON.stringify(currentActiveMenu.fetchedData.data);

  sentToGemini(input, realname);
}

function findByTechName(techName) {
  // Assuming heba_main_object is accessible in the scope
  return heba_main_object.find(function (item) {
    return item.techName === techName;
  });
}
function generateMenu(menuItems) {
  var $ul = $('<ul>');

  menuItems.forEach(function (item) {
    // Create the <li> element and append the techName as an attribute
    var $li = $('<li>')
      .attr('data-tech-name', item.techName)
      .addClass(item.techName)
      .addClass(item.allowed?.includes(userRole) ? '' : 'hidden');

    // Create and append the <a> and <img> elements to the <li>
    $li
      .append(
        $('<a>')
          .attr('href', '#')
          .append(
            $('<img>')
              .attr('src', item.iconUrl + '.svg')
              .addClass('side-nav-icon digi-default-icon')
          )
          .append(
            $('<img>')
              .attr('src', item.iconUrl + '-hover.svg')
              .addClass('side-nav-icon digi-active-icon')
              .css('display', 'none')
          )
          .append(document.createTextNode(' ' + item.name))
      )
      .addClass('inactive')
      // Append the <li> to the <ul>
      .appendTo($ul);
  });

  return $ul;
}

function markdownToHtml(markdown) {
  // Convert bold text: **text** to <strong>text</strong>
  let html = markdown.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Convert lists: Lines starting with * to <ul><li>item</li></ul> blocks
  html = html.replace(/(?:\n\* (.*))+/g, function (match) {
    const lis = match
      .trim()
      .split('\n')
      .map(function (li) {
        return `<li>${li.substring(2)}</li>`; // Remove the '* ' part
      })
      .join('');
    return `<ul>${lis}</ul>`;
  });

  // Convert paragraphs: Separate by two newlines
  html = `<p>${html}</p>`;
  html = html.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>');

  return html;
}

function renderChat(qtn, answersHtml, techName) {
  answersHtml = markdownToHtml(answersHtml); // Convert markdown to HTML
  var icons = [
    { icon: 'speaker', tooltipText: 'Read Aloud' },
    { icon: 'copy', tooltipText: 'Copy' },
    // { icon: 'en-ar', tooltipText: 'Translate' },
    // { icon: 'send1', tooltipText: 'Send to live' },
  ]
    .map(
      (item) =>
        `<div class="close_icon tooltip" data-icon="${item.icon}"><img src="./images/${item.icon}.svg" /><span class="tooltiptext">${item.tooltipText}</span></div>`
    )
    .join('');

  var content = `
    <div class="chat-helper-bg-inner">
      <div class="chat-bg-white">
        <div class="digi-d-flex chat-border-bottom">
          <div class="digi-question-align"><h1 class="ask-heba text-left">${qtn}</h1></div>
          <div class="d-flex app-font">
            <div class="app-font-size f-12">Aa</div>
            <div class="app-font-size f-14 active">Aa</div>
            <div class="app-font-size f-18">Aa</div>
          </div>
        </div>
        <div class="bot_answers">
          ${answersHtml}
        </div>
      </div>
    </div>
    <div>
    <div class="icon_group">${icons}</div>
    <div class="clr"></div>
    </div>`;

  // Append and use fade-in effect for smooth display
  var $chatContent = $('<div>', {
    class: 'chat-helper-bg responce-chip ' + techName,
    'data-tech-name': techName,
    html: content,
  })
    .hide()
    .appendTo('.chat-appender')
    .fadeIn('slow');

  // Font size adjustment
  $chatContent.on('click', '.app-font-size', function () {
    var newSize = $(this).hasClass('f-12')
      ? '12px'
      : $(this).hasClass('f-14')
      ? '14px'
      : $(this).hasClass('f-18')
      ? '18px'
      : '16px';
    $chatContent.find('.bot_answers').css('font-size', newSize);
    $chatContent.find('.app-font-size').removeClass('active');
    $(this).addClass('active');
  });

  // Text-to-speech toggle
  $chatContent.on('click', '.close_icon[data-icon="speaker"]', function () {
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel(); // Stops the speech
    } else {
      var textToSpeak = $chatContent.find('.bot_answers').text();
      var msg = new SpeechSynthesisUtterance(textToSpeak);
      var voices = window.speechSynthesis.getVoices();
      var femaleVoice =
        voices.find((voice) => voice.gender === 'female') ||
        voices.find((voice) => voice.lang.includes('en-US')) ||
        voices[0];
      msg.voice = femaleVoice;

      window.speechSynthesis.speak(msg);
    }
  });

  // Copy to clipboard
  $chatContent.on('click', '.close_icon[data-icon="copy"]', function () {
    var textToCopy = $chatContent.find('.bot_answers').text();
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        console.log('Text copied to clipboard');
        showcopy();
        // Optionally, show a message to the user indicating success
      })
      .catch((err) => {
        console.error('Failed to copy text: ', err);
      });
  });
  scrollToBottom();
}

function sentToGemini(input, displayName) {
  // var jsonData = {
  //   temperature: 0.6,
  //   data: '!!!' + currentContext + '!!!',
  //   input:
  //     'based on this data on ' + currentActiveMenu.name + '  ' + input + ' ',
  //   //  input: +' ' + JSON.stringify(all_course_data) + input + ' ' + hiddenPromt,
  // };

  const prompt = `Based on the context provided to you Contextdata =!!!${currentContext}!!! as  (Contextdata), write a response that addresses the user's query: "${input}" (User Query)`;

  // var jsonData = {
  //   temperature: 0.6,
  //   data: '!!!' + currentContext + '!!!',
  //   input:
  //     'based on this data on ' + currentActiveMenu.name + '  ' + input + ' ',
  //   //  input: +' ' + JSON.stringify(all_course_data) + input + ' ' + hiddenPromt,
  // };

  var jsonData = {
    temperature: 0.6,
    data: '',
    input: prompt,
  };

  $('.chat-loader').show();
  $('.send').hide();
  hiddenPromt = '';
  $.ajax({
    url: hebaBaseAPi,
    method: 'POST',
    beforeSend: function (xhr) {
      // Add the Access-Control-Allow-Origin header
      xhr.setRequestHeader('Access-Control-Allow-Origin', '*');
      xhr.setRequestHeader('withCredentials', 'true');
    },
    contentType: 'application/json',
    data: JSON.stringify(jsonData),
    success: function (response) {
      console.log('success jemini', response);
      $('.rounded-input').val('');
      if (displayName) {
        renderChat(input, response.responce, currentActiveMenu.techName);
      } else {
        renderChat(input, response.responce, currentActiveMenu.techName);
      }
      $('.chat-loader').hide();
      $('.send').show();
      $('.display-arrow').css({
        display: 'block',
      });
    },
    error: function (error) {
      console.log('error', error);
      $('.chat-loader').hide();
      $('.send').show();
    },
  });
}

function fetchMenuItemData(techName, callback) {
  const menuItem = heba_main_object.find((item) => item.techName === techName);
  if (!menuItem) {
    console.error('Tech name not found:', techName);
    return;
  }

  // Check if data has already been fetched
  if (menuItem.dataFetched) {
    console.log(`Using cached data for ${techName}`);
    if (callback) callback(menuItem.fetchedData);
    return;
  }

  $.ajax({
    url: menuItem.endpoint,
    method: menuItem.method,
    headers: menuItem.headers,
    success: function (data) {
      console.log(`Data fetched for ${techName}:`, data);
      // Update the item to indicate data has been fetched and store the data
      menuItem.dataFetched = true;
      menuItem.fetchedData = data;
      if (callback) callback(data);
    },
    error: function (xhr, status, error) {
      console.error(`Error fetching data for ${techName}:`, error);
    },
  });
}

function handlePolicyChip() {
  $('.policy-chip')
    .off('click')
    .on('click', function () {
      $('.policy-chip').removeClass('active');
      $(this).addClass('active');
      currentActiveMenu = { ...currentActiveMenu, fetchedData: { data: '' } };

      if ($(this).text() == 'Program') {
        currentActiveMenu.fetchedData.data = program_policy_data;
      } else {
        currentActiveMenu.fetchedData.data = institution_policy_data;
      }
      console.log('hanlde chp ', currentActiveMenu);
    });
}

function handleTabBasedData(techdataObj, data) {
  if (
    techdataObj.techName == 'learning_resources' &&
    $('.digi-courses').length == 0
  ) {
    data.data = data.data.slice(0, 4);
    let courseHtml = '';
    data.data.forEach(function (k, i) {
      courseHtml +=
        '<div id="' +
        k._id +
        '" class="digi-courses bg-white">' +
        k.course_name +
        '</div>';
    });

    $('.courseAppender').append(courseHtml);
    handleCourseSelection();
  }

  if (techdataObj.techName == 'grades_and_outcomes_staff') {
    //setTimeout(function () {
    $('.student-selector').hide();
    //}, 10);
  }

  //var technameChosen=techdataObj
}

function handleCourseSelection() {
  $('.digi-courses')
    .off('click')
    .on('click', function () {
      $('.digi-courses').removeClass('active');
      $(this).addClass('active');
      renderTopics($(this).attr('id'));
    });
}

function renderTopics(id) {
  var topichtml = '<option>Select Topic</option>';
  $('.topics-appender').children().remove();
  console.log('id passed', id);
  genericAjax(currentActiveMenu, 'endpoint2', function (data) {
    console.log('inside each ', data);
    data.data.forEach(function (i, k) {
      topichtml += '<option>' + i.delivery_topic + '- Topic</option>';
    });
    console.log('call back ', data);
    $('.topics-appender').append(topichtml);
  });
  topicHandle();
}

function topicHandle() {
  $('.topics-appender').on('change', function () {
    genericAjax(currentActiveMenu, 'endpoint3', function (data) {
      currentContext = data.data;
      console.log('final context text', data);
    });
  });
}
function genericAjax(menuItem, level, callback, appendInUrl = null) {
  let url = appendInUrl ? menuItem[level] + '' + appendInUrl : menuItem[level];
  console.log('formed URl', menuItem[level]);
  $.ajax({
    url: url,
    method: menuItem.method,
    headers: menuItem.headers,
    success: function (data) {
      console.log(`Data fetched for ${menuItem}:`, level);
      // Update the item to indicate data has been fetched and store the data
      menuItem.dataFetched = true;
      menuItem.fetchedData = data;
      if (callback) callback(data);
    },
    error: function (xhr, status, error) {
      console.error(`Error fetching data for ${menuItem}:`, error);
    },
  });
}

//staff events
function grades_outcomes_staff_chip() {
  $('.grades_outcomes_staff_chip')
    .off('click')
    .on('click', function () {
      $('.grades_outcomes_staff_chip').removeClass('active');
      $(this).addClass('active');

      if ($(this).text() == 'Overall') {
        currentActiveMenu.fetchedData.data = overall_course_grades_data;
        $('.student-selector').hide();
      } else {
        $('.student-selector').show();
      }
    });
  handleStaffStudentSelection();
}

function handleStaffStudentSelection() {
  console.log('handle studnet selection');

  $('.students-appender')
    .off('change')
    .on('change', function () {
      let chosenStudentId = $(this).find(':selected').text();
      console.log('chosen student id ', chosenStudentId);
      genericAjax(
        currentActiveMenu,
        'endpoint2',
        function (data) {
          currentActiveMenu.fetchedData.data = data.data;
          console.log('handleStaffStudentSelection', data);
        },
        chosenStudentId
      );
    });
}
