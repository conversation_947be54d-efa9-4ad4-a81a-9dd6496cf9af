<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Face Photo Capture with MediaPipe</title>
		<link
			rel="stylesheet"
			href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
		/>

		<style>
			#camera {
				width: 100%;
				max-width: 500px;
				margin: auto;
			}
			.preview-box {
				width: 100px;
				height: 100px;
				margin: 5px;
				overflow: hidden;
				border: 2px solid #ddd;
			}
			.preview-box img {
				width: 100%;
				/* height: auto; */
				height: 100px;
			}
			.oval-image {
				position: absolute;
			}
		</style>
	</head>
	<body>
		<div class="container text-center mt-5">
			<h2>Face Capture Tool</h2>
			<p>Follow instructions to capture the required face positions.</p>

			<div id="camera-container" class="mb-3">
				<img src="./images/oval.png" alt="Oval shape" class="oval-image" />
				<video id="camera" autoplay></video>
			</div>

			<button class="btn btn-primary" onclick="captureImage()">Capture</button>
			<p id="instruction" class="mt-2">
				Position 1: Please look straight at the camera
			</p>

			<div id="preview" class="d-flex justify-content-center mt-1">
				<div class="preview-box" id="img1">
					<img src="./images/straight.svg" alt="Straight Image" />
				</div>
				<div class="preview-box" id="img2">
					<img src="./images/left.svg" alt="Left Image" />
				</div>
				<div class="preview-box" id="img3">
					<img src="./images/right.svg" alt="Right Image" />
				</div>
				<div class="preview-box" id="img4">
					<img src="./images/up.svg" alt="Up Image" />
				</div>
			</div>
		</div>

		<script type="module">
			import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
			const { FaceLandmarker, FilesetResolver, DrawingUtils } = vision;

			let faceLandmarker;
			let video = document.getElementById('camera');
			let currentStep = 1;
			const instructions = [
				'Position 1: Please look straight at the camera',
				'Position 2: Please turn to your left',
				'Position 3: Please turn to your right',
				'Position 4: Please look straight down',
			];

			// Initialize MediaPipe FaceLandmarker
			async function initializeFaceLandmarker() {
				const filesetResolver = await FilesetResolver.forVisionTasks(
					'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
				);
				faceLandmarker = await FaceLandmarker.createFromOptions(
					filesetResolver,
					{
						baseOptions: {
							modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
						},
						runningMode: 'video',
						numFaces: 1,
					}
				);
			}

			// Start video stream
			async function startVideo() {
				const stream = await navigator.mediaDevices.getUserMedia({
					video: true,
				});
				video.srcObject = stream;
			}

			// function getFaceOrientation(landmarks) {
			// 	// Example indices for key landmarks
			// 	const leftEyeIndex = 33; // Left eye outer corner
			// 	const rightEyeIndex = 263; // Right eye outer corner
			// 	const noseIndex = 1; // Nose tip
			// 	const chinIndex = 152; // Chin bottom

			// 	const leftEye = landmarks[leftEyeIndex];
			// 	const rightEye = landmarks[rightEyeIndex];
			// 	const nose = landmarks[noseIndex];
			// 	const chin = landmarks[chinIndex];

			// 	// Calculate vertical distance between eyes and chin
			// 	const eyeCenterY = (leftEye.y + rightEye.y) / 2;
			// 	const eyeChinDistance = chin.y - eyeCenterY;

			// 	// Calculate distance between the eyes (to normalize measurements)
			// 	const eyeDistanceX = Math.abs(leftEye.x - rightEye.x);

			// 	// Thresholds
			// 	const downThreshold = 0.01 * eyeDistanceX; // Adjust based on testing
			// 	const noseToEyeRatio = (nose.y - eyeCenterY) / eyeChinDistance;

			// 	// Orientation logic
			// 	if (
			// 		eyeChinDistance < downThreshold &&
			// 		Math.abs(nose.x - 0.5) < 0.05 &&
			// 		noseToEyeRatio < 0.5
			// 	) {
			// 		return 'straight';
			// 	} else if (eyeChinDistance >= downThreshold) {
			// 		return 'down';
			// 	} else if (nose.x < 0.4) {
			// 		return 'right';
			// 	} else if (nose.x > 0.6) {
			// 		return 'left';
			// 	} else {
			// 		return 'unknown';
			// 	}
			// }

			function getFaceOrientation(landmarks) {
				// Example landmark indices for key points (these may vary based on the MediaPipe model)
				const leftEyeIndex = 33; // Left eye outer corner
				const rightEyeIndex = 263; // Right eye outer corner
				const noseIndex = 1; // Nose tip
				const chinIndex = 152; // Chin bottom

				const leftEye = landmarks[leftEyeIndex];
				const rightEye = landmarks[rightEyeIndex];
				const nose = landmarks[noseIndex];
				const chin = landmarks[chinIndex];

				// Calculate distances and positions
				const eyeDistanceY = Math.abs(leftEye.y - rightEye.y);
				const noseXPosition = nose.x;
				const eyeCenterY = (leftEye.y + rightEye.y) / 2;
				console.log({
					eyeDistanceY,
					chiny: chin.y,
					eyeCenterY,
					chin: eyeCenterY - 0.1,
				});
				// Determine orientation
				if (eyeDistanceY < 0.02 && Math.abs(nose.x - 0.5) < 0.05) {
					return 'straight';
				} else if (noseXPosition < 0.4) {
					return 'right';
				} else if (noseXPosition > 0.6) {
					return 'left';
				} else if (chin.y > eyeCenterY - 0.1) {
					return 'up';
				}
				// else if (chin.y > eyeCenterY + 0.1) {
				// 	return 'down';
				// }
				else {
					return 'unknown';
				}
			}

			// Capture and process the image
			async function captureImage() {
				const canvas = document.createElement('canvas');
				canvas.width = video.videoWidth;
				canvas.height = video.videoHeight;
				const context = canvas.getContext('2d');
				context.drawImage(video, 0, 0, canvas.width, canvas.height);

				// Run face landmark detection
				const detections = await faceLandmarker.detectForVideo(
					video,
					performance.now()
				);
				if (detections && detections.faceLandmarks.length > 0) {
					const landmarks = detections.faceLandmarks[0]; // Get landmarks for the first detected face

					// Check the face orientation
					const orientation = getFaceOrientation(landmarks);
					console.log('Detected orientation:', orientation, currentStep);
					if (currentStep === 1 && orientation !== 'straight') {
						alert(
							'Face not detected, please adjust your position as straight!'
						);
						return;
					} else if (currentStep === 2 && orientation !== 'left') {
						alert('Face not detected, please adjust your position as left!');
						return;
					} else if (currentStep === 3 && orientation !== 'right') {
						alert('Face not detected, please adjust your position as right!');
						return;
					} else if (
						currentStep === 4 &&
						!['straight', 'up'].includes(orientation)
					) {
						alert('Face not detected, please adjust your position as up!');
						return;
					}

					// Calculate bounding box from landmarks
					const xs = landmarks.map((point) => point.x * canvas.width);
					const ys = landmarks.map((point) => point.y * canvas.height);
					let minX = Math.min(...xs);
					let minY = Math.min(...ys);
					let maxX = Math.max(...xs);
					let maxY = Math.max(...ys);
					let faceWidth = maxX - minX;
					let faceHeight = maxY - minY;

					// Define padding as a percentage of face dimensions
					const padding = 0.4; // 20% padding around the face
					minX -= faceWidth * padding;
					minY -= faceHeight * padding;
					faceWidth += faceWidth * padding * 2;
					faceHeight += faceHeight * padding * 2;

					// Ensure boundaries are within the image
					minX = Math.max(0, minX);
					minY = Math.max(0, minY);
					faceWidth = Math.min(canvas.width - minX, faceWidth);
					faceHeight = Math.min(canvas.height - minY, faceHeight);

					// Create a cropped canvas for the face
					const croppedCanvas = document.createElement('canvas');
					croppedCanvas.width = faceWidth;
					croppedCanvas.height = faceHeight;
					croppedCanvas
						.getContext('2d')
						.drawImage(
							canvas,
							minX,
							minY,
							faceWidth,
							faceHeight,
							0,
							0,
							faceWidth,
							faceHeight
						);

					// Show the cropped face in the preview box
					document.getElementById(
						`img${currentStep}`
					).innerHTML = `<img src="${croppedCanvas.toDataURL()}" alt="Captured Image">`;

					// Update instructions for the next step
					currentStep++;
					if (currentStep <= instructions.length) {
						document.getElementById('instruction').innerText =
							instructions[currentStep - 1];
					} else {
						document.getElementById('instruction').innerText =
							'All photos captured!';
					}
				} else {
					alert('Face not detected, please adjust your position!');
				}
			}

			// Initialize everything when the page loads
			window.onload = async () => {
				await initializeFaceLandmarker();
				await startVideo();
				console.log('FaceLandmarker initialized and video started');
			};
			window.captureImage = captureImage;
		</script>
	</body>
</html>
