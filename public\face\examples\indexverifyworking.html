<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Face Photo Capture with MediaPipe</title>
		<link
			rel="stylesheet"
			href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
		/>
		<script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
		<script src="./js/face-api.js"></script>
		<style>
			#camera {
				width: 100%;
				max-width: 500px;
				margin: auto;
			}
			.preview-box {
				width: 100px;
				height: 100px;
				margin: 5px;
				overflow: hidden;
				border: 2px solid #ddd;
			}
			.preview-box img {
				width: 100%;
				height: 100px;
			}
			.oval-image {
				position: absolute;
			}
		</style>
	</head>
	<body>
		<div class="container text-center mt-5">
			<h2>Face Capture Tool</h2>
			<p>Follow instructions to capture the required face positions.</p>

			<div id="camera-container" class="mb-3">
				<img src="./images/oval.png" alt="Oval shape" class="oval-image" />
				<video id="camera" autoplay></video>
			</div>

			<button class="btn btn-primary" onclick="startCapture()">
				Start Capture
			</button>
			<p id="instruction" class="mt-2">
				Position 1: Please look straight at the camera
			</p>

			<div id="preview" class="d-flex justify-content-center mt-1">
				<div class="preview-box" id="img1">
					<img src="./images/straight.svg" alt="Straight Image" />
				</div>
				<div class="preview-box" id="img2">
					<img src="./images/left.svg" alt="Left Image" />
				</div>
				<div class="preview-box" id="img3">
					<img src="./images/right.svg" alt="Right Image" />
				</div>
				<div class="preview-box" id="img4">
					<img src="./images/up.svg" alt="Up Image" />
				</div>
			</div>
		</div>

		<script type="module">
			import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
			const { FaceLandmarker, FilesetResolver } = vision;
			const SIMILARITY_THRESHOLD = 0.1; // Define an appropriate threshold based on testing
			let faceLandmarker;
			let video = document.getElementById('camera');
			let currentStep = 1;
			const instructions = [
				'Position 1: Please look straight at the camera',
				'Position 2: Please turn to your left',
				'Position 3: Please turn to your right',
				'Position 4: Please look straight down',
			];

			// Initialize MediaPipe FaceLandmarker
			async function initializeFaceLandmarker() {
				const filesetResolver = await FilesetResolver.forVisionTasks(
					'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
				);
				faceLandmarker = await FaceLandmarker.createFromOptions(
					filesetResolver,
					{
						baseOptions: {
							modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
						},
						runningMode: 'video',
						numFaces: 1,
					}
				);
			}

			async function startVideo() {
				const stream = await navigator.mediaDevices.getUserMedia({
					video: true,
				});
				video.srcObject = stream;
			}

			function updateInstruction() {
				if (currentStep <= instructions.length) {
					document.getElementById('instruction').innerText =
						instructions[currentStep - 1];
				} else {
					document.getElementById('instruction').innerText =
						'All photos captured!';
					registerFacial();
				}
			}

			function getFaceOrientation(landmarks) {
				const leftEyeIndex = 33,
					rightEyeIndex = 263,
					noseIndex = 1,
					chinIndex = 152;

				const leftEye = landmarks[leftEyeIndex],
					rightEye = landmarks[rightEyeIndex];
				const nose = landmarks[noseIndex],
					chin = landmarks[chinIndex];

				const eyeDistanceY = Math.abs(leftEye.y - rightEye.y);
				const noseXPosition = nose.x;
				const eyeCenterY = (leftEye.y + rightEye.y) / 2;

				if (eyeDistanceY < 0.03 && Math.abs(nose.x - 0.5) < 0.05)
					return 'straight';
				else if (noseXPosition < 0.4) return 'right';
				else if (noseXPosition > 0.6) return 'left';
				else if (chin.y > eyeCenterY - 0.1) return 'up';
				else return 'unknown';
			}

			async function capturePosition(orientation) {
				const canvas = document.createElement('canvas');
				canvas.width = video.videoWidth;
				canvas.height = video.videoHeight;
				const context = canvas.getContext('2d');
				context.filter = 'brightness(1.2) contrast(1.2)';
				context.drawImage(video, 0, 0, canvas.width, canvas.height);

				const detections = await faceLandmarker.detectForVideo(
					video,
					performance.now()
				);
				console.log({ detections });

				// Check if detections are found
				if (detections && detections.faceLandmarks.length > 0) {
					const landmarks = detections.faceLandmarks[0];
					const detectedOrientation = getFaceOrientation(landmarks);

					console.log({ detectedOrientation, orientation });

					// If the detected orientation matches the required one, capture the image
					if (detectedOrientation === orientation) {
						// Calculate bounding box from landmarks
						const xs = landmarks.map((point) => point.x * canvas.width);
						const ys = landmarks.map((point) => point.y * canvas.height);
						let minX = Math.min(...xs);
						let minY = Math.min(...ys);
						let maxX = Math.max(...xs);
						let maxY = Math.max(...ys);
						let faceWidth = maxX - minX;
						let faceHeight = maxY - minY;

						// Define padding as a percentage of face dimensions
						const padding = 0.4; // 20% padding around the face
						minX -= faceWidth * padding;
						minY -= faceHeight * padding;
						faceWidth += faceWidth * padding * 2;
						faceHeight += faceHeight * padding * 2;

						// Ensure boundaries are within the image
						minX = Math.max(0, minX);
						minY = Math.max(0, minY);
						faceWidth = Math.min(canvas.width - minX, faceWidth);
						faceHeight = Math.min(canvas.height - minY, faceHeight);

						// Create a cropped canvas for the face
						const croppedCanvas = document.createElement('canvas');
						croppedCanvas.width = faceWidth;
						croppedCanvas.height = faceHeight;
						croppedCanvas
							.getContext('2d')
							.drawImage(
								canvas,
								minX,
								minY,
								faceWidth,
								faceHeight,
								0,
								0,
								faceWidth,
								faceHeight
							);

						const croppedImage = new Image();
						croppedImage.src = croppedCanvas.toDataURL();
						await croppedImage.decode();
						// console.log(croppedImage);
						const detectionsFace = await faceapi
							.detectSingleFace(
								croppedImage
								// new faceapi.TinyFaceDetectorOptions()
							)
							.withFaceLandmarks()
							.withFaceDescriptor();
						console.log(detectionsFace);
						if (detectionsFace) {
							document.getElementById(
								`img${currentStep}`
							).innerHTML = `<img src="${croppedImage.src}" alt="Captured Image">`;
							currentStep++;
							updateInstruction();
							countdownAndCapture(); // Move to the next position countdown
						} else {
							document.getElementById('instruction').innerText =
								'Different faces detected or face i not clear. Please ensure the same person is captured with correct orientation';
							countdownAndCapture();
						}
					} else {
						// Retry if orientation does not match
						setTimeout(() => capturePosition(orientation), 1000);
					}
				} else {
					// Retry if no detections were found
					console.log('No face detected, retrying...');
					setTimeout(() => capturePosition(orientation), 1000);
				}
			}

			// Helper function to calculate similarity score (Euclidean distance or another metric)
			function compareFeatureVectors(vector1, vector2) {
				// Calculate similarity (e.g., using Euclidean distance)
				return Math.sqrt(
					vector1.reduce((sum, point, i) => {
						return (
							sum +
							Math.pow(point[0] - vector2[i][0], 2) +
							Math.pow(point[1] - vector2[i][1], 2)
						);
					}, 0)
				);
			}

			function countdownAndCapture() {
				let countdown = 3;
				const countdownInterval = setInterval(() => {
					if (countdown > 0) {
						if (currentStep > 4) {
							clearInterval(countdownInterval);
							document.getElementById('instruction').innerText = ``;
							return;
						}
						document.getElementById(
							'instruction'
						).innerText = `Capturing in ${countdown}...`;
						countdown--;
					} else {
						clearInterval(countdownInterval);
						console.log({ countdownInterval, currentStep });
						const findStep = ['straight', 'left', 'right', 'up'][
							currentStep - 1
						];
						if (findStep) {
							capturePosition(findStep);
						} else {
							currentStep = 1;
							clearInterval(countdownInterval);
							document.getElementById('instruction').innerText = ``;
							return;
						}
					}
				}, 1000);
			}

			function startCapture() {
				currentStep = 1;
				updateInstruction();
				countdownAndCapture();
			}

			function dString(name) {
				if (name !== '') {
					return window.atob(name);
				}
				return '';
			}

			function dataURLtoFile(dataurl, filename) {
				var arr = dataurl.split(','),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = dString(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				return new File([u8arr], filename, { type: mime });
			}

			function registerFacial() {
				const centerImgElement = document.querySelector('#img1 img');
				const centerImgSrc = centerImgElement.getAttribute('src');

				const leftImgElement = document.querySelector('#img2 img');
				const leftImgSrc = leftImgElement.getAttribute('src');

				const rightImgElement = document.querySelector('#img3 img');
				const rightImgSrc = rightImgElement.getAttribute('src');

				const upImgElement = document.querySelector('#img4 img');
				const upImgSrc = upImgElement.getAttribute('src');

				const center = dataURLtoFile(centerImgSrc, 'center.png');
				const left = dataURLtoFile(leftImgSrc, 'left.png');
				const right = dataURLtoFile(rightImgSrc, 'right.png');
				const up = dataURLtoFile(upImgSrc, 'up.png');

				let formData = new FormData();
				formData.append('_id', '6730cc0ae88b8edfd5064493');
				formData.append('center', center);
				formData.append('left', left);
				formData.append('right', right);
				formData.append('up', up);

				const URL =
					'https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/user/user_face_registration';
				$.ajax({
					type: 'POST',
					url: URL,
					data: formData,
					cache: false,
					processData: false,
					contentType: false,
					headers: {
						'Access-Control-Allow-Origin': '*',
						authorization:
							'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkaWdpcHJvZHVjdHNhZG1pbiIsImlhdCI6MTczMTMwNjg4NywiZXhwIjoxNzMxMzI0ODg3fQ.WnX1XxhlMfPrfOW4c6Ta33-q7Sc1ORX1k49p9Bkxcuc',
					},
					success: function (data) {
						console.log(data);
						if (data.success == true) {
							console.log('reg success');
						} else {
							console.log('reg failed');
						}
					},
					error: function () {
						console.log('facial request failed');
					},
				});
			}

			async function loadLibraries() {
				const MODEL_URL = 'models';
				await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
				await faceapi.loadFaceLandmarkModel(MODEL_URL);
				await faceapi.loadFaceRecognitionModel(MODEL_URL);
				// await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
				console.log('load facial Libraries done');
			}

			// Initialize everything when the page loads
			window.onload = async () => {
				await initializeFaceLandmarker();
				await loadLibraries();
				await startVideo();
				console.log('FaceLandmarker initialized and video started');
			};

			window.startCapture = startCapture;
		</script>
	</body>
</html>

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Face Photo Capture with MediaPipe</title>
		<link
			rel="stylesheet"
			href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
		/>

		<style>
			#camera {
				width: 100%;
				max-width: 500px;
				margin: auto;
			}
			.preview-box {
				width: 100px;
				height: 100px;
				margin: 5px;
				overflow: hidden;
				border: 2px solid #ddd;
			}
			.preview-box img {
				width: 100%;
				/* height: auto; */
				height: 100px;
			}
			.oval-image {
				position: absolute;
			}
		</style>
		<script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
		<script src="./js/face-api.js"></script>
	</head>
	<body>
		<div class="container text-center mt-5">
			<h2>Face Verification Tool</h2>

			<div id="camera-container" class="mb-3">
				<img src="./images/oval.png" alt="Oval shape" class="oval-image" />
				<video id="camera" autoplay></video>
			</div>

			<button class="btn btn-primary" onclick="captureImage()">Capture</button>
			<p id="resultMessage" class="mt-2"></p>
		</div>

		<script type="module">
			import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
			const { FaceLandmarker, FilesetResolver, DrawingUtils } = vision;
			let uploadedDescriptors = [];
			let faceLandmarker;
			let video = document.getElementById('camera');

			// Initialize MediaPipe FaceLandmarker
			async function initializeFaceLandmarker() {
				const filesetResolver = await FilesetResolver.forVisionTasks(
					'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
				);
				faceLandmarker = await FaceLandmarker.createFromOptions(
					filesetResolver,
					{
						baseOptions: {
							modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
						},
						runningMode: 'video',
						numFaces: 1,
					}
				);
			}

			// Start video stream
			async function startVideo() {
				const stream = await navigator.mediaDevices.getUserMedia({
					video: true,
				});
				video.srcObject = stream;
			}
			// Capture and process the image

			async function capturePosition() {
				const canvas = document.createElement('canvas');
				canvas.width = video.videoWidth;
				canvas.height = video.videoHeight;
				const context = canvas.getContext('2d');
				context.filter = 'brightness(1.2) contrast(1.2)';
				context.drawImage(video, 0, 0, canvas.width, canvas.height);

				// Run face landmark detection
				const detections = await faceLandmarker.detectForVideo(
					video,
					performance.now()
				);
				if (detections && detections.faceLandmarks.length > 0) {
					const landmarks = detections.faceLandmarks[0]; // Get landmarks for the first detected face

					// Calculate bounding box from landmarks
					const xs = landmarks.map((point) => point.x * canvas.width);
					const ys = landmarks.map((point) => point.y * canvas.height);
					let minX = Math.min(...xs);
					let minY = Math.min(...ys);
					let maxX = Math.max(...xs);
					let maxY = Math.max(...ys);
					let faceWidth = maxX - minX;
					let faceHeight = maxY - minY;

					// Define padding as a percentage of face dimensions
					const padding = 0.4; // 20% padding around the face
					minX -= faceWidth * padding;
					minY -= faceHeight * padding;
					faceWidth += faceWidth * padding * 2;
					faceHeight += faceHeight * padding * 2;

					// Ensure boundaries are within the image
					minX = Math.max(0, minX);
					minY = Math.max(0, minY);
					faceWidth = Math.min(canvas.width - minX, faceWidth);
					faceHeight = Math.min(canvas.height - minY, faceHeight);

					// Create a cropped canvas for the face
					const croppedCanvas = document.createElement('canvas');
					croppedCanvas.width = faceWidth;
					croppedCanvas.height = faceHeight;
					croppedCanvas
						.getContext('2d')
						.drawImage(
							canvas,
							minX,
							minY,
							faceWidth,
							faceHeight,
							0,
							0,
							faceWidth,
							faceHeight
						);

					// Convert cropped canvas to an image for detection
					const croppedImage = new Image();
					croppedImage.src = croppedCanvas.toDataURL();
					await croppedImage.decode(); // Ensure the image is fully loaded

					// Detect face descriptor from the cropped image
					const detectionsFace = await faceapi
						.detectSingleFace(
							croppedImage
							// new faceapi.TinyFaceDetectorOptions()
						)
						.withFaceLandmarks()
						.withFaceDescriptor();
					console.log(detectionsFace);
					if (detectionsFace) {
						console.log('Face descriptor:', detectionsFace.descriptor);
						const capturedDescriptor = detectionsFace.descriptor;
						const matchFound = compareDescriptors(capturedDescriptor);
						console.log(matchFound);

						if (matchFound) {
							document.getElementById('resultMessage').textContent =
								'Success! Match found.';
						} else {
							document.getElementById('resultMessage').textContent =
								'No match found. Try again.';
							await countdownAndCapture();
						}
					} else {
						document.getElementById('resultMessage').textContent =
							'No face detected in cropped image.';
						await countdownAndCapture();
					}
				} else {
					document.getElementById('resultMessage').textContent =
						'Face not detected, please adjust your position!';
				}
			}

			async function countdownAndCapture() {
				let countdown = 3;
				const countdownInterval = setInterval(() => {
					if (countdown > 0) {
						document.getElementById(
							'resultMessage'
						).innerText = `Capturing in ${countdown}...`;
						countdown--;
					} else {
						clearInterval(countdownInterval);
						capturePosition();
					}
				}, 1000);
			}

			async function captureImage() {
				await countdownAndCapture();
			}

			function compareDescriptors(capturedDescriptor) {
				let threshold = 0.6; // Define a threshold for matching
				let matchCount = 0;

				uploadedDescriptors.forEach((descriptor) => {
					const distance = faceapi.euclideanDistance(
						capturedDescriptor,
						descriptor
					);
					if (distance < threshold) {
						matchCount++;
					}
				});

				const averageDistance = matchCount / uploadedDescriptors.length;
				return averageDistance >= 0.5 || matchCount > 0;
			}

			// Initialize everything when the page loads
			window.onload = async () => {
				await initializeFaceLandmarker();
				await startVideo();
				console.log('FaceLandmarker initialized and video started');
			};
			window.captureImage = captureImage;

			const daAuthAppUrl =
				'https://auth-staging.gcp.digivalitsolutions.com/api/v0/auth/facial-labeled-descriptors?employeeOrAcademicId=';

			function loadSingleDescfromDb(acadamicId) {
				console.log('load from db called');
				$.ajax({
					url: daAuthAppUrl + acadamicId,
					type: 'GET',
					dataType: 'json', // added data type
					success: function (res) {
						console.log('Vaaa', res.data.descriptors);
						uploadedDescriptors = res.data.descriptors;
					},
					error: function (err) {
						console.log('error occured load from db', err);
					},
				});
			}
			console.log({ uploadedDescriptors });
			async function loadLibraries() {
				const MODEL_URL = 'models';
				await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
				await faceapi.loadFaceLandmarkModel(MODEL_URL);
				await faceapi.loadFaceRecognitionModel(MODEL_URL);
				// await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
				console.log('load facial Libraries done');
			}

			$(document).ready(async function () {
				console.log('webcam is ready');
				await loadLibraries();
				loadSingleDescfromDb('D0033A');
				// trainAndVerify();
			});
		</script>
	</body>
</html>
