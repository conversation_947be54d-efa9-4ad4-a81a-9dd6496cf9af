function _0x126e() {
  var _0x404158 = [
    'removeItem',
    'apiKey',
    '@#MSK0033#@',
    'leaveUrl',
    'toString',
    '195fsjyGW',
    '55GwPLAN',
    'isMobileDevice',
    'join',
    '72NiPirX',
    'parseQuery',
    'currentUser',
    'enc',
    'success\x20getCurrentUser',
    '165351FFjPKb',
    'passWord',
    '_lib_1',
    'reload',
    'onUserLeave',
    'inMeetingServiceListener\x20onUserLeave',
    '862056ipiAkI',
    'log',
    'getAttendeeslist',
    'inMeetingServiceListener\x20onMeetingStatus',
    'meetingNumber',
    'DOMContentLoaded',
    'onMeetingStatus',
    'Utf8',
    'load',
    'addEventListener',
    'lang',
    'decrypt',
    'china',
    'inMeetingServiceListener\x20onUserIsInWaitingRoom',
    '1469167uReXlz',
    'prepareJssdk',
    '/zoom/leave.html?',
    'topic',
    '_lib',
    'parse',
    'loggedIn',
    'pagehide',
    '8vsUHEL',
    'inMeetingServiceListener',
    'https://jssdk.zoomus.cn/1.9.9/lib',
    '34808BIMbrh',
    'AES',
    'localStorage',
    'leaveMeeting',
    'inMeetingServiceListener\x20onUserJoin',
    'getItem',
    '439070GcoCsA',
    'serialize',
    'signature',
    '10278fLrmgt',
    'i18n',
    'preLoadWasm',
    'setItem',
    'testTool',
    'result',
    '243697qCNXGh',
    'getCurrentUser',
    'crossOriginIsolated',
    'setZoomJSLib',
  ];
  _0x126e = function () {
    return _0x404158;
  };
  return _0x126e();
}
function _0x111a(_0x6595c0, _0x10164d) {
  var _0x126e08 = _0x126e();
  return (
    (_0x111a = function (_0x111a4d, _0x3add56) {
      _0x111a4d = _0x111a4d - 0x1eb;
      var _0x505f1e = _0x126e08[_0x111a4d];
      return _0x505f1e;
    }),
    _0x111a(_0x6595c0, _0x10164d)
  );
}
var _0x2b9e13 = _0x111a;
(function (_0x20a8ad, _0x3060ea) {
  var _0x10901d = _0x111a,
    _0x3805df = _0x20a8ad();
  while (!![]) {
    try {
      var _0x986a77 =
        parseInt(_0x10901d(0x223)) / 0x1 +
        (-parseInt(_0x10901d(0x211)) / 0x2) *
          (parseInt(_0x10901d(0x1f5)) / 0x3) +
        parseInt(_0x10901d(0x1fb)) / 0x4 +
        (-parseInt(_0x10901d(0x1ec)) / 0x5) *
          (parseInt(_0x10901d(0x21d)) / 0x6) +
        parseInt(_0x10901d(0x209)) / 0x7 +
        (parseInt(_0x10901d(0x214)) / 0x8) *
          (-parseInt(_0x10901d(0x1f0)) / 0x9) +
        (parseInt(_0x10901d(0x21a)) / 0xa) *
          (-parseInt(_0x10901d(0x1ed)) / 0xb);
      if (_0x986a77 === _0x3060ea) break;
      else _0x3805df['push'](_0x3805df['shift']());
    } catch (_0x41d964) {
      _0x3805df['push'](_0x3805df['shift']());
    }
  }
})(_0x126e, 0x1f1f2),
  window[_0x2b9e13(0x204)](_0x2b9e13(0x200), function (_0x465d0f) {
    var _0x3798e2 = _0x2b9e13;
    const _0x164de7 = localStorage['getItem']('_lib');
    _0x164de7 !== null &&
      (window[_0x3798e2(0x216)][_0x3798e2(0x220)](_0x3798e2(0x20f), 'true'),
      websdkready());
  });
function websdkready() {
  var _0x2db95d = _0x2b9e13;
  const _0x39bd53 = localStorage[_0x2db95d(0x219)](_0x2db95d(0x20d)),
    _0x3eb6d2 = localStorage[_0x2db95d(0x219)](_0x2db95d(0x1f7));
  var _0x384723 = CryptoJS['AES'][_0x2db95d(0x206)](
      _0x39bd53[_0x2db95d(0x1eb)](),
      _0x2db95d(0x229)
    ),
    _0x5071ef = JSON[_0x2db95d(0x20e)](
      _0x384723[_0x2db95d(0x1eb)](CryptoJS[_0x2db95d(0x1f3)][_0x2db95d(0x202)])
    ),
    _0x5ebcc9 = CryptoJS[_0x2db95d(0x215)][_0x2db95d(0x206)](
      _0x3eb6d2[_0x2db95d(0x1eb)](),
      _0x2db95d(0x229)
    ),
    _0x3094dc = JSON[_0x2db95d(0x20e)](
      _0x5ebcc9[_0x2db95d(0x1eb)](CryptoJS['enc'][_0x2db95d(0x202)])
    ),
    _0x3e6db4 = window[_0x2db95d(0x221)],
    _0x50fe9c = _0x3e6db4[_0x2db95d(0x1f1)](),
    _0x3c2085 = {
      apiKey: _0x5071ef[0x3]['d'],
      meetingNumber: _0x5071ef[0x5]['f'],
      userName: _0x5071ef[0x1]['b'],
      passWord: _0x5071ef[0x6]['g'],
      leaveUrl: _0x2db95d(0x20b) + _0x3e6db4[_0x2db95d(0x21b)](_0x50fe9c),
      role: parseInt(0x0, 0xa),
      userEmail: _0x5071ef[0x2]['c'],
      lang: 'en',
      signature: _0x3094dc[0x0]['s'] || '',
      china: ![],
    };
  _0x3e6db4[_0x2db95d(0x1ee)]() && (vConsole = new VConsole());
  if (_0x3c2085[_0x2db95d(0x207)])
    ZoomMtg[_0x2db95d(0x226)](_0x2db95d(0x213), '/av');
  ZoomMtg[_0x2db95d(0x21f)](), ZoomMtg[_0x2db95d(0x20a)]();
  function _0x4f34d5(_0x2188c0) {
    var _0x50fa5c = _0x2db95d;
    ZoomMtg['init']({
      leaveUrl: _0x3c2085[_0x50fa5c(0x22a)],
      webEndpoint: _0x3c2085['webEndpoint'],
      disableCORP: !window[_0x50fa5c(0x225)],
      isSupportAV: !![],
      disableInvite: !![],
      meetingInfo: [_0x50fa5c(0x20c), 'host'],
      success: function () {
        var _0x3e8ee7 = _0x50fa5c;
        ZoomMtg[_0x3e8ee7(0x21e)][_0x3e8ee7(0x203)](
          _0x3c2085[_0x3e8ee7(0x205)]
        ),
          ZoomMtg[_0x3e8ee7(0x21e)][_0x3e8ee7(0x1f8)](
            _0x3c2085[_0x3e8ee7(0x205)]
          ),
          ZoomMtg[_0x3e8ee7(0x1ef)]({
            meetingNumber: _0x3c2085[_0x3e8ee7(0x1ff)],
            userName: _0x3c2085['userName'],
            signature: _0x2188c0,
            apiKey: _0x3c2085[_0x3e8ee7(0x228)],
            userEmail: _0x3c2085['userEmail'],
            passWord: _0x3c2085[_0x3e8ee7(0x1f6)],
            success: function (_0x7c7283) {
              var _0x2b5079 = _0x3e8ee7;
              ZoomMtg[_0x2b5079(0x1fd)]({}),
                ZoomMtg[_0x2b5079(0x224)]({
                  success: function (_0xd33f1a) {
                    var _0x202ba6 = _0x2b5079;
                    console[_0x202ba6(0x1fc)](
                      _0x202ba6(0x1f4),
                      _0xd33f1a[_0x202ba6(0x222)][_0x202ba6(0x1f2)]
                    );
                  },
                });
            },
            error: function (_0x175713) {
              var _0x3c0bcc = _0x3e8ee7;
              console[_0x3c0bcc(0x1fc)](_0x175713);
            },
          });
      },
      error: function (_0x509508) {
        var _0x2d6f0d = _0x50fa5c;
        console[_0x2d6f0d(0x1fc)](_0x509508);
      },
    }),
      ZoomMtg[_0x50fa5c(0x212)]('onUserJoin', function (_0x400b9d) {
        var _0x170305 = _0x50fa5c;
        console['log'](_0x170305(0x218), _0x400b9d);
      }),
      ZoomMtg[_0x50fa5c(0x212)](_0x50fa5c(0x1f9), function (_0x462ac1) {
        var _0x19969f = _0x50fa5c;
        console[_0x19969f(0x1fc)](_0x19969f(0x1fa), _0x462ac1);
      }),
      ZoomMtg['inMeetingServiceListener'](
        'onUserIsInWaitingRoom',
        function (_0x3829b3) {
          var _0x3ac7c4 = _0x50fa5c;
          console[_0x3ac7c4(0x1fc)](_0x3ac7c4(0x208), _0x3829b3);
        }
      ),
      ZoomMtg['inMeetingServiceListener'](
        _0x50fa5c(0x201),
        function (_0x213bcd) {
          var _0x1eaa15 = _0x50fa5c;
          console['log'](_0x1eaa15(0x1fe), _0x213bcd);
        }
      );
  }
  _0x4f34d5(_0x3c2085[_0x2db95d(0x21c)]);
}
window[_0x2b9e13(0x204)](_0x2b9e13(0x210), function (_0x5d2d5a) {
  var _0x47ba7a = _0x2b9e13;
  _0x5d2d5a['preventDefault'](),
    window[_0x47ba7a(0x216)][_0x47ba7a(0x227)]('loggedIn'),
    ZoomMtg[_0x47ba7a(0x217)]({}),
    (_0x5d2d5a['returnValue'] = '');
});
