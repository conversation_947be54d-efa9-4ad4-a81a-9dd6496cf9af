if (annyang) {
  // Let's define our first command. First the text we expect, and then the function it should call

  var logSpokenWords = function (spokenText) {
    console.log('Spoken text:', spokenText);
    $('.rounded-input').val(spokenText);
    setTimeout(() => {
      $('.send ').trigger('click');
    }, 3000);
  };

  annyang.addCallback('result', function (phrases) {
    logSpokenWords(phrases[0]); // Log the most confident result
  });

  // Start listening. You can call this here, or attach this call to an event, button, etc.
  annyang.start();
}
