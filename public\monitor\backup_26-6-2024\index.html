<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SLA - Riyadh - Session & Exams Monitor</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script src="classes.js"></script>
    <script src="exams.js"></script>
</head>
<body>
    <header class="header bg-primary text-white text-center py-3">
        <div class="container">
            <h1>SLA - Riyadh - Live Monitoring: Sessions & Exams</h1>
            <div id="current-date-time" class="d-flex justify-content-center align-items-center">
                <span>Today: <span id="current-date"></span> | Current Time: <span id="current-time"></span></span>
                <span class="instruction-wrapper ml-2">(Dashboard updated every minute)</span>
                <i class="fas fa-cog settings-icon ml-2" onclick="openSettings()"></i>
            </div>
        </div>
    </header>
    <main class="container my-4">
        <div class="slider-container overflow-hidden">
            <div class="slider d-flex">
                <section class="slide w-50 p-3">
                    <h2>Session Status</h2>
                    <div id="classes-grid" class="status-grid row">
                        <!-- Classes will be loaded here from JSON -->
                    </div>
                </section>
                <section class="slide w-50 p-3">
                    <h2>Exams Status</h2>
                    <div id="exams-grid" class="status-grid row">
                        <!-- Exams will be loaded here from JSON -->
                    </div>
                </section>
            </div>
        </div>
    </main>
    <aside id="sidebar" class="sidebar">
        <div class="sidebar-content">
            <button class="close-btn" onclick="closeSidebar()">×</button>
            <div id="details-content"></div>
        </div>
    </aside>
    <aside id="settings-sidebar" class="sidebar">
        <div class="sidebar-content">
            <button class="close-btn" onclick="closeSettings()">×</button>
            <h2>Filter Settings</h2>
            <div class="filter-section">
                <h3>Show only (Sessions)</h3>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input class-filter" value="completed" id="completedCheck">
                    <label class="form-check-label" for="completedCheck">Completed</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input class-filter" value="missed" id="missedCheck">
                    <label class="form-check-label" for="missedCheck">Missed</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input class-filter" value="late" id="lateStartedCheck">
                    <label class="form-check-label" for="lateStartedCheck">Late Started</label>
                </div>
            </div>
            <div class="filter-section">
                <h3>Show only (Exams)</h3>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input exam-filter" value="late-reported" id="lateReportedCheck">
                    <label class="form-check-label" for="lateReportedCheck">Late Reported</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input exam-filter" value="extra-time" id="extraTimeCheck">
                    <label class="form-check-label" for="extraTimeCheck">Extra Time Given</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input exam-filter" value="not-started" id="notStartedCheck">
                    <label class="form-check-label" for="notStartedCheck">Not Started on Time</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input exam-filter" value="proctor-not-reported" id="proctorNotReportedCheck">
                    <label class="form-check-label" for="proctorNotReportedCheck">Proctor Not Reported on Time</label>
                </div>
            </div>
            <div class="filter-section">
                <h3>Time Filter</h3>
                <div class="form-check">
                    <input type="radio" class="form-check-input" name="time-filter" id="onTimeFilter" value="on-time">
                    <label class="form-check-label" for="onTimeFilter">On Time</label>
                </div>
                <div class="form-check">
                    <input type="radio" class="form-check-input" name="time-filter" id="lateFilter" value="late">
                    <label class="form-check-label" for="lateFilter">Late</label>
                </div>
            </div>
            <button class="btn btn-primary mt-3" onclick="applyClassFilter()">Apply</button>
        </div>
    </aside>
    <footer class="bg-primary text-white text-center py-2">
        <div class="container">
            <span>DigiClass I DigiAssess I Q360 I HEBA.ai I Integer I</span>
            <a class="site-link text-white ml-2" href="https://digi-val.com/">www.digi-val.com</a>
        </div>
    </footer>
</body>
</html>
