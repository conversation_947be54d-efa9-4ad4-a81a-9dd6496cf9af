import React from 'react';
import { createRoot } from 'react-dom/client';
import i18n from './i18n';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import ReactGA from 'react-ga4';
import { createBrowserHistory } from 'history';
import HelpWidget from 'Modules/DigivalHelpWidget/HelpWidget';
import reportWebVitals from './reportWebVitals';
import Store from './ReduxApi/store';
import App from './App';
// import './index.css';
import './index.scss';
// import CustomizeTheme from '../src/CustomizeTheme';

ReactGA.initialize(process.env.REACT_APP_GTAG_ID);

const history = createBrowserHistory();
history.listen((location) => {
  ReactGA.send({
    hitType: 'pageview',
    page: location.pathname,
  });
});
const publicPath = process.env.REACT_APP_BASE_PATH || '';
const container = document.getElementById('root');
const root = createRoot(container);
i18n.init(i18n.config, () => {
  root.render(
    // <React.StrictMode>
    <Provider store={Store}>
      <Router basename={publicPath} history={history}>
        <App />
      </Router>
      <HelpWidget />
      {/* <CustomizeTheme history={history} /> */}
    </Provider>
    // </React.StrictMode>
  );
});
// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
