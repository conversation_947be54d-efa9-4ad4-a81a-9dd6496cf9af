<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Task Manager</title>
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      body {
        background-color: #f7f8fa;
      }
      .task-manager {
        display: flex;
        height: 100vh;
      }
      .sidebar {
        width: 250px;
        background-color: #fff;
        padding: 20px;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
      }
      .content {
        flex: 1;
        padding: 20px;
      }
      .task-list {
        background-color: #fff;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
      .task-details {
        background-color: #fff;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
      }
      .task-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
      }
      .task-item:last-child {
        border-bottom: none;
      }
      .task-item.completed {
        text-decoration: line-through;
        color: #999;
      }
      .task-item .task-date {
        font-size: 0.8em;
        color: #999;
      }
    </style>
  </head>
  <body>
    <div class="task-manager">
      <div class="sidebar">
        <div class="profile text-center">
          <img
            src="https://via.placeholder.com/100"
            class="rounded-circle mb-3"
            alt="Natalie Smith"
          />
          <h5>Natalie Smith</h5>
          <p><EMAIL></p>
          <div class="progress mb-3" style="height: 5px">
            <div
              class="progress-bar"
              role="progressbar"
              style="width: 34%"
              aria-valuenow="34"
              aria-valuemin="0"
              aria-valuemax="100"
            ></div>
          </div>
          <p><strong>12</strong> Completed tasks</p>
          <p><strong>22</strong> To do tasks</p>
          <p><strong>243</strong> All completed</p>
        </div>
        <div class="projects mt-4">
          <h6>Projects</h6>
          <ul class="list-unstyled">
            <li><span class="badge badge-primary">Marketing</span></li>
            <li><span class="badge badge-secondary">Design</span></li>
            <li><span class="badge badge-success">Development</span></li>
            <li><span class="badge badge-danger">Management</span></li>
          </ul>
        </div>
        <div class="team mt-4">
          <h6>Team</h6>
          <ul class="list-inline">
            <li class="list-inline-item">
              <img
                src="https://via.placeholder.com/50"
                class="rounded-circle"
                alt="Team Member"
              />
            </li>
            <li class="list-inline-item">
              <img
                src="https://via.placeholder.com/50"
                class="rounded-circle"
                alt="Team Member"
              />
            </li>
            <li class="list-inline-item">
              <img
                src="https://via.placeholder.com/50"
                class="rounded-circle"
                alt="Team Member"
              />
            </li>
            <li class="list-inline-item">
              <img
                src="https://via.placeholder.com/50"
                class="rounded-circle"
                alt="Team Member"
              />
            </li>
          </ul>
        </div>
      </div>
      <div class="content">
        <div class="row">
          <div class="col-md-4">
            <div class="task-list">
              <h5>Marketing</h5>
              <div class="task-item">
                <input type="checkbox" checked />
                <span>Write an article about design</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Disrupt next level aesthetic raw</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Chicharrones craft beer tattooed</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Vaporware readymade shabby</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Four dollar toast taxidermy</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Slow-carb disrupt kogi tote bag</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Pour-over subway tile twee</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <div class="task-item">
                <input type="checkbox" />
                <span>Create AdWords campaign</span>
                <p class="task-date">22 Feb, 2019</p>
              </div>
              <button class="btn btn-primary btn-block mt-4">Add task</button>
            </div>
          </div>
          <div class="col-md-8">
            <div class="task-details">
              <h5>Write an article about design</h5>
              <p><strong>12 Mar, 2019</strong></p>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce
                accumsan commodo lectus gravida dictum. Aliquam a dui eu arcu
                hendrerit porta sed in velit. Fusce eu semper magna. Aenean
                porta facilisis neque, ac dignissim magna vestibulum eu. Etiam
                id ligula eget neque placerat ultricies in sed neque. Nam vitae
                rutrum est. Etiam non condimentum ante, eu consequat orci.
                Aliquam a dui eu arcu hendrerit porta sed in velit. Fusce eu
                semper magna.
              </p>
              <p><strong>History:</strong></p>
              <ul>
                <li>Okla Nowak assigned to Natalie Smith. 25 Nov, 2019</li>
                <li>Okla Nowak added to Marketing. 18 Feb, 2019</li>
                <li>Okla Nowak created task. 18 Feb, 2019</li>
              </ul>
              <p><strong>Attachments:</strong></p>
              <ul>
                <li><a href="#">Article.docx</a> added 17 May, 2020</li>
              </ul>
              <div class="mt-4">
                <textarea
                  class="form-control"
                  placeholder="Write a comment..."
                ></textarea>
                <button class="btn btn-primary mt-2 float-right">Send</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  </body>
</html>
