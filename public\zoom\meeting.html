<!DOCTYPE html>

<head>
    <title>Digiclass Zoom Video</title>
    <meta charset="utf-8" />
    <meta
      http-equiv="origin-trial"
      content="AshXcT2M31MweffHJGd0hOekOzpNUfeBdJU1nBvS1hpjj4mR8g3Vhi71KurCsS5LHTG0cj42UWb5TjcFQlxDww4AAACIeyJvcmlnaW4iOiJodHRwczovL2RpZ2ljbGFzcy5kaWdpdmFsc29sdXRpb25zLmNvbTo0NDMiLCJmZWF0dXJlIjoiVW5yZXN0cmljdGVkU2hhcmVkQXJyYXlCdWZmZXIiLCJleHBpcnkiOjE2Mzk1MjYzOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="
    />
    <link type="text/css" rel="stylesheet" href="https://source.zoom.us/1.9.9/css/bootstrap.css" />
    <link type="text/css" rel="stylesheet" href="https://source.zoom.us/1.9.9/css/react-select.css" />
    <meta name="format-detection" content="telephone=no">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta http-equiv="origin-trial" content="">
    <link rel="icon" href="../favicon.svg" />
    <link rel="apple-touch-icon" href="../favicon.svg" />
    <link rel="icon" href="../favicon.svg" />
    <style>
        button.meeting-info-icon__icon-wrap.ax-outline-blue,#app-signal {display: none !important;}
    </style>
</head>

<body>
    <script src="https://source.zoom.us/1.9.9/lib/vendor/react.min.js"></script>
    <script src="https://source.zoom.us/1.9.9/lib/vendor/react-dom.min.js"></script>
    <script src="https://source.zoom.us/1.9.9/lib/vendor/redux.min.js"></script>
    <script src="https://source.zoom.us/1.9.9/lib/vendor/redux-thunk.min.js"></script>
    <script src="https://source.zoom.us/1.9.9/lib/vendor/lodash.min.js"></script>
    <script src="https://source.zoom.us/zoom-meeting-1.9.9.min.js"></script>
    <script src="js/crypto.js"></script>
    <script src="js/common.js"></script>
    <script src="js/tool.js"></script>
    <script src="js/vconsole.min.js"></script>
    <script src="js/meeting.js"></script>   
</body>

</html>