{"editor.formatOnSave": true, "editor.renderWhitespace": "boundary", "editor.rulers": [100], "eslint.alwaysShowStatus": true, "git.autofetch": true, "editor.insertSpaces": true, "editor.tabSize": 2, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}