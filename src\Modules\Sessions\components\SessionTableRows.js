import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { List, Map } from 'immutable';
import { Table } from 'react-bootstrap';
import { getClassName, splitNameArray } from '../../Utils/utils';
import { getSessionRowClass, getSubjects } from './utils';
import SessionGroupCell from './SessionGroupCell';
import SessionStatusCell from './SessionStatusCell';
import InfraNameCell from './InfraNameCell';
import SessionTimingCell from './SessionTimingCell';
import SessionActionCell from './SessionActionCell';

const SessionTableRows = ({
  schedules,
  authData,
  userType,
  programId,
  onSessionClick,
}) => {
  const { t } = useTranslation();

  return (
    <Table hover className="session-table">
      <thead></thead>
      <tbody className={getClassName('tbody-arabic', '')}>
        {schedules.map((schedule, sIndex) => {
          const loggedStudentData =
            userType === 'student'
              ? schedule
                  .get('students', List())
                  .find(
                    (item) => item.get('_id') === authData.get('_id', '')
                  ) || Map()
              : Map();

          return (
            <tr
              className={getSessionRowClass(schedule, userType, authData)}
              key={sIndex}
            >
              <td
                className={getClassName(
                  'text-left-arabic',
                  'text-left',
                  'w-25'
                )}
              >
                <SessionGroupCell schedule={schedule} programId={programId} />
                <SessionStatusCell
                  schedule={schedule}
                  onSessionClick={onSessionClick}
                />
              </td>
              <td className="">
                <div className="f-14">
                  {getSubjects(schedule.get('subjects', List()))}
                </div>
              </td>
              <td className="w-10">
                <InfraNameCell schedule={schedule} programId={programId} />
              </td>
              <td className="w-15">
                <div className="f-14 text_overflow">
                  {schedule.get('infra_name', '') !== ''
                    ? splitNameArray(schedule.get('infra_name', ''), 'http', 0)
                    : t('none')}
                </div>
              </td>
              <td className="">
                <SessionTimingCell schedule={schedule} />
              </td>
              <td>
                <SessionActionCell
                  schedule={schedule}
                  authData={authData}
                  userType={userType}
                  loggedStudentData={loggedStudentData}
                  onSessionClick={onSessionClick}
                />
              </td>
            </tr>
          );
        })}
      </tbody>
    </Table>
  );
};

SessionTableRows.propTypes = {
  schedules: PropTypes.instanceOf(List).isRequired,
  authData: PropTypes.object.isRequired,
  userType: PropTypes.string.isRequired,
  programId: PropTypes.string.isRequired,
  onSessionClick: PropTypes.func.isRequired,
};

export default SessionTableRows;
