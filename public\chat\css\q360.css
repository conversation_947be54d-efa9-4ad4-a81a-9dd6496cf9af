.text-right {
  text-align: right;
  margin-right: 2.5em;
}

.text-right a {
  color: #147afc;
  text-decoration: underline;
  cursor: pointer;
}

.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 100; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0, 0, 0); /* Fallback color */
  background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
  overflow: 'scroll';
}

/* The Close Button */
.modal-close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.modal-close:hover,
.modal-close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.common-chip-q360 {
  width: 21.3% !important;
}

.select2-container {
  width: 95% !important;
  box-sizing: border-box;
}

.select2-selection--single {
  width: 100%;
  height: 55px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  padding: 14px 5px !important;
}

.select2-dropdown {
  width: auto !important;
  box-sizing: border-box;
  max-width: 100%;
}

body {
  overflow-x: hidden;
}

.box1 {
  padding-left: 15px;
  margin-top: 15px;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 55px !important;
  right: 15px !important;
}
.select2-container--open .select2-dropdown--below {
  width: 46% !important;
}

#loading-backdrop {
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #eff9fbde; /* Semi-transparent black */
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it appears on top of other elements */
}

/* Loading image style */
#loading-image {
  width: 100px;
  height: 100px;
}

div#preview-content {
  margin-bottom: 10em;
}

div#selected-form {
  position: absolute;
  right: 20px;
}

div#selected-form a {
  color: #147afc;
  text-decoration: underline;
  cursor: pointer;
}
