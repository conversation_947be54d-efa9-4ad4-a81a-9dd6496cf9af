import React from 'react';
import socketIOClient from 'socket.io-client';
import { SOCKET_URL } from '../constants';

export const Socket = socketIOClient.connect(SOCKET_URL, {
  transports: ['websocket'],
  reconnection: true, // Enable automatic reconnection
  reconnectionAttempts: 5, // Number of attempts before giving up
  reconnectionDelay: 1000, // Time between each reconnection attempt in milliseconds
  randomizationFactor: 0.5, // Randomization factor to prevent clients from synchronizing their reconnection attempts
});
export const SocketContext = React.createContext();
