import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Box,
  Chip,
  Card,
  CardContent,
  Typography,
  FormControl,
  Select,
  MenuItem,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Checkbox,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  selectCourseBasedStaffDetails,
  selectDeliveryBasedStaff,
} from 'ReduxApi/CourseSettings/selector';
import { useDispatch, useSelector } from 'react-redux';
import { getCourseStudentsList } from 'ReduxApi/Sessions/action';
import {
  getCourseBasedStaffDetails,
  getDeliveryBasedStaff,
  updateCourseWiseDeliveryType,
} from 'ReduxApi/CourseSettings/action';
import useCalendar from 'Hooks/CalendarHook';

// Styled Components
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
  border: '1px solid #f0f0f0',
  transition: 'all 0.3s ease',
  marginTop: theme.spacing(3),
  '&:hover': {
    boxShadow: '0 4px 20px rgba(0,0,0,0.12)',
  },
}));

const StyledSelect = styled(Select)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 8,
    backgroundColor: '#fafafa',
    '&:hover': {
      backgroundColor: '#f5f5f5',
    },
  },
}));

const GroupChip = styled(Chip)(({ theme, variant, selected, size }) => ({
  borderRadius: 9,
  fontWeight: 600,
  fontSize: size === 'small' ? '0.75rem' : '0.875rem',
  padding: size === 'small' ? '4px 8px' : '8px 16px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  border: 'none',
  boxShadow: selected ? '0 2px 8px rgba(0,0,0,0.1)' : 'none',
  backgroundColor: selected
    ? '#ffffff'
    : size === 'small'
    ? '#f0f0f0'
    : 'transparent',
  color: selected ? '#333333' : '#666666',
  '&:hover': {
    backgroundColor: selected ? '#ffffff' : '#eeeeee',
  },
  '& .MuiChip-label': {
    padding: '0',
  },
}));

const SegmentedControlContainer = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  backgroundColor: '#f5f5f5',
  borderRadius: 13,
  padding: 4,
  gap: 0,
  border: '1px solid #e0e0e0',
}));

// Custom Hooks
const useUserSelectionModal = () => {
  const [modalState, setModalState] = useState(
    fromJS({
      open: false,
      currentGroupId: null,
      searchTerm: '',
      selectedUsers: List(),
    })
  );

  const openModal = useCallback((groupId, selectedStaffIds = []) => {
    setModalState(
      fromJS({
        open: true,
        currentGroupId: groupId,
        searchTerm: '',
        selectedUsers: List(selectedStaffIds),
      })
    );
  }, []);

  const closeModal = useCallback(() => {
    setModalState((prev) =>
      prev.merge({
        open: false,
        searchTerm: '',
      })
    );
  }, []);

  const updateSearchTerm = useCallback((searchTerm) => {
    setModalState((prev) => prev.set('searchTerm', searchTerm));
  }, []);

  const toggleUserSelection = useCallback((userId) => {
    setModalState((prev) => {
      const currentSelectedUsers = prev.get('selectedUsers', List());
      const userIndex = currentSelectedUsers.indexOf(userId);

      let newSelectedUsers;
      if (userIndex >= 0) {
        newSelectedUsers = currentSelectedUsers.delete(userIndex);
      } else {
        newSelectedUsers = currentSelectedUsers.push(userId);
      }
      return prev.set('selectedUsers', newSelectedUsers);
    });
  }, []);

  const resetSelection = useCallback(() => {
    setModalState((prev) => prev.set('selectedUsers', List()));
  }, []);

  return {
    modalState,
    openModal,
    closeModal,
    updateSearchTerm,
    toggleUserSelection,
    resetSelection,
  };
};

const useDeliveryTypeData = (masterGroup) => {
  return useMemo(() => {
    if (masterGroup.size === 0) return fromJS({});

    return masterGroup
      .groupBy((group) => {
        const deliveryType = group.get('delivery_type') || 'unknown';
        const deliverySymbol = group.get('delivery_symbol') || 'unknown';
        const groupId = group.get('_id') || Math.random().toString(36);
        return `${deliveryType}-${deliverySymbol}-${groupId}`;
      })
      .mapEntries(([key, groups]) => [
        key,
        Map({
          delivery_type: groups.first().get('delivery_type'),
          delivery_symbol: groups.first().get('delivery_symbol'),
          groups: groups.filter((g) => g.get('session_group', List()).size > 0),
          maleGroups: groups.filter(
            (g) =>
              g.get('gender') === 'male' &&
              g.get('session_group', List()).size > 0
          ),
          femaleGroups: groups.filter(
            (g) =>
              g.get('gender') === 'female' &&
              g.get('session_group', List()).size > 0
          ),
        }),
      ])
      .filter((group) => group.get('groups', List()).size > 0);
  }, [masterGroup]);
};

const useSessionGroups = (assignInchargeData) => {
  return useMemo(() => {
    if (!assignInchargeData.get('deliveryGroups', List()).size) return List();

    const currentGroupType = assignInchargeData.get('groupType', 'male');
    const groups = assignInchargeData.get('deliveryGroups', List());

    return groups
      .filter((group) => group.get('gender') === currentGroupType)
      .flatMap((group, groupIndex) =>
        group.get('session_group', List()).map((sessionGroup, sessionIndex) => {
          const sessionId = sessionGroup.get('_id');
          const fallbackId = `session-${groupIndex}-${sessionIndex}`;
          const finalId = sessionId || fallbackId;

          // Debug logging - remove this after fixing
          if (!sessionId) {
            console.warn('Creating session group with missing _id:', {
              sessionGroup: sessionGroup.toJS(),
              fallbackId,
              groupIndex,
              sessionIndex,
            });
          }

          return fromJS({
            _id: finalId,
            displayName: `${
              group.get('gender') === 'male' ? 'M' : 'F'
            } - ${sessionGroup.get('group_name')}`,
            masterGroupId: group.get('_id'),
            masterGroupName: group.get('group_name'),
            sessionGroupName: sessionGroup.get('group_name'),
            gender: group.get('gender'),
            groupNo: group.get('group_no'),
          });
        })
      );
  }, [assignInchargeData]);
};

// Sub-components
const DeliveryTypeSelector = ({
  deliveryType,
  groupedMasterData,
  onDeliveryTypeChange,
}) => (
  <Box sx={{ mb: 3 }}>
    <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
      Primary Delivery Type:
    </Typography>
    <FormControl sx={{ width: '50%' }}>
      <StyledSelect value={deliveryType} onChange={onDeliveryTypeChange}>
        {groupedMasterData
          .map(
            (group) =>
              `${group.get('delivery_type')} - ${group.get('delivery_symbol')}`
          )
          .map((type, index) => (
            <MenuItem key={type || `delivery-type-${index}`} value={type}>
              {type}
            </MenuItem>
          ))
          .toArray()}
      </StyledSelect>
    </FormControl>
  </Box>
);
DeliveryTypeSelector.propTypes = {
  deliveryType: PropTypes.string,
  groupedMasterData: PropTypes.object,
  onDeliveryTypeChange: PropTypes.func,
};

const GroupTypeSelector = ({
  groupTypes,
  currentGroupType,
  studentCounts,
  onGroupTypeChange,
}) => (
  <Box sx={{ mb: 3 }}>
    <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 1 }}>
      Group Type:
    </Typography>
    <SegmentedControlContainer>
      {groupTypes
        .map((groupType, index) => (
          <GroupChip
            key={groupType.get('type') || `group-type-${index}`}
            variant={groupType.get('type')}
            selected={currentGroupType === groupType.get('type')}
            label={`${groupType.get('label')} (${
              groupType.get('type') === 'male'
                ? studentCounts.male
                : studentCounts.female
            })`}
            onClick={() => onGroupTypeChange(groupType.get('type'))}
          />
        ))
        .toArray()}
    </SegmentedControlContainer>
  </Box>
);
GroupTypeSelector.propTypes = {
  groupTypes: PropTypes.object,
  currentGroupType: PropTypes.string,
  studentCounts: PropTypes.object,
  onGroupTypeChange: PropTypes.func,
};
const AssignmentTable = ({
  sessionGroups,
  getAssignedStaff,
  onOpenUserSelection,
}) => {
  if (sessionGroups.size === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 3 }}>
        <Typography variant="body2" color="textSecondary">
          No groups available for the selected delivery type and gender.
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        border: '1px solid #E5E7EB',
        borderRadius: '8px',
        overflow: 'hidden',
        width: '50%',
      }}
    >
      {/* Headers */}
      <Grid
        container
        spacing={2}
        sx={{
          background: '#F3F4F6',
          borderRadius: '8px 8px 0px 0px',
          p: 2,
          m: 0,
        }}
      >
        <Grid item xs={5} sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle2" fontWeight={600}>
            Delivery Groups
          </Typography>
        </Grid>
        <Grid item xs={4} sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle2" fontWeight={600}>
            Assign Staff Member
          </Typography>
        </Grid>
      </Grid>

      {/* Data Rows */}
      <Box sx={{ background: '#FFF', borderRadius: '0px 0px 8px 8px' }}>
        {sessionGroups.map((sessionGroup, index) => {
          const sessionId = sessionGroup.get('_id');
          const fallbackKey = `session-group-${index}`;
          const finalKey = sessionId || fallbackKey;

          // Debug logging - remove this after fixing
          if (!sessionId) {
            console.warn(
              'Missing session ID for group:',
              sessionGroup.toJS(),
              'using fallback:',
              fallbackKey
            );
          }

          return (
            <Grid
              container
              key={finalKey}
              sx={{
                p: 2,
                borderBottom:
                  index < sessionGroups.size - 1 ? '1px solid #E5E7EB' : 'none',
              }}
            >
              <Grid item xs={5}>
                <Box
                  sx={{
                    py: 1,
                    display: 'flex',
                    alignItems: 'center',
                    height: '100%',
                  }}
                >
                  <Typography variant="body2" fontWeight={600}>
                    {sessionGroup.get('displayName')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ py: 1 }}>
                  <TextField
                    placeholder="Select Staff"
                    value={(() => {
                      const assignedStaffs = getAssignedStaff(
                        sessionGroup.get('_id', '')
                      );

                      if (assignedStaffs && assignedStaffs.size > 0) {
                        const staffNames = assignedStaffs
                          .map((staff) => {
                            const firstName = staff.getIn(
                              ['name', 'first'],
                              ''
                            );
                            const lastName = staff.getIn(['name', 'last'], '');
                            return `${firstName} ${lastName}`.trim();
                          })
                          .toArray();
                        return staffNames.join(', ');
                      }
                      return '';
                    })()}
                    onClick={() =>
                      onOpenUserSelection(sessionGroup.get('_id', ''))
                    }
                    fullWidth
                    size="small"
                    InputProps={{
                      readOnly: true,
                      sx: {
                        cursor: 'pointer',
                        backgroundColor: '#fafafa',
                        '&:hover': {
                          backgroundColor: '#f5f5f5',
                        },
                      },
                    }}
                  />
                </Box>
              </Grid>
            </Grid>
          );
        })}
      </Box>
    </Box>
  );
};
AssignmentTable.propTypes = {
  sessionGroups: PropTypes.object,
  getAssignedStaff: PropTypes.func,
  onOpenUserSelection: PropTypes.func,
};
const UserSelectionDialog = ({
  modalState,
  filteredStaffMembers,
  onClose,
  onSearchChange,
  onUserToggle,
  onReset,
  onAssign,
}) => {
  const isOpen = modalState.get('open', false);
  const selectedUsers = modalState.get('selectedUsers', List());
  const searchTerm = modalState.get('searchTerm', '');

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '600px',
        },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h6" fontWeight={600}>
          Select Staff Member
        </Typography>
      </DialogTitle>
      <DialogContent sx={{ p: 0 }}>
        {/* Search and Filter Bar */}
        <Box sx={{ p: 3, pb: 2, borderBottom: '1px solid #e0e0e0' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{ position: 'relative', flex: 1 }}>
              <TextField
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                fullWidth
                size="small"
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
                  ),
                  sx: { borderRadius: 2 },
                }}
              />
            </Box>
            <IconButton sx={{ border: '1px solid #e0e0e0', borderRadius: 2 }}>
              <FilterListIcon />
            </IconButton>
          </Box>
        </Box>

        {/* User List */}
        <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
          {filteredStaffMembers.map((staff, index) => {
            const staffId = staff.get('_id');
            const isSelected = selectedUsers.includes(staffId);
            const fullName = `${staff.getIn(
              ['name', 'first'],
              ''
            )} ${staff.getIn(['name', 'last'], '')}`.trim();

            return (
              <Box
                key={staff.get('_id') || `staff-${index}`}
                sx={{
                  p: 2,
                  borderBottom: '1px solid #f0f0f0',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: '#f8f9fa',
                  },
                  backgroundColor: isSelected ? '#f0f8ff' : 'transparent',
                }}
                onClick={() => onUserToggle(staff.get('_id'))}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Checkbox
                    checked={isSelected}
                    onChange={() => onUserToggle(staff.get('_id'))}
                    onClick={(e) => e.stopPropagation()}
                    sx={{ p: 0 }}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body1"
                      fontWeight={600}
                      sx={{ mb: 0.5 }}
                    >
                      {fullName}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {staff.get('user_id', '')} • {staff.get('gender', '')} •
                      Staff Member
                    </Typography>
                  </Box>
                  <Chip
                    label="GENERAL"
                    size="small"
                    sx={{
                      backgroundColor: '#f5f5f5',
                      color: '#666666',
                      fontWeight: 600,
                      fontSize: '0.75rem',
                    }}
                  />
                </Box>
              </Box>
            );
          })}
          {filteredStaffMembers.size === 0 && (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography color="text.secondary">
                No staff members found matching your search.
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 3, pt: 2, borderTop: '1px solid #e0e0e0' }}>
        <Button
          onClick={onReset}
          sx={{ color: '#1976d2', textTransform: 'none' }}
        >
          Reset
        </Button>
        <Button
          onClick={onClose}
          sx={{ color: '#666666', textTransform: 'none' }}
        >
          Cancel
        </Button>
        <Button
          onClick={onAssign}
          variant="contained"
          disabled={selectedUsers.size === 0}
          sx={{
            textTransform: 'none',
            px: 3,
            py: 1,
          }}
        >
          Assign {selectedUsers.size > 0 ? `(${selectedUsers.size})` : ''}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
UserSelectionDialog.propTypes = {
  modalState: PropTypes.object,
  filteredStaffMembers: PropTypes.object,
  onClose: PropTypes.func,
  onSearchChange: PropTypes.func,
  onUserToggle: PropTypes.func,
  onReset: PropTypes.func,
  onAssign: PropTypes.func,
};
// Main Component
const CourseWiseDeliveryTypes = ({
  assignInchargeData,
  setAssignInchargeData,
  programId,
  courseId,
  term,
  year,
  level,
  rotation,
  rotationCount,
}) => {
  const dispatch = useDispatch();
  const { currentCalendarId } = useCalendar();
  const institutionCalenderId = currentCalendarId();

  // Redux selectors
  const courseBasedStaffDetails = useSelector(selectCourseBasedStaffDetails);
  const deliveryBasedStaff = useSelector(selectDeliveryBasedStaff);
  const sessionData = useSelector((state) => state?.sessions);

  // Local state
  const isInitialized = useRef(false);
  const {
    modalState,
    openModal,
    closeModal,
    updateSearchTerm,
    toggleUserSelection,
    resetSelection,
  } = useUserSelectionModal();

  // Derived state
  const courseStudentDataMemo = useMemo(
    () => sessionData.get('courseStudentData', Map()),
    [sessionData]
  );

  const studentCounts = useMemo(
    () => ({
      total: courseStudentDataMemo.get('totalStudentCount', 0),
      male: courseStudentDataMemo.get('maleStudentCount', 0),
      female: courseStudentDataMemo.get('femaleStudentCount', 0),
    }),
    [courseStudentDataMemo]
  );

  const masterGroup = useMemo(
    () => courseStudentDataMemo.get('masterGroup', List()),
    [courseStudentDataMemo]
  );

  const groupedMasterData = useDeliveryTypeData(masterGroup);
  const sessionGroups = useSessionGroups(assignInchargeData);

  // Reset initialization flag when key parameters change
  useEffect(() => {
    isInitialized.current = false;
  }, [
    programId,
    courseId,
    term,
    year,
    level,
    rotation,
    rotationCount,
    institutionCalenderId,
  ]);

  // API call functions
  const callGetDeliveryBasedStaff = useCallback(
    (params) => {
      dispatch(getDeliveryBasedStaff(params));
    },
    [dispatch]
  );

  const callGetCourseStudentsList = useCallback(() => {
    dispatch(
      getCourseStudentsList({
        calendarId: institutionCalenderId,
        programId,
        level,
        term,
        courseId,
        year,
        ...(rotation === 'yes' && { rotationCount }),
      })
    );
  }, [
    dispatch,
    institutionCalenderId,
    programId,
    level,
    term,
    courseId,
    year,
    rotation,
    rotationCount,
  ]);

  const callGetCourseBasedStaffDetails = useCallback(() => {
    const params = {
      institutionCalenderId,
      programId,
      levelNo: level,
      term,
      courseId,
      yearNo: year,
      rotation,
      ...(rotation === 'yes' && { rotation_count: rotationCount }),
    };
    dispatch(getCourseBasedStaffDetails(params));
  }, [
    dispatch,
    institutionCalenderId,
    programId,
    level,
    term,
    courseId,
    year,
    rotation,
    rotationCount,
  ]);

  // Initial API calls
  useEffect(() => {
    callGetCourseStudentsList();
  }, [callGetCourseStudentsList]);

  // Load course-based staff details initially
  useEffect(() => {
    if (
      programId &&
      courseId &&
      level &&
      term &&
      year &&
      institutionCalenderId
    ) {
      callGetCourseBasedStaffDetails();
    }
  }, [
    programId,
    courseId,
    level,
    term,
    year,
    institutionCalenderId,
    callGetCourseBasedStaffDetails,
  ]);

  useEffect(() => {
    if (
      assignInchargeData.get('deliveryType') &&
      assignInchargeData.get('groupType')
    ) {
      const [deliveryType] = assignInchargeData
        .get('deliveryType', '')
        .split(' - ');
      const currentGroupType = assignInchargeData.get('groupType', '');
      const currentDeliveryGroups = assignInchargeData.get(
        'deliveryGroups',
        List()
      );

      if (currentDeliveryGroups.size > 0) {
        const sessionIds = [];
        currentDeliveryGroups.forEach((group) => {
          const sessionGroups = group.get('session_group', List());
          sessionGroups.forEach((sessionGroup) => {
            const sessionId = sessionGroup.get('_id', '');
            if (sessionId) {
              sessionIds.push(sessionId);
            }
          });
        });

        if (sessionIds.length > 0) {
          const params = {
            institutionCalenderId,
            programId,
            courseId,
            yearNo: year,
            levelNo: level,
            term,
            rotation: 'no',
            rotation_count: null,
            deliveryType,
            gender: currentGroupType,
            sessionId: sessionIds,
          };

          callGetDeliveryBasedStaff(params);
        }
      }
    }
  }, [
    institutionCalenderId,
    programId,
    courseId,
    year,
    level,
    term,
    callGetDeliveryBasedStaff,
    assignInchargeData,
  ]);

  // Update assignInchargeData when groupedMasterData changes
  useEffect(() => {
    if (groupedMasterData.size > 0) {
      const firstGroupKey = groupedMasterData.keySeq().first();
      const firstGroup = groupedMasterData.get(firstGroupKey);

      setAssignInchargeData((prevState) => {
        const newDeliveryType = `${firstGroup.get(
          'delivery_type'
        )} - ${firstGroup.get('delivery_symbol')}`;

        if (prevState.get('deliveryType') === newDeliveryType) {
          return prevState;
        }

        return prevState.merge({
          deliveryType: newDeliveryType,
          deliveryGroups: firstGroup.get('groups', List()),
          groupTypes: fromJS([
            {
              type: 'male',
              label: 'Male Groups',
              count: studentCounts.male,
            },
            {
              type: 'female',
              label: 'Female Groups',
              count: studentCounts.female,
            },
          ]),
        });
      });
    }
  }, [groupedMasterData, studentCounts, setAssignInchargeData]);

  // Initialize staff data
  useEffect(() => {
    if (
      groupedMasterData.size > 0 &&
      deliveryBasedStaff.size > 0 &&
      !isInitialized.current
    ) {
      isInitialized.current = true;

      const formattedData = deliveryBasedStaff.reduce((acc, session) => {
        const sessionId = session.get('sessionId', '');
        const staffList = session.get('staff_details', List()).map((staff) =>
          Map({
            _id: staff.get('staff_id', ''),
            user_id: staff.get('user_id', ''),
            name: staff.get('name', ''),
            gender: staff.get('gender', ''),
          })
        );
        return acc.set(sessionId, staffList);
      }, Map());

      const staffIds = deliveryBasedStaff.flatMap((item) =>
        item
          .get('staff_details', List())
          .map((staff) => staff.get('staff_id', ''))
      );

      setAssignInchargeData((prev) =>
        prev.set('selectedStaffList', formattedData)
      );
    }
  }, [deliveryBasedStaff, groupedMasterData, setAssignInchargeData]);

  // Event handlers
  const handleDeliveryTypeChange = useCallback(
    (event) => {
      const newDeliveryType = event.target.value;
      const [deliveryType, deliverySymbol] = newDeliveryType.split(' - ');
      const groupKey = `${deliveryType}-${deliverySymbol}`;
      const selectedGroup = groupedMasterData.get(groupKey);

      if (selectedGroup) {
        setAssignInchargeData((prev) =>
          prev.merge({
            deliveryType: newDeliveryType,
            deliveryGroups: selectedGroup.get('groups', List()),
            groupTypes: fromJS([
              {
                type: 'male',
                label: 'Male Groups',
                count: studentCounts.male,
              },
              {
                type: 'female',
                label: 'Female Groups',
                count: studentCounts.female,
              },
            ]),
          })
        );
      }
    },
    [groupedMasterData, studentCounts, setAssignInchargeData]
  );

  const handleGroupTypeChange = useCallback(
    (groupType) => {
      isInitialized.current = false;
      setAssignInchargeData((prev) => {
        const currentDeliveryType = prev.get('deliveryType', '');
        const [deliveryType, deliverySymbol] = currentDeliveryType.split(' - ');
        const groupKey = `${deliveryType}-${deliverySymbol}`;
        const selectedGroup = groupedMasterData.get(groupKey);

        if (selectedGroup) {
          const filteredGroups =
            groupType === 'male'
              ? selectedGroup.get('maleGroups', List())
              : selectedGroup.get('femaleGroups', List());

          return prev.merge({
            groupType,
            deliveryGroups: filteredGroups,
          });
        }
        return prev;
      });
    },
    [groupedMasterData, setAssignInchargeData]
  );

  const handleAssigneeChange = useCallback(
    (groupId, staffIds) => {
      const staffList = fromJS(courseBasedStaffDetails || []);
      const selectedStaffs = staffIds
        .map((staffId) =>
          staffList.find((staff) => staff.get('_id') === staffId)
        )
        .filter(Boolean);

      setAssignInchargeData((prev) =>
        prev.setIn(['selectedStaffList', groupId], List(selectedStaffs))
      );
    },
    [courseBasedStaffDetails, setAssignInchargeData]
  );

  const getAssignedStaff = useCallback(
    (groupId) => {
      return assignInchargeData.getIn(['selectedStaffList', groupId], List());
    },
    [assignInchargeData]
  );

  const handleOpenUserSelection = useCallback(
    (groupId) => {
      const currentSelectedStaffs = getAssignedStaff(groupId);
      const selectedStaffIds = currentSelectedStaffs
        .map((staff) => staff.get('_id', ''))
        .filter((id) => id)
        .toArray();

      openModal(groupId, selectedStaffIds);

      // Only call API if staff details are not already loaded
      if (!courseBasedStaffDetails || courseBasedStaffDetails.length === 0) {
        callGetCourseBasedStaffDetails();
      }
    },
    [
      getAssignedStaff,
      openModal,
      callGetCourseBasedStaffDetails,
      courseBasedStaffDetails,
    ]
  );

  const handleAssignUser = useCallback(() => {
    const selectedUserIds = modalState.get('selectedUsers', List()).toArray();
    const currentGroupId = modalState.get('currentGroupId');

    if (selectedUserIds.length > 0 && currentGroupId) {
      handleAssigneeChange(currentGroupId, selectedUserIds);
    }
    closeModal();
  }, [modalState, handleAssigneeChange, closeModal]);

  // Data formatting for backend
  const formatDataForBackend = useCallback(() => {
    const selectedStaffList = assignInchargeData.get(
      'selectedStaffList',
      Map()
    );
    const deliveryType = assignInchargeData.get('deliveryType', '');

    if (!groupedMasterData || groupedMasterData.size === 0) {
      return [];
    }

    const [deliveryTypeName, deliverySymbol] = deliveryType.split(' - ');
    const groupKey = `${deliveryTypeName}-${deliverySymbol}`;
    const selectedGroup = groupedMasterData.get(groupKey);

    if (!selectedGroup) {
      return [];
    }

    const allGroups = selectedGroup.get('groups', List());
    const formattedData = [];

    allGroups.forEach((group) => {
      const groupId = group.get('_id', '');
      const groupType = group.get('gender', '');
      const sessionGroups = group.get('session_group', List());

      sessionGroups.forEach((sessionGroup) => {
        const sessionGroupId = sessionGroup.get('_id', '');
        const sessionGroupName = sessionGroup.get('group_name', '');
        const staffMembers = selectedStaffList.get(sessionGroupId, List());

        if (
          staffMembers &&
          (List.isList(staffMembers)
            ? staffMembers.size > 0
            : Array.isArray(staffMembers)
            ? staffMembers.length > 0
            : false)
        ) {
          let staffIds;
          if (List.isList(staffMembers)) {
            staffIds = staffMembers.map((staff) => staff.get('_id')).toArray();
          } else if (Array.isArray(staffMembers)) {
            staffIds = staffMembers.map(
              (staff) => staff._id || staff.get('_id')
            );
          } else {
            staffIds = [];
          }

          formattedData.push({
            deliveryType: deliveryTypeName,
            delivery_symbol: deliverySymbol,
            groupType,
            groupNo: sessionGroupName,
            groupId,
            sessionId: sessionGroupId,
            staffMembers: staffIds,
            institutionCalenderId,
            programId,
            levelNo: level,
            term,
            courseId,
            yearNo: year,
            rotation,
            ...(rotation === 'yes' && { rotation_count: rotationCount }),
          });
        }
      });
    });
    return formattedData;
  }, [
    assignInchargeData,
    groupedMasterData,
    institutionCalenderId,
    programId,
    level,
    term,
    courseId,
    year,
    rotation,
    rotationCount,
  ]);

  const handleSubmitAssignments = useCallback(() => {
    const formattedData = formatDataForBackend();

    dispatch(
      updateCourseWiseDeliveryType({ groups: formattedData }, () => {
        const selectedStaffList = assignInchargeData.get(
          'selectedStaffList',
          Map()
        );
        const deliveryType = assignInchargeData.get('deliveryType', '');
        const [deliveryTypeName, deliverySymbol] = deliveryType.split(' - ');
        const groupKey = `${deliveryTypeName}-${deliverySymbol}`;
        const selectedGroup = groupedMasterData.get(groupKey);

        if (selectedGroup) {
          const allGroups = selectedGroup.get('groups', List());
          const sessionIds = [];

          allGroups.forEach((group) => {
            const sessionGroups = group.get('session_group', List());
            sessionGroups.forEach((sessionGroup) => {
              const sessionGroupId = sessionGroup.get('_id', '');
              if (sessionGroupId) {
                sessionIds.push(sessionGroupId);
              }
            });
          });

          if (sessionIds.length > 0) {
            const params = {
              institutionCalenderId,
              programId,
              courseId,
              yearNo: year,
              levelNo: level,
              term,
              rotation: 'no',
              rotation_count: null,
              deliveryType: deliveryTypeName,
              gender: assignInchargeData.get('groupType', ''),
              sessionId: sessionIds,
            };

            callGetDeliveryBasedStaff(params);
          }
        }
      })
    );
  }, [
    formatDataForBackend,
    dispatch,
    assignInchargeData,
    groupedMasterData,
    institutionCalenderId,
    programId,
    courseId,
    year,
    level,
    term,
    callGetDeliveryBasedStaff,
  ]);

  // Filtered staff members for search
  const filteredStaffMembers = useMemo(() => {
    const searchTerm = modalState.get('searchTerm', '').toLowerCase();
    const staffList = fromJS(courseBasedStaffDetails || []);

    return staffList.filter((staff) => {
      const firstName = staff.getIn(['name', 'first'], '').toLowerCase();
      const lastName = staff.getIn(['name', 'last'], '').toLowerCase();
      const fullName = `${firstName} ${lastName}`.toLowerCase();
      const userId = staff.get('user_id', '').toLowerCase();
      const gender = staff.get('gender', '').toLowerCase();

      return (
        fullName.includes(searchTerm) ||
        userId.includes(searchTerm) ||
        gender.includes(searchTerm)
      );
    });
  }, [courseBasedStaffDetails, modalState]);

  return (
    <div>
      <StyledCard>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight={700} gutterBottom>
            Assign Incharge for Each Delivery Type
          </Typography>

          <DeliveryTypeSelector
            deliveryType={assignInchargeData.get('deliveryType', '')}
            groupedMasterData={groupedMasterData}
            onDeliveryTypeChange={handleDeliveryTypeChange}
          />

          <GroupTypeSelector
            groupTypes={assignInchargeData.get('groupTypes', List())}
            currentGroupType={assignInchargeData.get('groupType')}
            studentCounts={studentCounts}
            onGroupTypeChange={handleGroupTypeChange}
          />

          <AssignmentTable
            sessionGroups={sessionGroups}
            getAssignedStaff={getAssignedStaff}
            onOpenUserSelection={handleOpenUserSelection}
          />

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmitAssignments}
              sx={{ textTransform: 'none', px: 4, py: 1.5 }}
            >
              Submit Assignments
            </Button>
          </Box>
        </CardContent>
      </StyledCard>

      <UserSelectionDialog
        modalState={modalState}
        filteredStaffMembers={filteredStaffMembers}
        onClose={closeModal}
        onSearchChange={updateSearchTerm}
        onUserToggle={toggleUserSelection}
        onReset={resetSelection}
        onAssign={handleAssignUser}
      />
    </div>
  );
};

export default CourseWiseDeliveryTypes;

CourseWiseDeliveryTypes.propTypes = {
  assignInchargeData: PropTypes.instanceOf(Map),
  setAssignInchargeData: PropTypes.func,
  programId: PropTypes.string,
  courseId: PropTypes.string,
  term: PropTypes.string,
  year: PropTypes.string,
  level: PropTypes.string,
  rotation: PropTypes.string,
  rotationCount: PropTypes.string,
};
