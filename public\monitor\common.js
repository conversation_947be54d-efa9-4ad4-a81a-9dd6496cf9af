let sessionData = config.defaultInstitution
let showLoaderApi = false
let lastRefreshTime = new Date().toLocaleTimeString();
$(document).ready(function () {

  const userDetails = localStorage.getItem('userDetails');

  if(!userDetails){
    return
  }
    
  // Load refresh time from localStorage or use default from config
  const savedRefreshTime = localStorage.getItem('refreshPageTime');
  const refreshInterval = savedRefreshTime ? parseInt(savedRefreshTime) : refreshPageTime;
  const savedDateFilter = localStorage.getItem('dateFilter');
  const savedActiveTab = localStorage.getItem('activeTabState');

  if (savedActiveTab) {
    $(`#${savedActiveTab}`).tab('show');
  }

  $('#refreshTimeInput').val(refreshInterval);
  $('#refreshTimeDisplay').text(formatMilliSecondsTime(refreshInterval));
  $("#dateFilter").val(savedDateFilter || new Date().toISOString().split('T')[0]);
  $('#last-refresh-time').text(new Date().toLocaleTimeString());

    // Update the display when the input value changes
    $('#refreshTimeInput').on('input', function () {
      const inputValue = parseInt($(this).val());
      const minValue = 300000;
      if (inputValue < minValue) {
          $(this).val(minValue); 
      }
      $('#refreshTimeDisplay').text(formatMilliSecondsTime($(this).val()));
    });

    if (localStorage.getItem('load_value') === 'true') {
      localStorage.removeItem('load_value');
      showLoaderApi = true  
      loadFilterValues();
    }
    
    $('#dateFilter').on('change', function() {
      scheduleDateInput = $(this).val();
      $("#current-date").text(scheduleDateInput)
      handleInstitutionData(sessionData)
      closeSettings();
    });
    
    $('#logoutButton').click(function(){
      handleLogout()
    });

    $('#clearCache').click(function(){
      clearFilterValues()
    });

    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
      const activeTabId = $(e.target).attr('id');
      localStorage.setItem('activeTabState', activeTabId);
     });
    
    fetchInstitutionData();
    setInterval(refreshPage, refreshInterval);

});

function refreshPage() {
  const currentHour = new Date().getHours();
  
  // Check if the current time is between 7 AM (7) and 7 PM (19)
  if (currentHour >= 7 && currentHour < 19) {
    saveFilterValues();
    location.reload();
  }
}

function fetchInstitutionData(){
  const collegeCode = config.queryParams.get('code')
  let institutionApiUrlWithParams = ""
  if(collegeCode){
      institutionApiUrlWithParams = `${config.institutionApiUrl}?code=${collegeCode}`;
  }else{
    alert('Please enter the college code ')
  }

  // Fetch institution data
  $.ajax({
      url: institutionApiUrlWithParams,
      headers: config.institutionApiHeaders,
      success: function (response) {
          sessionData = response
          handleInstitutionData(response);
      },
      error: function (xhr, status, error) {
          console.error('Error fetching institution data:', error);
          handleInstitutionData(config.defaultInstitution);
      }
  });
}

function handleInstitutionData(response) {
    if (response.status && response.data && response.data.collegeList && response.data.collegeList.length > 0) {
        const institution = response.data.collegeList[0];
        let baseUrl = institution.baseUrl;
        let baseUrlApiKey = institution.serviceAccessKey;
        let daServiceApiBaseUrl = institution.daService.apiBaseUrl;
        let daServiceApiServiceKey = institution.daService.apiServiceKey
        const scheduleDate = $('#dateFilter').val() || new Date().toISOString().split('T')[0]; 

        const logoUrl = institution.collegeLogo;
        if(logoUrl){
            $('.college-logo').attr('src', logoUrl);
            $('.college-logo').css({
              'width': 100,
              'height': 100
            });  
        }

        const collegeName = institution.collegeName
        if(collegeName){
          $('#college-name').text(collegeName);
        }

        // Fetch session data
        fetchSessionData(baseUrl, scheduleDate, baseUrlApiKey);

        // Fetch exam data
        fetchExamData( scheduleDate,   daServiceApiBaseUrl, daServiceApiServiceKey);
    } else {
        alert('Institution data not found');
    }
}

// Fetch session data
function fetchSessionData(baseUrl, scheduleDate, headers) {
  const sessionApiUrl = `${baseUrl}v1/hebaAI/scheduleListDetails?scheduleDate=${scheduleDate}`;

  if(!showLoaderApi){
    showLoader(true)
  }

  $.ajax({
      url: sessionApiUrl,
      headers: { 'digicronkey': headers },
      success: function(response) {
        showLoader(false);
          renderSessions(response, baseUrl, headers);
      },
      error: function (xhr, status, error) {
        showLoader(false);
          console.error('Error fetching session data:', error);
      }
  });
}

// Fetch exam data
function fetchExamData(scheduleDate, daServiceApiBaseUrl,  daServiceApiServiceKey) {
 
  //  const fullExamApiUrl = `${daServiceApiBaseUrl}/misc/testcenter-status?date=${scheduleDate}`;
   const fullExamApiUrl = `https://daapi.prodsla.digi-val.com/api/v1/misc/testcenter-status?date=${scheduleDate}`;

  if(!showLoaderApi){
    showLoader(true)
  }
  
  $.ajax({
      url: fullExamApiUrl,
      headers: { 'x-api-key': daServiceApiServiceKey },
      success: function(response) {
        showLoader(false);
          renderExams(response, daServiceApiBaseUrl, daServiceApiServiceKey);
      },
      error: function (xhr, status, error) {
        showLoader(false);
          console.error('Error fetching exam data:', error);
      }
  });
}

// Utility functions
function convertToLocalTime(utcTime) {
  return new Date(utcTime).toLocaleTimeString();
}

function calculateTimeDifference(startUtc, startSchedule) {
  const differenceInMinutes = Math.abs(new Date(startUtc) - new Date(startSchedule)) / (1000 * 60);
  return differenceInMinutes;
}

function clearFilterValues() {

    // Clear all stored data
    localStorage.removeItem('refresh');
    localStorage.removeItem('selectedStatusesClass');
    localStorage.removeItem('selectedStatusExam');
    localStorage.removeItem('selectedTimeFilter');
    localStorage.removeItem('colorPicker');
    localStorage.removeItem('load_value');
    localStorage.removeItem('dateFilter');
    localStorage.removeItem('refreshPageTime');
    localStorage.removeItem('activeTabState');

    // Clear input fields and selected filters
    $('.class-filter:checked').prop('checked', false); 
    $('.exam-filter:checked').prop('checked', false); 
    $('input[name="time-filter"]:checked').prop('checked', false); 
    $('#colorPicker').val('#00afaa'); 
    $('#dateFilter').val(new Date().toISOString().split('T')[0]); 
    $('#refreshTimeInput').val(refreshPageTime);

    location.reload();
}