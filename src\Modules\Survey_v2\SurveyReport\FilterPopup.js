import React, { lazy, Suspense } from 'react';
import PropTypes from 'prop-types';

import { Checkbox, Divider } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { styled } from '@mui/material/styles';
import { InputBase } from '@mui/material';

import MButton from 'Widgets/FormElements/material/Button';
import { List, Map } from 'immutable';
import { themeColorCheckBox } from '../../../designUtils';

const DialogModal = lazy(() => import('../models/DialogModal'));

const Search = styled('div')(({ theme }) => ({
  position: 'relative',
  marginLeft: 0,
  [theme.breakpoints.up('sm')]: {
    marginLeft: theme.spacing(0),
    marginRight: theme.spacing(0),
  },
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  fontSize: '14px',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    width: '100%',
    marginLeft: '50px',
  },
}));

const SearchIconWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: '100%',
  position: 'absolute',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
}));

const FilterPopup = ({ filterPopupOpen, handleCloseFilter }) => {
  return (
    <Suspense fallback="...">
      <DialogModal show={filterPopupOpen} onClose={() => {}} fullWidth={true}>
        <div className="px-3 pt-3">
          <div className="text-bright-blue font-20px-filter-report bold mb-2">
            Filter by
          </div>
          <Search className="digi-report-search-overview mb-4">
            <SearchIconWrapper>
              <SearchIcon color="disabled" />
            </SearchIconWrapper>
            <StyledInputBase
              className="digi-search-width"
              placeholder="Search programs..."
              inputProps={{ 'aria-label': 'search' }}
              // value={searchText} // || irSearch
              // onChange={(e) => {
              //   setSearchText(e.target.value);
              // }}
            />
          </Search>
          <div className="d-flex align-items-center report-filter-header px-2">
            <div className="text-bright-blue bold">All Programs</div>
            <div className="d-flex ml-auto align-items-center">
              <div>
                <Checkbox sx={themeColorCheckBox} />
              </div>
              <div>Select All</div>
            </div>
          </div>

          <div className="d-flex align-items-center">
            <Checkbox sx={themeColorCheckBox} />
            <div>B.Tech Information Technology</div>
          </div>
          <div className="d-flex align-items-center">
            <Checkbox sx={themeColorCheckBox} />
            <div>B.E. Mechanical Engineering</div>
          </div>
          <div className="d-flex align-items-center">
            <Checkbox sx={themeColorCheckBox} />
            <div>B.Tech Computer Science</div>
          </div>
          <div className="d-flex align-items-center">
            <Checkbox sx={themeColorCheckBox} />
            <div>B.E. Civil Engineering</div>
          </div>
          {/* <div className="d-flex align-items-center">
            <Checkbox />
            <div>Anonyms 00 </div>
          </div> */}

          <Divider />

          <div className="p-3 d-flex">
            <div className="d-flex ml-auto">
              <div className="mr-3">
                <MButton
                  className="textTransform-button"
                  variant="outlined"
                  color="gray"
                  clicked={() => handleCloseFilter()}
                  sx={{
                    '&.MuiButton-root:hover': {
                      backgroundColor:
                        'var(--backgroundTheme-color-selectShade)',
                    },
                  }}
                >
                  Cancel
                </MButton>
              </div>
              <div>
                <MButton
                  className="textTransform-button"
                  sx={{
                    backgroundColor: 'var(--backgroundTheme-color-vibrant)',
                    '&.MuiButton-root:hover': {
                      backgroundColor: 'var(--backgroundTheme-color-vibrant)',
                    },
                  }}
                >
                  Apply
                </MButton>
              </div>
            </div>
          </div>
        </div>
      </DialogModal>
    </Suspense>
  );
};

export default FilterPopup;
FilterPopup.propTypes = {
  filterPopupOpen: PropTypes.bool,
  handleCloseFilter: PropTypes.func,
  handleFilterApply: PropTypes.func,
  filterDetails: PropTypes.instanceOf(List),
  setFilterDetails: PropTypes.func,
  formula: PropTypes.instanceOf(Map),
  setFormula: PropTypes.func,
};
