import * as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import discussion_forum_icon from 'Assets/icons/Discussion Icon.svg';
import Container from '@mui/material/Container';
import { Grid, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import UpcomingSessions from './UpcomingSessions';
import DescriptionIcon from '@mui/icons-material/Description';
import RecentChatIcon from 'Assets/RecentChatIcon.svg';
import TimelineIcon from '@mui/icons-material/Timeline';
import TopicIcon from '@mui/icons-material/Topic';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { formattedFullName } from '../../../ReduxApi/util';
import {
  isChatly,
  isDefaultChat,
  isModuleEnabled,
  isRecentDocument,
  isActivityManagement,
} from 'utils';
import AnnouncementWelcome from 'Modules/Announcement/AnnouncementWelcome';
import CampaignIcon from '@mui/icons-material/Campaign';
import { crucial_constants } from 'Modules/Utils/constants';
import RecentChatWelcome from 'Modules/Chat/Components/newChat/RecentChatPhase1/RecentChatWelcome';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import { getLmsSetting } from 'ReduxApi/LmsStudent/action';

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: theme.palette.text.secondary,
  display: 'flex',
}));

function WelcomePage({ authData }) {
  WelcomePage.propTypes = {
    authData: PropTypes.instanceOf(Map),
  };
  const isDefaultChatEnabled = isDefaultChat();
  const isDigiChatEnabled = isChatly();
  const isDigiRecentDocument = isRecentDocument();
  const isActivity = isActivityManagement();
  const linkArray = [
    {
      name: 'Recent Documents',
      icon: <DescriptionIcon sx={{ color: '#6B7280' }} />,
      link: '/dashboard/recent-document',
    },
    // {
    //   name: 'Recent Chats',
    //   icon: <SmsIcon sx={{ color: '#6B7280' }} />,
    //   link: '/dashboard/recent-chats',
    // },
    {
      name: isDigiChatEnabled ? 'Chatly' : 'Recent Chats',
      icon: <img src={RecentChatIcon} alt="Recent_Chat_Icon" />,
      link: isDefaultChatEnabled
        ? '/dashboard/recent-chats'
        : isDigiChatEnabled
        ? '/dashboard/new_recent-chats'
        : '/dashboard/main',
    },
    {
      name: 'Recent Activity',
      icon: <TimelineIcon sx={{ color: '#6B7280' }} />,
      link: '/dashboard/recent-activity?&search=&tab=',
    },
    {
      name: 'Announcement Board',
      icon: <CampaignIcon sx={{ color: '#6B7280' }} />,
      link: '/announcement',
    },
    {
      name: crucial_constants.forum_discussion,
      icon: <img src={discussion_forum_icon} alt="discussion_forum_icon" />,
      link: '/courses?type=forum',
    },
    {
      name: 'My Course',
      icon: <TopicIcon sx={{ color: '#6B7280' }} />,
      link: '/courses',
    },
  ];

  const filteredItems = () => {
    let checkCards = [];
    if (isDefaultChatEnabled) {
      checkCards.push('Recent Chats');
    }
    if (isDigiChatEnabled) {
      checkCards.push('Chatly');
    }
    if (isDigiRecentDocument) {
      checkCards.push('Recent Documents');
    }
    if (isModuleEnabled('ACTIVITY_ENABLED') && isActivity) {
      checkCards.push('Recent Activity');
    }
    if (isModuleEnabled('ANNOUNCEMENT') || isModuleEnabled('ANNOUNCEMENT_V2')) {
      checkCards.push('Announcement Board');
    }
    if (isModuleEnabled('DISCUSSION_FORUM')) {
      checkCards.push(crucial_constants.forum_discussion);
    }
    checkCards.push('My Course');
    return linkArray.filter((item) => checkCards.includes(item.name));
  };

  const filteredCards = filteredItems();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state);
  const selectedRole = user.get('selectedRole', Map());

  useEffect(() => {
    if (selectedRole.get('name', '') === 'Schedule Staff') {
      dispatch(getLmsSetting({ type: 'leave' }));
    }
  }, [selectedRole]); //eslint-disable-line

  return (
    <React.Fragment>
      <CssBaseline />
      <Container maxWidth="md">
        <Box>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              fontSize: '32px',
              lineHeight: '52px',
              color: '#374151',
            }}
          >
            Welcome
          </Typography>
          <Typography
            variant="h6"
            sx={{ fontWeight: 'bold', lineHeight: '23px', color: '#374151' }}
          >
            {formattedFullName(authData.get('name', Map()).toJS())}
          </Typography>
          <Typography
            sx={{
              color: ' rgba(0, 0, 0, 0.54)',
              fontSize: '17px',
              lineHeight: '20px',
            }}
          >
            ID : {authData.get('user_id', '')}
          </Typography>
          <UpcomingSessions />
          {(isModuleEnabled('ANNOUNCEMENT') ||
            isModuleEnabled('ANNOUNCEMENT_V2')) && <AnnouncementWelcome />}
          {isModuleEnabled('DIGI_CHAT') && <RecentChatWelcome />}
          <Typography
            variant="h6"
            sx={{ fontWeight: 'bold', color: '#374151' }}
            className="mt-3"
          >
            Quick jump to
          </Typography>
          <Box sx={{ width: '100%' }} className="mt-3">
            <Grid
              container
              rowSpacing={1}
              columnSpacing={{ xs: 1, sm: 2, md: 3 }}
            >
              {filteredCards.map((item, index) => {
                return (
                  <Grid
                    item
                    xs={6}
                    key={index}
                    sx={{
                      // marginLeft:
                      //   filteredCards.length === 3 && index === 2 ? '25%' : '0',
                      cursor: 'pointer',
                      '& .MuiPaper-elevation:hover': {
                        boxShadow:
                          '0px 2px 4px -1px rgb(0 0 0 / 20%), 0px 4px 5px 0px rgb(0 0 0 / 14%), 0px 1px 10px 0px rgb(0 0 0 / 12%)',
                      },
                    }}
                  >
                    <Link to={item.link}>
                      <Item className="justify-content-between px-4 py-3">
                        <div className="d-flex">
                          {item.icon}
                          <Typography
                            sx={{ color: '#374151', fontWeight: 'bold', pl: 1 }}
                          >
                            {item.name}
                          </Typography>
                        </div>
                        <KeyboardArrowRightIcon sx={{ color: 'black' }} />
                      </Item>
                    </Link>
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        </Box>
      </Container>
    </React.Fragment>
  );
}

export default React.memo(WelcomePage);
