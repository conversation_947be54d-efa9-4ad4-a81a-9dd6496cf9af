import React, { useState, Suspense } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { List, Map } from 'immutable';
import { preparePercentage, fixedTwoDigit } from '../../Utils/utils';
import { getURLParams } from '../../../ReduxApi/util';
import InfoIcon from '@mui/icons-material/Info';
import CustomTooltip from 'Widgets/FormElements/material/CustomTooltip';

const RatingModal = React.lazy(() => import('../modal/RatingModal'));

const SessionHeader = ({
  isLoading,
  courseSessionData,
  userType,
  authData,
  currentTab,
}) => {
  const { t } = useTranslation();
  const [ratingModal, setRatingModal] = useState(false);
  if (isLoading) {
    return (
      <div className="d-flex justify-content-between pt-3">
        <p className="bold mb-0 pt-6 mt-4 text-center">{t('loading')}</p>
      </div>
    );
  }

  const lateAbsentValue = courseSessionData.getIn([0, 'studentLateAbsent'], 0);
  const padLeadingZero = (number) => {
    return number < 10 ? `0${number}` : `${number}`;
  };
  const formattedLateAbsent = `Late Absent ${padLeadingZero(lateAbsentValue)}`;
  const lateConfig = courseSessionData.getIn([0, 'lateConfig'], List());
  const lateConfigSize = courseSessionData.getIn(
    [0, 'lateConfig'],
    List()
  ).size;

  const adminCourses = getURLParams('_admin_courses', true) === 'true';

  return (
    <>
      <div className="d-flex justify-content-between pt-3">
        <p className="bold mb-0 pt-6 mt-4">{t('all_session')}</p>
        {userType === 'staff' &&
          adminCourses &&
          authData.get('courseAdmin') && (
            <div className="d-flex">
              <div>
                <span className="bg-hovercolor f-14 border-radius-20 p-2 border_blue">
                  <span className="mr-2">
                    <i className="fa fa-star text-warning" aria-hidden="true" />{' '}
                    {courseSessionData.size > 0 &&
                      courseSessionData.getIn(
                        [0, 'feedback', 'avgRating'],
                        0
                      )}{' '}
                  </span>
                  <span
                    className="text-skyblue bold icon"
                    onClick={
                      courseSessionData.size > 0 &&
                      courseSessionData.getIn(
                        [0, 'feedback', 'avgRating'],
                        0
                      ) !== 0
                        ? () => setRatingModal(true)
                        : () => {}
                    }
                  >
                    {t('view')}
                  </span>
                </span>
              </div>
            </div>
          )}
        <div className="mt-4 pt-6">
          {authData.get('user_type', 'student') === 'student' && (
            <div className="d-flex">
              <div className="bg-hovercolor f-14 border-radius-20 p-2 border_blue d-flex">
                <div className="pr-1">
                  {preparePercentage(courseSessionData.get(0, Map()))}%{' '}
                  {t('attendance')}
                </div>
                <div>
                  ({courseSessionData.getIn([0, 'attendedSessions'], 0)} /{' '}
                  {courseSessionData.getIn([0, 'completedSessions'], 0)}{' '}
                  {t('session_attended')})
                </div>
              </div>

              {currentTab === 'all' && (
                <div className="ml-3">
                  <div className="bg-hovercolor f-14 border-radius-20 p-2 border_blue">
                    {fixedTwoDigit(
                      courseSessionData.getIn([0, 'absentPercentage'], 0)
                    )}
                    % Warning Absent
                  </div>
                </div>
              )}

              {currentTab === 'all' && lateConfigSize > 0 && (
                <div className="ml-3 bg-errorLite f-14 border-radius-20 p-2 error_Border d-flex">
                  <div className="pr-1">{formattedLateAbsent}</div>
                  <CustomTooltip
                    placement="top-end"
                    title={
                      <div className="table-LateAbsent borderClass">
                        <table className="my-tableLateAbsent">
                          <thead>
                            <tr>
                              <th>Late Label</th>
                              <th>No of Late</th>
                              <th>Absence</th>
                            </tr>
                          </thead>
                          <tbody>
                            {lateConfig.map((value, indexKey) => (
                              <tr key={indexKey}>
                                <td>
                                  <div>
                                    <div>{value.get('labelName', '')}</div>
                                    <div className="text-warning">
                                      {`Setting*(${value.get(
                                        'noOfLate',
                                        ''
                                      )} Late = ${value.get(
                                        'noOfAbsent',
                                        ''
                                      )} Absent)`}
                                    </div>
                                  </div>
                                </td>
                                <td>{value.get('studentLate', '')}</td>
                                <td>{value.get('studentAbsent', '')}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    }
                  >
                    <InfoIcon
                      fontSize="small"
                      className="cursor-pointer"
                      sx={{ color: '#374151' }}
                    />
                  </CustomTooltip>
                </div>
              )}

              {currentTab === 'all' &&
                courseSessionData.getIn([0, 'warningData'], '') !== '' &&
                courseSessionData.getIn([0, 'warningData'], '') !== null && (
                  <div className="ml-3">
                    <div className="bg-lightwarning f-14 border-radius-20 p-2 border_warning">
                      {courseSessionData.getIn([0, 'warningData'], '')}
                    </div>
                  </div>
                )}
            </div>
          )}
        </div>
      </div>
      {ratingModal && (
        <Suspense fallback={<div>Loading...</div>}>
          <RatingModal
            ratingModal={ratingModal}
            setRatingModal={setRatingModal}
            courseSessionData={courseSessionData}
          />
        </Suspense>
      )}
    </>
  );
};

SessionHeader.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  courseSessionData: PropTypes.instanceOf(List).isRequired,
  userType: PropTypes.string.isRequired,
  authData: PropTypes.object.isRequired,
  currentTab: PropTypes.string.isRequired,
};

export default SessionHeader;
