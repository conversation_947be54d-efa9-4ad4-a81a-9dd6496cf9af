import React from 'react';
import PropTypes from 'prop-types';
import { getClassName } from '../../Utils/utils';
import { getTranslatedDuration } from '../../Utils/utils';
import { getUTCSessionTiming } from '../../../ReduxApi/util';

const SessionTimingCell = ({ schedule }) => (
  <p className="f-14 mb-0">
    <i
      className={getClassName('pl-2', 'pr-2', 'fa fa-clock-o')}
      aria-hidden="true"
    ></i>
    {getTranslatedDuration(getUTCSessionTiming({ schedule }))}
  </p>
);

SessionTimingCell.propTypes = {
  schedule: PropTypes.object.isRequired,
};

export default SessionTimingCell;
