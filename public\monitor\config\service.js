  // Configuration object
  config = {
    queryParams: new URLSearchParams(window.location.search),
    institutionApiUrl: 'https://digi-unify.service.digi-val.com/api/v1/institutions/institutionList',
    institutionApiHeaders: { 'digikey': '8bq1v%j!1Jyl7Gr^qm' },
    defaultCollegeCode: 'staging1',
    sessionApiHeaders: { 'digicronkey': 'Digicronfromdomain890731' },
    defaultBaseUrl: 'https://default-base-url.com/api/',
    defaultExamsApiUrl: 'https://daapi-release.gcp.digivalitsolutions.com/api/v1/misc/testcenter-status',
    defaultExamsApiKey: 'default-exams-api-key',
    defaultInstitution: {
        "status_code": 200,
        "status": true,
        "message": "DATA_RETRIEVED",
        "data": {
            "collegeList": [
                {
                    "collegeName": "Digival Institution",
                    "collegeCode": "staging1",
                    "type": "staging",
                    "socketUrl": "https://isnc-staging-dscron-yk25kmkzeq-el.a.run.app/",
                    "baseUrl": "https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/",
                    "faceUrl": "https://auth-staging.gcp.digivalitsolutions.com/api/",
                    "webUrl": "https://digiclass-staging1.gcp.digivalitsolutions.com/",
                    "blinkUrl": "https://digiclass-staging1.gcp.digivalitsolutions.com/liveness_mobile/mobile.html",
                    "chatApiKey": "r98xryxj6t7s",
                    "country": "Saudi Arabia",
                    "collegeImage": "https://www.easyuni.mu/media/institution/photo/2018/05/22/%D8%A7%D8%A8%D9%86_%D8%B3%D9%8A%D9%86%D8%A7.jpg.600x400_q85.jpg",
                    "configName": "ibnsina",
                    "smsService": false,
                    "biometricLogin": false,
                    "studentApiKey": "1Twgaaj8Z7bmKcfJy291BzNjdbAmxucI",
                    "staffApiKey": "FJcTLVkAD0DQKHPI6xgvc7zQRjnlYEfE",
                    "ApiEncryption": true,
                    "dcHelpUrl": "https://helper.gcp.digivalitsolutions.com/",
                    "hebaUrl": "https://gemini-pro-chatbot-yk25kmkzeq-el.a.run.app/vertexai_chat_bot",
                    "sso": true,
                    "ssoProvider": "teams",
                    "teams": {
                        "clientID": "82275301-feec-4d2d-9849-5381e05735b1",
                        "tenantId": "7cbb033f-083f-4a28-a233-1a73806d4e66",
                        "ios": {
                            "studentRedirectUri": "msauth.com.digival.digiclass.student://auth",
                            "staffRedirectUri": "msauth.com.digival.digiclass.staff://auth"
                        },
                        "android": {
                            "studentRedirectUri": "msauth://com.digivalsolutions.digiclass.student/4bC5ibotx1UZaWbqOlKmLDE576c%3D",
                            "staffRedirectUri": "msauth://com.digivalsolutions.digiclass.staff/zMkfhJUui7OkrdXGFOWV5bGqg20%3D"
                        }
                    },
                    "daService": {
                        "apiBaseUrl": "https://daapi-release.gcp.digivalitsolutions.com/api/v1/",
                        "apiServiceKey": "apikey123"
                    }
                }
            ]
        }
    }
};

let daVerifyPasswordUrl = "http://localhost:5000/api/v1/user/verify-admin-password";
let daForgotPasswordUrl = "https://digiassess-nonprod.gcp.digivalitsolutions.com/staging-daweb/login";
let userName = "<EMAIL>";
let password = "Monitor!SLA@2025";
