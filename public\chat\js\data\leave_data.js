leave_data = `{
  "studentScheduleData": [
      {
          "session": {
              "s_no": 1,
              "delivery_symbol": "L",
              "delivery_no": 1,
              "session_type": "Interactive lecture",
              "session_topic": "Structure and functions of the skin and its appendages (skin, hair, nail and glands)"
          },
          "_id": "64ddabf1917636fa75796944",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-08-24T10:44:33.814Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 2,
              "delivery_symbol": "L",
              "delivery_no": 2,
              "session_type": "Interactive lecture",
              "session_topic": "Disorders of sebaceous glands and sweat glands of the skin (e.g., acne vulgaris, miliaria …)."
          },
          "_id": "64ddac34917636daea7969b0",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "pending",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-08-24T11:05:33.954Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 8,
              "delivery_symbol": "CSL",
              "delivery_no": 2,
              "session_type": "Clinical skill lab",
              "session_topic": "History of respiratory system"
          },
          "_id": "64ddc603d989fdfd9ce1fca1",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-08-29T12:09:16.787Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 16,
              "delivery_symbol": "T",
              "delivery_no": 1,
              "session_type": "Tutorial",
              "session_topic": "Limb leads ECG reporting"
          },
          "_id": "64ddc8fa1c5c64e58631bff2",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-10-03T13:01:18.324Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 31,
              "delivery_symbol": "T",
              "delivery_no": 3,
              "session_type": "Tutorial",
              "session_topic": "Long strip ECG – 1 reporting"
          },
          "_id": "64ddcc4830a6afdf727f01e8",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-09-18T15:58:48.532Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 39,
              "delivery_symbol": "T",
              "delivery_no": 5,
              "session_type": "Tutorial",
              "session_topic": "Radiology: abnormal cardiac x-ray"
          },
          "_id": "64ddcc8130a6af03e37f030c",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 81,
              "delivery_symbol": "CSL",
              "delivery_no": 17,
              "session_type": "Clinical skill lab",
              "session_topic": "History of rheumatological diseases"
          },
          "_id": "64ddcd2a30a6afcd097f047c",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "auto",
                  "time": "2023-08-23T05:05:30.365Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 4,
              "delivery_symbol": "L",
              "delivery_no": 3,
              "session_type": "Interactive lecture",
              "session_topic": "Allergic and immunological skin disorders 1(e.g., eczema, urticarial, drug eruptions …)."
          },
          "_id": "64ddcfa1564e8fea98e048dd",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 3,
              "delivery_symbol": "CSL",
              "delivery_no": 1,
              "session_type": "Clinical skill lab",
              "session_topic": "Skin scrapping for fungus."
          },
          "_id": "64ddd04130a6af2dc27f05df",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 6,
              "delivery_symbol": "L",
              "delivery_no": 4,
              "session_type": "Interactive lecture",
              "session_topic": "Allergic and immunological skin disorders II (e.g., eczema, urticarial, drug eruptions …)"
          },
          "_id": "64ddd9422c9dcd31f86d3727",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-13T14:37:04.613Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 5,
              "delivery_symbol": "CSL",
              "delivery_no": 2,
              "session_type": "Clinical skill lab",
              "session_topic": "Wood’s lamp examination."
          },
          "_id": "64ddd97a2c9dcd59056d3747",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 8,
              "delivery_symbol": "L",
              "delivery_no": 5,
              "session_type": "Interactive lecture",
              "session_topic": "Blistering skin diseases and emergencies (e.g., pemphigus vulgaris, bullous pemphigoid …)."
          },
          "_id": "64ddd9c62c9dcd533b6d3770",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "pending",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 10,
              "delivery_symbol": "L",
              "delivery_no": 6,
              "session_type": "Interactive lecture",
              "session_topic": "Diseases of hair, nail and skin color (e.g., alopecia, vitilligo …)."
          },
          "_id": "64ddd9fc2c9dcd6c106d3795",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-09-21T12:04:44.393Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 9,
              "delivery_symbol": "CSL",
              "delivery_no": 4,
              "session_type": "Clinical skill lab",
              "session_topic": "Skin biopsy."
          },
          "_id": "64ddda312c9dcdcf036d37b5",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "DERMATOLOGY",
          "course_code": "DERM 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "_id": "64e03a83b6bc93856672d52c",
          "program_name": "Medicine",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "year_no": "year5",
          "level_no": "Level 9",
          "term": "regular",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-09-18T15:58:02.432Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 7,
              "delivery_symbol": "CSL",
              "delivery_no": 1,
              "session_type": "Clinical skill lab",
              "session_topic": "History of CVS"
          },
          "_id": "64e03b34b6bc936e4472d627",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-08-24T05:53:06.121Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 30,
              "delivery_symbol": "CSL",
              "delivery_no": 6,
              "session_type": "Clinical skill lab",
              "session_topic": "General examination"
          },
          "_id": "64e03ddffb437648b14611c7",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "auto",
                  "time": "2023-08-23T07:52:33.363Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 40,
              "delivery_symbol": "CSL",
              "delivery_no": 7,
              "session_type": "Clinical skill lab",
              "session_topic": "Basics of Auscultation"
          },
          "_id": "64e03e28fb437677704611ed",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "auto",
                  "time": "2023-08-23T10:03:05.039Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 9,
              "delivery_symbol": "CSL",
              "delivery_no": 3,
              "session_type": "Clinical skill lab",
              "session_topic": "History of abdominal/renal system"
          },
          "_id": "64e03e782c9dcd9a936d6649",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "reason": "As per institution guideline marked as Present",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 29,
              "delivery_symbol": "CSL",
              "delivery_no": 5,
              "session_type": "Clinical skill lab",
              "session_topic": "History of neurological system"
          },
          "_id": "64e03eab2c9dcd06736d6670",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "time": "2023-08-29T12:35:14.980Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 20,
              "delivery_symbol": "L",
              "delivery_no": 9,
              "session_type": "Interactive Lecture",
              "session_topic": "Bronchial asthma"
          },
          "_id": "64e03f322c9dcd67d26d66c7",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "reason": "As per institution guideline marked as Present",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 21,
              "delivery_symbol": "L",
              "delivery_no": 10,
              "session_type": "Interactive Lecture",
              "session_topic": "COPD"
          },
          "_id": "64e03f772c9dcd84356d66f3",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "manual",
                  "reason": "As per institution guideline marked as Present",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 35,
              "delivery_symbol": "L",
              "delivery_no": 12,
              "session_type": "Interactive Lecture",
              "session_topic": "Interstitial lung diseases"
          },
          "_id": "64e03fd02c9dcd33ab6d6737",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "auto",
                  "time": "2023-08-24T05:03:21.818Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 41,
              "delivery_symbol": "CSL",
              "delivery_no": 8,
              "session_type": "Clinical skill lab",
              "session_topic": "General and Local cardiac exam"
          },
          "_id": "64e068ac917636213179a675",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-08-29T05:40:30.876Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 42,
              "delivery_symbol": "CSL",
              "delivery_no": 9,
              "session_type": "Clinical skill lab",
              "session_topic": "General and Local chest exam"
          },
          "_id": "64e06900917636dcea79a69f",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-08-27T11:36:05.112Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 56,
              "delivery_symbol": "CSL",
              "delivery_no": 12,
              "session_type": "Clinical skill lab",
              "session_topic": "Examination of cranial nerves"
          },
          "_id": "64e069d291763614a779a71d",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-08-29T06:08:51.058Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 72,
              "delivery_symbol": "CSL",
              "delivery_no": 13,
              "session_type": "Clinical skill lab",
              "session_topic": "Examination of motor system"
          },
          "_id": "64e06a2a37d54b6a4dcbba75",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-08-28T07:15:35.973Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 54,
              "delivery_symbol": "CSL",
              "delivery_no": 10,
              "session_type": "Clinical skill lab",
              "session_topic": "Examination of Coordination and sensory system"
          },
          "_id": "64e06a7437d54b4684cbbaa0",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-08-28T10:06:25.748Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 55,
              "delivery_symbol": "CSL",
              "delivery_no": 11,
              "session_type": "Clinical skill lab",
              "session_topic": "General and Local abdominal exam"
          },
          "_id": "64e06ac937d54b0bfdcbbacb",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-08-30T05:05:06.380Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 73,
              "delivery_symbol": "CSL",
              "delivery_no": 14,
              "session_type": "Clinical skill lab",
              "session_topic": "Examination of the MSK"
          },
          "_id": "64e06b0d37d54b2b20cbbaf2",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-08-30T08:46:08.201Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 76,
              "delivery_symbol": "CSL",
              "delivery_no": 15,
              "session_type": "Clinical skill lab",
              "session_topic": "Instrumental techniques"
          },
          "_id": "64e06b3b37d54b27e3cbbb3b",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-08-30T10:58:26.383Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 34,
              "delivery_symbol": "L",
              "delivery_no": 11,
              "session_type": "Interactive Lecture",
              "session_topic": "Pneumonia"
          },
          "_id": "64e06bb8564e8f0bebe0779d",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "primaryTime": "2023-08-29T05:08:01.096Z",
                  "time": "2023-08-29T05:08:01.096Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 49,
              "delivery_symbol": "L",
              "delivery_no": 15,
              "session_type": "Interactive Lecture",
              "session_topic": "Pleural effusion"
          },
          "_id": "64e06bfd564e8f9499e07815",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "primaryTime": "2023-08-29T07:36:02.436Z",
                  "time": "2023-08-29T07:36:02.436Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 53,
              "delivery_symbol": "T",
              "delivery_no": 8,
              "session_type": "Tutorial",
              "session_topic": "Radiology: CT brain/ bone"
          },
          "_id": "64e06d05d989fd6a0ae2450b",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "primaryTime": "2023-08-29T10:07:56.367Z",
                  "time": "2023-08-29T10:07:56.367Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 36,
              "delivery_symbol": "L",
              "delivery_no": 13,
              "session_type": "Interactive Lecture",
              "session_topic": "Suppurative lung disease"
          },
          "_id": "64e06e9337d54b7b35cbc096",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-08-31T05:04:12.890Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 62,
              "delivery_symbol": "L",
              "delivery_no": 18,
              "session_type": "Interactive Lecture",
              "session_topic": "TB"
          },
          "_id": "64e06ec037d54b276acbc0d3",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-08-31T07:35:31.998Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 65,
              "delivery_symbol": "S",
              "delivery_no": 2,
              "session_type": "Seminar",
              "session_topic": "Approach to dyspnea"
          },
          "_id": "64e06f1137d54b338fcbc11e",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-08-30T12:49:09.981Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 1,
              "delivery_symbol": "L",
              "delivery_no": 1,
              "session_type": "Interactive Lecture",
              "session_topic": "Hypertension"
          },
          "_id": "64e0700ffb4376ea5e4618ca",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-05T05:14:27.283Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 3,
              "delivery_symbol": "L",
              "delivery_no": 3,
              "session_type": "Interactive Lecture",
              "session_topic": "Heart Failure"
          },
          "_id": "64e07046fb43767b04461908",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-05T08:12:04.187Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 43,
              "delivery_symbol": "T",
              "delivery_no": 6,
              "session_type": "Tutorial",
              "session_topic": "Radiology: abnormal chest x-ray"
          },
          "_id": "64e07097fb4376ba6646192f",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-05T11:39:44.437Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 2,
              "delivery_symbol": "L",
              "delivery_no": 2,
              "session_type": "Interactive Lecture",
              "session_topic": "Coronary artery disease/ACS"
          },
          "_id": "64e070dffb437689294619d4",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-07T07:07:21.295Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 6,
              "delivery_symbol": "L",
              "delivery_no": 6,
              "session_type": "Interactive Lecture",
              "session_topic": "Valvular heart disease-1"
          },
          "_id": "64e071c4b6bc9311b272de7b",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-12T07:00:39.065Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 18,
              "delivery_symbol": "L",
              "delivery_no": 7,
              "session_type": "Interactive Lecture",
              "session_topic": "Arrhythmia"
          },
          "_id": "64e071f6b6bc93291c72dedb",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-14T08:23:57.060Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 51,
              "delivery_symbol": "L",
              "delivery_no": 17,
              "session_type": "Interactive Lecture",
              "session_topic": "Pulmonary embolism"
          },
          "_id": "64e1b7c6ad3f28df8d165fcc",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "auto",
                  "time": "2023-09-19T05:10:19.985Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 37,
              "delivery_symbol": "L",
              "delivery_no": 14,
              "session_type": "Interactive Lecture",
              "session_topic": "Pulmonary hypertension Cor-pulmonale"
          },
          "_id": "64e1b80cd989fdff6ae27f8a",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-19T07:05:43.186Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 79,
              "delivery_symbol": "L",
              "delivery_no": 22,
              "session_type": "Interactive Lecture",
              "session_topic": "Lipid disorder"
          },
          "_id": "64e1b84fa868a58ec7ec3520",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-19T11:01:04.391Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 22,
              "delivery_symbol": "S",
              "delivery_no": 1,
              "session_type": "Seminar",
              "session_topic": "Approach to chest pain"
          },
          "_id": "64e1b899b47d1b1d50bf9c48",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-19T12:14:30.806Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 50,
              "delivery_symbol": "L",
              "delivery_no": 16,
              "session_type": "Interactive Lecture",
              "session_topic": "Bronchogenic carcinoma and para-malignant syndrome"
          },
          "_id": "64e1b8ccb47d1bf62cbf9c73",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-21T05:09:46.097Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 19,
              "delivery_symbol": "L",
              "delivery_no": 8,
              "session_type": "Interactive Lecture",
              "session_topic": "Respiratory failure"
          },
          "_id": "64e1b8fdb47d1b70bdbf9ca8",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "mode": "auto",
                  "time": "2023-09-21T08:25:52.238Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 4,
              "delivery_symbol": "L",
              "delivery_no": 4,
              "session_type": "Interactive Lecture",
              "session_topic": "Myocardial and Pericardial diseases"
          },
          "_id": "64e1b927b47d1b1432bf9d04",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-26T05:03:11.235Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 5,
              "delivery_symbol": "L",
              "delivery_no": 5,
              "session_type": "Interactive Lecture",
              "session_topic": "Infective endocarditis"
          },
          "_id": "64e1b95d1c5c6485b932334d",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "primaryStatus": "pending",
                  "status": "present",
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-26T07:05:06.882Z",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 113,
              "delivery_symbol": "L",
              "delivery_no": 34,
              "session_type": "Interactive Lecture",
              "session_topic": "HIV"
          },
          "_id": "64e1b9abee8abf5a246d38f0",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "primaryTime": "2023-09-26T11:03:50.439Z",
                  "time": "2023-09-26T11:03:50.439Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 12,
              "delivery_symbol": "HV",
              "delivery_no": 3,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bb933e02396d6f959874",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-04T05:11:19.309Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 10,
              "delivery_symbol": "HV",
              "delivery_no": 1,
              "session_type": "Hospital visit",
              "session_topic": "Hospital Round"
          },
          "_id": "64e1bc44c007190d15324247",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-03T07:31:04.548Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 13,
              "delivery_symbol": "HV",
              "delivery_no": 4,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bccab6bc93d7e4730a03",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-03T10:10:42.068Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 14,
              "delivery_symbol": "HV",
              "delivery_no": 5,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bd3dfcdfb40d230058b2",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-04T07:28:03.081Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 23,
              "delivery_symbol": "HV",
              "delivery_no": 6,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bd99b6bc9354d9730a4b",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-04T12:48:44.542Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 24,
              "delivery_symbol": "HV",
              "delivery_no": 7,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bddb3e0239a6c6959931",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-06T06:05:17.766Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 25,
              "delivery_symbol": "HV",
              "delivery_no": 8,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1be60928b42e03dfc6154",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "present",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "join_url": "",
                  "mode": "auto",
                  "time": "2023-09-06T07:40:31.890Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 26,
              "delivery_symbol": "HV",
              "delivery_no": 9,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bf15fb266d6c4d87920c",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-06T11:54:03.651Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 27,
              "delivery_symbol": "HV",
              "delivery_no": 10,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bf8ca54c7f85bab35beb",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-10T05:42:20.476Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 44,
              "delivery_symbol": "HV",
              "delivery_no": 11,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Learning"
          },
          "_id": "64e1bfeba41415bf0d65a5d2",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-10T07:44:59.907Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 45,
              "delivery_symbol": "HV",
              "delivery_no": 12,
              "session_type": "Hospital visit",
              "session_topic": "Hospital round-case based discussion"
          },
          "_id": "64e1c042a41415852865a615",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "pending",
                  "primaryStatus": "pending",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 46,
              "delivery_symbol": "HV",
              "delivery_no": 13,
              "session_type": "Hospital visit",
              "session_topic": "Hospital round-case based discussion"
          },
          "_id": "64e1c08ba41415040365a65c",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 47,
              "delivery_symbol": "HV",
              "delivery_no": 14,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e1c0bd1c5c6451393234d6",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "absent",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 48,
              "delivery_symbol": "HV",
              "delivery_no": 15,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e1c10d1c5c6482da32351f",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "pending",
                  "primaryStatus": "pending",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 57,
              "delivery_symbol": "HV",
              "delivery_no": 16,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e1c1691c5c640d9c323557",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-13T05:09:15.541Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 58,
              "delivery_symbol": "HV",
              "delivery_no": 17,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e1c1d8a868a533b7ec37fd",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-13T07:06:21.806Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 59,
              "delivery_symbol": "HV",
              "delivery_no": 18,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e1c2348efc48c705fea4d9",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "pending",
                  "primaryStatus": "pending",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 60,
              "delivery_symbol": "HV",
              "delivery_no": 19,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e32661fb266d92d987a54d",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-17T05:08:33.866Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 61,
              "delivery_symbol": "HV",
              "delivery_no": 20,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e32754fe1cc860d3b8e073",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-17T07:04:55.469Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 67,
              "delivery_symbol": "HV",
              "delivery_no": 21,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e327e3fb4376339646a28f",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-17T10:07:59.219Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 68,
              "delivery_symbol": "HV",
              "delivery_no": 22,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e3288cfe1cc8530db8e1c3",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-18T15:59:23.431Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 69,
              "delivery_symbol": "HV",
              "delivery_no": 23,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e328c8c007193a02326cba",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-18T07:10:44.394Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 70,
              "delivery_symbol": "HV",
              "delivery_no": 24,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e32906c007191640326d0a",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-18T15:59:47.546Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 71,
              "delivery_symbol": "HV",
              "delivery_no": 25,
              "session_type": "Hospital visit",
              "session_topic": "Case Based Discussion"
          },
          "_id": "64e329fbfb43769fc946a467",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-20T06:35:22.654Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 83,
              "delivery_symbol": "HV",
              "delivery_no": 26,
              "session_type": "Hospital visit",
              "session_topic": "Hospital Round"
          },
          "_id": "64e32a55fe1cc80c1eb8e2e0",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-20T07:40:56.323Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 84,
              "delivery_symbol": "HV",
              "delivery_no": 27,
              "session_type": "Hospital visit",
              "session_topic": "Hospital Round"
          },
          "_id": "64e32adec007199975326e75",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "manual",
                  "time": "2023-09-21T07:46:48.449Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 85,
              "delivery_symbol": "HV",
              "delivery_no": 28,
              "session_type": "Hospital visit",
              "session_topic": "Hospital Round"
          },
          "_id": "64e32b23fb43761e4146a50b",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year5",
          "level_no": "Level 9",
          "course_name": "INTERNAL MEDICINE",
          "course_code": "MED 509",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "last": "HAMMADI",
                      "middle": "AYED",
                      "family": "ALSEFRI"
                  },
                  "status": "present",
                  "primaryStatus": "absent",
                  "_id": "600433a93fa31926505c302e",
                  "reasonIds": [],
                  "mode": "auto",
                  "time": "2023-09-25T05:43:32.691Z"
              }
          ]
      },
   
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-22T07:15:34.984Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 113,
              "delivery_symbol": "L",
              "delivery_no": 40,
              "session_type": "Interactive lecture",
              "session_topic": "Pelvic floor dysfunction"
          },
          "_id": "659155dbad1c4cc0ad30c380",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "status": "present",
                  "primaryStatus": "present",
                  "mode": "auto",
                  "reasonIds": [],
                  "time": "2024-03-05T06:58:02.028Z",
                  "primaryTime": "2024-03-05T06:58:02.028Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 30,
              "delivery_symbol": "HV",
              "delivery_no": 8,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 8"
          },
          "_id": "659155e3155063286f127b7e",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-22T08:50:27.235Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 31,
              "delivery_symbol": "HV",
              "delivery_no": 9,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 9"
          },
          "_id": "659156847492c45df17ada06",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-22T12:59:56.322Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 39,
              "delivery_symbol": "HV",
              "delivery_no": 10,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 10"
          },
          "_id": "659156aa308c65514c1d34c5",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "absent",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-25T18:03:40.408Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 40,
              "delivery_symbol": "HV",
              "delivery_no": 11,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 11"
          },
          "_id": "659156cdd9171243981cb770",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "absent",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-25T18:02:41.331Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 41,
              "delivery_symbol": "HV",
              "delivery_no": 12,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 12"
          },
          "_id": "659156f746c2141d866852f4",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "absent",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-25T17:57:39.760Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 112,
              "delivery_symbol": "L",
              "delivery_no": 39,
              "session_type": "Interactive lecture",
              "session_topic": "Pelvic floor dysfunction"
          },
          "_id": "65915723155063e935127bc7",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "status": "present",
                  "primaryStatus": "present",
                  "mode": "auto",
                  "reasonIds": [],
                  "time": "2024-03-05T05:42:53.518Z",
                  "primaryTime": "2024-03-05T05:42:53.518Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 47,
              "delivery_symbol": "HV",
              "delivery_no": 13,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 13"
          },
          "_id": "65915757c71102bf594d6473",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-29T12:25:38.928Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 48,
              "delivery_symbol": "HV",
              "delivery_no": 14,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 14"
          },
          "_id": "65915793155063f0bc127bdb",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-29T07:52:28.028Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 49,
              "delivery_symbol": "HV",
              "delivery_no": 15,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 15"
          },
          "_id": "659157c7673a7f624162dfd4",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-01-29T12:26:50.241Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 57,
              "delivery_symbol": "HV",
              "delivery_no": 16,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit 16"
          },
          "_id": "65915816673a7fc8bf62dff2",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "pending",
                  "primaryStatus": "pending",
                  "reasonIds": []
              }
          ]
      },
      {
          "session": {
              "s_no": 58,
              "delivery_symbol": "HV",
              "delivery_no": 17,
              "session_type": "Hospital visit",
              "session_topic": "Hospital visit  17"
          },
          "_id": "6591586403bc331b499bc4b2",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "absent",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-02-01T20:52:17.999Z"
              }
          ]
      },
      {
          "session": {
              "s_no": 94,
              "delivery_symbol": "L",
              "delivery_no": 33,
              "session_type": "Interactive lecture",
              "session_topic": "Ovarian massess 1"
          },
          "_id": "65915885f070bb646d2d7db3",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "join_url": "",
                  "status": "present",
                  "primaryStatus": "present",
                  "mode": "auto",
                  "reasonIds": [],
                  "time": "2024-03-03T07:24:52.083Z",
                  "primaryTime": "2024-03-03T07:24:52.083Z"
              }
          ]
      },
    
          "_id": "65915929eb0d13136bd1bda5",
          "program_name": "Medicine",
          "term": "regular",
          "year_no": "year6",
          "level_no": "Level 10",
          "course_name": "Obstetrics and gynecology",
          "course_code": "OBG 612",
          "students": [
              {
                  "name": {
                      "first": "AFNAN",
                      "middle": "AYED",
                      "last": "HAMMADI",
                      "family": "ALSEFRI"
                  },
                  "_id": "600433a93fa31926505c302e",
                  "status": "present",
                  "primaryStatus": "absent",
                  "mode": "manual",
                  "reasonIds": [],
                  "time": "2024-02-05T13:17:02.333Z"
              }
          ]
      },
   
   
     
          ]
      },
    `;
