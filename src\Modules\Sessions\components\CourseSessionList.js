import React, {
  useCallback,
  useEffect,
  useState,
  useContext,
  lazy,
  Suspense,
} from 'react';
import { withRouter, useLocation } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

import { useTranslation } from 'react-i18next';
import * as actions from '../../../ReduxApi/Sessions/action';
import * as chatActions from '../../../ReduxApi/Chat/action';
import {
  selectSessionData,
  selectCourseListWithSessions,
  selectIsLoading,
  selectCourseSessionTabList,
} from '../../../ReduxApi/Sessions/selectors';
import { selectAuthData } from '../../../ReduxApi/User/selectors';
import { selectUnreadCount } from '../../../ReduxApi/Chat/selectors';

import Activities from '../../Activities/components/activities';
import Documents from '../../Documents/components/CourseDocument';
import {
  CapitalizeAll,
  eString,
  dString,
  removeURLParams,
  getURLParams,
} from '../../../ReduxApi/util';
import ChatDashboard from '../../Chat/ChatDashboard';

import { ChatContext } from '../../../Context/ChatServices';
import {
  isChatEnabled,
  getEnvLabelChanged,
  levelRename,
  getVersionName,
  isChatly,
  isDefaultChat,
} from '../../../utils';
import useCalendar from 'Hooks/CalendarHook';
import NewChatDashboard from 'Modules/Chat/Components/newChat/NewChatDashboard';
import ForumDiscussionIndex from './ForumDiscussion.js';

import Assignment from './Assignment';
import LocalStorageService from 'LocalStorageService';

import WarningCard from './WarningCards/WarningCard';
import TabNavigation from './TabNavigation';
import SessionFilters from './SessionFilters';
import SessionTable from './SessionTable';
import SessionHeader from './SessionHeader';
import Reports from './Reports';
import CourseSettings from './CourseSettings/CourseSettings';

const AllStudentsComponent = lazy(() => import('./AllStudents'));
const ExportDialog = lazy(() => import('../modal/ExportPopUp'));

// Custom hooks
const useTabNavigation = (initialTab) => {
  const [activeTab, setActiveTab] = useState(() => {
    const savedTab = LocalStorageService.getCustomToken('activeAllTab');
    return savedTab || initialTab;
  });

  const navigateToTab = useCallback((tabName) => {
    setActiveTab(tabName);
    LocalStorageService.setCustomToken('activeAllTab', tabName);
  }, []);

  return { activeTab, navigateToTab };
};

const useChatChannels = (chatClient, userId, currentCourseId, setChatData) => {
  const [chatChannels, setChatChannels] = useState([]);

  const initChannel = useCallback(async () => {
    if (!isChatEnabled()) return;

    const filters = {
      type: { $in: ['team'] },
      members: { $in: [userId] },
      _course_id: currentCourseId,
      term: getURLParams('term', true),
      rotation: getURLParams('rotation', true),
      year: getURLParams('_year_no', true),
      level: getURLParams('_level_no', true),
    };

    if (getURLParams('rotation', true) === 'yes') {
      filters.rotation_count = parseInt(getURLParams('rotation_count', true));
    }

    const sort = { last_message_at: -1 };
    const options = { watch: true, state: true };

    try {
      const channels = await chatClient.queryChannels(filters, sort, options);
      const filteredChannels = channels.filter((channel) => {
        const { name, _course_id } = channel.data;
        return (
          name &&
          _course_id &&
          _course_id.toString() === currentCourseId.toString()
        );
      });

      const unReadCount = filteredChannels.filter(
        (channel) => channel?.state?.unreadCount > 0
      );

      setChatChannels(filteredChannels);
      setChatData(
        Map({
          unReadCount: unReadCount?.length || 0,
        })
      );
    } catch (error) {
      console.error('Error initializing chat channels:', error);
    }
  }, [chatClient, userId, setChatData, currentCourseId]);

  return { chatChannels, initChannel };
};

const useBreadcrumb = (
  courseSessionData,
  authData,
  setBreadCrumbName,
  programId
) => {
  const { t } = useTranslation();

  useEffect(() => {
    if (courseSessionData.size > 0) {
      const course = courseSessionData.get(0);
      const breadcrumb = `<b>${course.get('course_code', '')} : ${course.get(
        'course_name',
        ''
      )}${getVersionName(course)}</b>
        <p>
        ${
          authData.get('user_type', '') === 'staff'
            ? `<i class="fa fa-star text-warning pr-2 pl-2" aria-hidden="true"></i>${course.getIn(
                ['feedback', 'avgRating'],
                '0.0'
              )} • `
            : ''
        }
        ${course.get('program_name', '')} • ${t('year')} ${course
        .get('year', '')
        .replace('year', '')} • 
        ${
          getEnvLabelChanged()
            ? levelRename(course.get('level', ''), programId)
            : `${t('level')} ${course.get('level', '').replace('Level', '')}`
        }  •  ${CapitalizeAll(course.get('term', 'regular'))}
        </p>`;
      setBreadCrumbName(breadcrumb);
    } else {
      const courseCode = dString(getURLParams('_course_code'));
      const courseName = dString(getURLParams('_course_name'));
      setBreadCrumbName(`<b>${courseCode} : ${courseName}</b>`);
    }
  }, [courseSessionData, setBreadCrumbName, authData, t, programId]);
};

function CourseSessionList(props) {
  const { courseId } = props.match.params;
  const [currentCourseId] = useState(dString(courseId));
  const [programId] = useState(dString(getURLParams('_program_id')));
  const current_tab = getURLParams('current_tab');
  const [currentPage, setCurrentPage] = useState(1);

  const [selectedSessionFilter, setSelectedSessionFilter] = useState('today');
  const {
    authData,
    courseSessionData,
    setBreadCrumbName,
    isLoading,
    unReadCount,
    setChatData,
    courseSessionTabList,
    tabsApi,
    currentTab,
    // getCourseSessionReport,
  } = props;
  const location = useLocation();
  const chatClient = useContext(ChatContext);
  const { isCurrentAcademicYear } = useCalendar();
  const userType = authData.get('user_type', '');
  const isDigiChatEnabled = isChatly();

  // Custom hooks
  const { activeTab, navigateToTab } = useTabNavigation(
    current_tab !== 'forum' ? 'sessionView' : 'discussionView'
  );
  const { chatChannels, initChannel } = useChatChannels(
    chatClient,
    authData.get('_id', ''),
    currentCourseId,
    setChatData
  );
  useBreadcrumb(courseSessionData, authData, setBreadCrumbName, programId);

  useEffect(() => {
    setSelectedSessionFilter(currentTab);
  }, [currentTab]);

  useEffect(() => {
    return () => localStorage.removeItem('activeAllTab');
  }, []);

  const term = getURLParams('term', true);
  const rotation = getURLParams('rotation', true);
  const year = getURLParams('_year_no', true);
  const level = getURLParams('_level_no', true);
  const _cid = getURLParams('_cid', true);
  const rotationCount = getURLParams('rotation_count', true);
  console.log('rotationCount: ', rotationCount);

  useEffect(() => {
    if (isChatEnabled()) {
      initChannel();
    }
  }, [initChannel]);

  useEffect(() => {
    if (chatChannels && activeTab !== 'chatView') {
      const myChannelEventHandler = () => {
        initChannel();
      };
      chatChannels.forEach(async (channel) => {
        channel.on('message.new', myChannelEventHandler);
        channel.on('user.banned', myChannelEventHandler);
        channel.on('user.unbanned', myChannelEventHandler);
      });
      return () =>
        chatChannels.forEach(async (channel) => {
          channel.off('message.new', myChannelEventHandler);
          channel.off('user.banned', myChannelEventHandler);
          channel.off('user.unbanned', myChannelEventHandler);
        });
    }
  }, [chatChannels, initChannel, activeTab]);

  useEffect(() => {
    if (location?.state?.isLoadChat) {
      onTabChange('chatView');
    } else if (location?.state?.isLoadRestrict) {
      if (location?.state?.currentPage !== '') {
        setCurrentPage(parseInt(location?.state?.currentPage));
      }
      onTabChange('documentsView');
    } else if (location?.state?.isLoadActivity) {
      onTabChange('activitiesView');
    }
  }, [location]);

  const handleFilterChange = useCallback(
    (filterType) => {
      setSelectedSessionFilter(filterType);
      tabsApi({ type: filterType });
    },
    [tabsApi]
  );

  function goToSession(data) {
    const { history } = props;
    const adminCourses = getURLParams('_admin_courses', true);
    history.push(
      `/sessions/document?_id=${eString(
        data.getIn(['session', '_session_id'], '')
      )}&_schedule_id=${eString(data.get('_id', ''))}&_course_id=${eString(
        data.get('_course_id')
      )}&_program_id=${eString(data.get('_program_id'))}&_level_no=${eString(
        data.get('level_no')
      )}&_merge_status=${eString(data.get('merge_status'))}&_year_no=${eString(
        data.get('year_no')
      )}&term=${eString(data.get('term'))}&_cid=${eString(
        data.get('_institution_calendar_id')
      )}&_type=${eString(data.get('type'))}&rotation=${eString(
        data.get('rotation', 'no')
      )}&rotation_count=${eString(
        data.get('rotation_count', 0) !== null
          ? data.get('rotation_count', 0)
          : 0
      )}&_admin_courses=${eString(adminCourses)}&mode=${eString(
        data.get('mode', '')
      )}&course_code=${eString(
        data.get('course_code', '')
      )}&course_name=${eString(data.get('course_name', ''))}`
    );
  }

  function exportEnable() {
    return (
      userType === 'staff' && getURLParams('_admin_courses', true) !== 'true'
    );
  }

  const [exportPopUpOpen, setExportPopUpOpen] = useState(false);

  const commonParamsData = Map({
    institutionCalendarId: _cid,
    merge_status: getURLParams('_merge_status', true),
    admin_course: getURLParams('_admin_courses', true),
    programId,
    term,
    currentCourseId,
    rotation,
    year,
    level,
    rotation_count: getURLParams('rotation_count', true),
  });

  const exportEnabled = exportEnable();
  const handleScrollLeft = useCallback(() => {
    const container = document.getElementById('tabScrollContainer');
    if (container) container.scrollLeft -= 100;
  }, []);

  const handleScrollRight = useCallback(() => {
    const container = document.getElementById('tabScrollContainer');
    if (container) container.scrollLeft += 100;
  }, []);
  const arrowShow = exportEnabled && courseSessionTabList.size > 5;

  // Event handlers
  const handleSessionClick = useCallback(
    (schedule) => {
      goToSession(schedule, props.history);
    },
    [props.history] //eslint-disable-line
  );

  const handleTabChange = useCallback(
    (tabName) => {
      navigateToTab(tabName);
      const { history } = props;
      history.push({
        pathname: location.pathname,
        search: removeURLParams(location.search, ['chat', 'index']),
      });
    },
    [navigateToTab, props, location]
  );

  const handleExportPopUp = useCallback(() => {
    setExportPopUpOpen(!exportPopUpOpen);
  }, [exportPopUpOpen]);

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'sessionView':
        return (
          <>
            <SessionFilters
              filters={courseSessionTabList}
              selectedFilter={selectedSessionFilter}
              onFilterChange={handleFilterChange}
              showArrows={arrowShow}
              onScrollLeft={handleScrollLeft}
              onScrollRight={handleScrollRight}
              exportEnabled={exportEnabled}
              onExportClick={handleExportPopUp}
            />
            <div className="pl-4 pr-4 min_height_400">
              <SessionHeader
                isLoading={isLoading}
                courseSessionData={courseSessionData}
                userType={userType}
                authData={authData}
                currentTab={currentTab}
              />
              <SessionTable
                sessions={courseSessionData.getIn([0, 'sessions'], List())}
                authData={authData}
                userType={userType}
                programId={programId}
                onSessionClick={handleSessionClick}
              />
            </div>
          </>
        );
      case 'documentsView':
        return (
          <Documents
            courseId={currentCourseId}
            docType="course"
            paginationNo={currentPage}
          />
        );
      case 'activitiesView':
        return <Activities courseId={currentCourseId} />;
      case 'chatView':
        return isCurrentAcademicYear() ? (
          isDefaultChat() ? (
            <ChatDashboard courseChatHeight={true} modelType="all" />
          ) : (
            isDigiChatEnabled && (
              <NewChatDashboard
                modelType="courseWise"
                courseId={currentCourseId}
                rotationCount={
                  getURLParams('rotation', true) === 'yes'
                    ? getURLParams('rotation_count', true)
                    : null
                }
                term={getURLParams('term', true)}
              />
            )
          )
        ) : null;
      case 'discussionView':
        return (
          <ForumDiscussionIndex
            programId={programId}
            authData={authData}
            commonParamsData={commonParamsData}
          />
        );
      case 'allStudentsView':
        return (
          <Suspense fallback="">
            <AllStudentsComponent data={commonParamsData} />
          </Suspense>
        );
      case 'allAssignmentView':
        return <Assignment courseId={currentCourseId} />;
      case 'warningNew':
        return (
          <WarningCard
            courseId={currentCourseId}
            programId={programId}
            term={term}
            year={year}
            level={level}
            rotation={rotation}
            rotationCount={rotationCount}
            institutionCalenderId={_cid}
          />
        );
      case 'reportsView':
        return <Reports />;
      case 'courseWiseSettingView':
        return (
          <CourseSettings
            courseId={currentCourseId}
            programId={programId}
            term={term}
            year={year}
            level={level}
            rotation={rotation}
            rotationCount={rotationCount}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="main w-100">
      <div className="container-fluid digi-document-bg p-0">
        <TabNavigation
          activeTab={activeTab}
          onTabChange={handleTabChange}
          unReadCount={unReadCount}
          userType={userType}
          isCurrentAcademicYear={isCurrentAcademicYear}
          courseSessionData={courseSessionData}
        />

        {renderContent()}

        {exportPopUpOpen && (
          <Suspense fallback="Loading...">
            <ExportDialog
              exportPopUpOpen={exportPopUpOpen}
              handleClose={handleExportPopUp}
            />
          </Suspense>
        )}
      </div>
    </div>
  );
}

CourseSessionList.propTypes = {
  history: PropTypes.object,
  SessionListData: PropTypes.object,
  authData: PropTypes.object,
  match: PropTypes.object,
  setBreadCrumbName: PropTypes.func,
  courseSessionData: PropTypes.instanceOf(List),
  goToSession: PropTypes.func,
  isLoading: PropTypes.bool,
  unReadCount: PropTypes.number,
  setChatData: PropTypes.func,
  courseSessionTabList: PropTypes.instanceOf(List),
  tabsApi: PropTypes.func,
  currentTab: PropTypes.string,
  getCourseSessionReport: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    SessionListData: selectSessionData(state),
    courseSessionData: selectCourseListWithSessions(state),
    authData: selectAuthData(state),
    isLoading: selectIsLoading(state),
    unReadCount: selectUnreadCount(state),
    courseSessionTabList: selectCourseSessionTabList(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, ...chatActions })
)(CourseSessionList);
