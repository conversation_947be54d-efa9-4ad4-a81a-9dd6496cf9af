import { useSelector } from 'react-redux';
import { selectAuthData } from 'ReduxApi/User/selectors';

const useAuthHook = () => {
  const authData = useSelector(selectAuthData);

  const isComprehensiveMode =
    authData.get('warningMode', '') === 'comprehensive';

  const isStudentComprehensiveEnabled =
    authData.get('user_type', '') === 'student' && isComprehensiveMode;

  const userId = authData.get('_id', '');

  return {
    isComprehensiveMode,
    isStudentComprehensiveEnabled,
    userId,
  };
};

export default useAuthHook;
