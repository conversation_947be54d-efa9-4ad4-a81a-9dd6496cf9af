.rules_staging:
  rules:
    - if: $CI_COMMIT_BRANCH == "staging1"
      when: manual

build staging1:
  stage: build
  environment: staging1
  interruptible: true
  extends:
    - .rules_staging
    - .build
  variables:
    ECR_STAGING: $ECR:staging1
  script:
    - docker build --build-arg buildStage=ecs-staging1 --tag $ECR_STAGING .
    - docker push $ECR_STAGING

deploy to staging1:
  stage: deploy
  environment: staging1
  extends:
    - .aws_image
    - .ecs_update_service
