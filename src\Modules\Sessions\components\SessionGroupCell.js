import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import {
  getFormattedGroupName,
  studentGroupRename,
  getStudentGroupsNew,
} from '../../../ReduxApi/util';
import { studentGroupViewList } from '../../../utils';

const SessionGroupCell = ({ schedule, programId }) => (
  <div className="f-14 digi-set-rating-black digi-font-500">
    {schedule.get('type', '') !== 'regular'
      ? studentGroupViewList(schedule.get('student_groups', List()), programId)
          .entrySeq()
          .map(([groupName, sGroup]) => {
            const deliverySymbol = sGroup.get('delivery_symbol', '');
            const sessionGroup = ` -${sGroup.get('session_group', '')}`;
            return (
              getFormattedGroupName(
                studentGroupRename(groupName, programId),
                2
              ) + `${deliverySymbol === '' ? '' : sessionGroup}`
            );
          })
          .join(', ')
      : getStudentGroupsNew(schedule.get('student_groups', List()), programId)}
  </div>
);

SessionGroupCell.propTypes = {
  schedule: PropTypes.object.isRequired,
  programId: PropTypes.string.isRequired,
};

export default SessionGroupCell;
