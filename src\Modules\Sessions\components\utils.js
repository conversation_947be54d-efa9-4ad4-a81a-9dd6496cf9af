import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import {
  formatSingleDigit,
  getCollegeLogo,
  getEnvCollegeName,
  getVersionName,
  isLateAbsentEnabled,
  jsUcFirstAll,
} from 'utils';
import moment from 'moment';
import { List, Map, fromJS } from 'immutable';
import {
  formattedFullName,
  eString,
  getURLParams,
  checkEnableSession,
} from '../../../ReduxApi/util';

function toDataURL(src, callback, outputFormat) {
  let image = new Image();
  image.crossOrigin = 'Anonymous';
  image.onload = function () {
    let canvas = document.createElement('canvas');
    let ctx = canvas.getContext('2d');
    let dataURL;
    canvas.height = this.naturalHeight;
    canvas.width = this.naturalWidth;
    ctx.drawImage(this, 0, 0);
    dataURL = canvas.toDataURL(outputFormat);
    callback(dataURL);
  };
  image.src = src;
  if (image.complete || image.complete === undefined) {
    image.src = src;
  }
}

export const exportExcel = (authData, data) => {
  const workbook = new ExcelJS.Workbook({ excelValidate: false });
  let worksheet = workbook.addWorksheet('Course Sessions Report', {
    views: [{ state: 'frozen', ySplit: 11 }],
  });
  const programName = data.getIn(['courseDetails', 'program_name'], '');
  const courseName = data.getIn(['courseDetails', 'course_name'], '');
  const courseCode = data.getIn(['courseDetails', 'course_code'], '');

  const isLateEnable = isLateAbsentEnabled();

  const exportBy =
    jsUcFirstAll(authData.getIn(['name', 'first'], '')) +
    ' ' +
    jsUcFirstAll(authData.getIn(['name', 'middle'], '')) +
    ' ' +
    jsUcFirstAll(authData.getIn(['name', 'last'], ''));

  function getStatusName(status, lateLabel, isRestricted) {
    const statusData = {
      absent: 'A',
      present: 'P',
      leave: 'L',
      onduty: 'OD',
      permission: 'Per',
      exclude: 'E',
    };
    return statusData?.[status] !== undefined
      ? `${statusData[status]} ${lateLabel !== null ? `(${lateLabel})` : ''} ${
          isRestricted && status === 'absent' ? '(Course Restricted)' : ''
        } `
      : status;
  }

  function returnStudentStatus(userId, scheduleId, status, isActive) {
    const findArray = data
      .get('studentsData', List())
      .find((item) => item.get('_id', '') === userId);

    if (findArray) {
      const findSession = findArray
        .get('schedules', List())
        .find((item) => item.get('scheduleId', '') === scheduleId);
      if (findSession && !isActive) return 'Canceled';
      if (findSession && status === 'missed') return jsUcFirstAll(status);
      if (findSession) {
        const hasTardis =
          findSession.get('tardisId', null) !== null &&
          findSession.getIn(['tardisId', 'name'], '') !== '';
        return `${jsUcFirstAll(
          getStatusName(
            findSession.get('status', ''),
            findSession.get('lateLabel', ''),
            findSession.get('isRestricted', false)
          )
        )} ${
          hasTardis
            ? `(${jsUcFirstAll(findSession.getIn(['tardisId', 'name'], ''))})`
            : ``
        }`;
      }
    }
    return '-';
  }

  function getStudentSummary(userId) {
    const findArray = data
      .get('studentsData', List())
      .find((item) => item.get('_id', '') === userId);
    if (findArray) {
      let data = [
        findArray.get('schedules', List()).size,
        findArray
          .get('schedules', List())
          .filter((item) => item.get('status', '') === 'present').size,
        findArray
          .get('schedules', List())
          .filter((item) => item.get('status', '') === 'absent').size,
        findArray
          .get('schedules', List())
          .filter((item) => item.get('status', '') === 'leave').size,
        findArray
          .get('schedules', List())
          .filter((item) => item.get('status', '') === 'onduty').size,
        findArray
          .get('schedules', List())
          .filter((item) => item.get('status', '') === 'permission').size,
        findArray
          .get('schedules', List())
          .filter((item) => item.get('status', '') === 'pending').size,
      ];

      if (isLateEnable) {
        const lateAbsentValue = findArray.get('studentLateAbsent', 0);
        data.splice(5, 0, lateAbsentValue);
      }

      return data;
    }
    return [];
  }

  const attendanceHeader = data.get('attendanceHeader', List());
  const rowsData = data
    .get('studentsData', List())
    .map((student, index) =>
      [
        index + 1,
        formattedFullName(student.get('name', Map()).toJS()),
        student.get('academicId', ''),
        student.get('warningLabel', '-'),
        getStudentSummary(student.get('_id', '')),
        [
          ...attendanceHeader.map((schedule) =>
            returnStudentStatus(
              student.get('_id', ''),
              schedule.get('schedule_id', ''),
              schedule.get('schedule_status', ''),
              schedule.get('isActive', false)
            )
          ),
        ],
      ].flat()
    )
    .toJS();
  const sessionTopicRow = attendanceHeader
    .map((item) => item.get('session_topic'))
    .toJS();
  const scheduleDateTime = attendanceHeader.map((item) => getDate(item)).toJS();
  function prefixZero(num) {
    if (typeof num !== 'number') return num;
    return num < 10 ? `0${num}` : num;
  }

  function getScheduleTime(schedule) {
    const start = schedule.get('start', Map());
    const end = schedule.get('end', Map());
    if (start.isEmpty() || end.isEmpty()) return '';
    return `${start.get('hour')}:${prefixZero(
      start.get('minute', 0)
    )} ${start.get('format')} - ${end.get('hour')}:${prefixZero(
      end.get('minute', 0)
    )} ${end.get('format')}`;
  }

  function getDate(item) {
    return `${moment(item.get('schedule_date')).format(
      'YYYY-MM-DD'
    )} ${getScheduleTime(item)}`;
  }

  const headerWithKeys = [
    { name: 'S.N0', width: 30 },
    { name: 'STUDENT NAME', width: 60 },
    { name: 'ACADEMIC ID', width: 30 },
    { name: 'WARNING', width: 20 },
    { name: 'TOTAL SESSIONS', width: 20 },
    { name: 'TOTAL PRESENTS', width: 20 },
    { name: 'TOTAL ABSENTS', width: 20 },
    { name: 'TOTAL LEAVES', width: 20 },
    { name: 'TOTAL ON-DUTY', width: 20 },
    { name: 'TOTAL PERMISSIONS', width: 25 },
    { name: 'TOTAL PENDING', width: 20 },
    ...attendanceHeader.map((item) => {
      return {
        name: `${item.get('session', '').toUpperCase()}`,
        width: 50,
      };
    }),
  ];

  if (isLateEnable) {
    const newObject = { name: 'TOTAL LATE ABSENTS', width: 20 };
    headerWithKeys.splice(9, 0, newObject);
  }

  const headerOnly = headerWithKeys.map((item) => item.name);

  function insertHeaderIfLateEnable(list, header, lateEnable) {
    const index = list.indexOf(header);
    if (lateEnable) {
      list.splice(index - 1, 0, '');
    }
  }

  const sessionTopicList = [
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    'SESSION TOPIC',
    ...sessionTopicRow,
  ];
  insertHeaderIfLateEnable(sessionTopicList, 'SESSION TOPIC', isLateEnable);

  const scheduleDateTimeList = [
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    'SCHEDULE DATE & TIME',
    ...scheduleDateTime,
  ];
  insertHeaderIfLateEnable(
    scheduleDateTimeList,
    'SCHEDULE DATE & TIME',
    isLateEnable
  );

  const collegeName = getEnvCollegeName();
  let rows = [
    ['', ''],
    ['', `     ` + collegeName],
    ['', '', '', '', '', '', '', '', '', '', '', 'P - Present'],
    [
      'Program Name',
      programName,
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'A - Absent',
    ],
    [
      'Course Code / Name',
      `${courseCode} - ${courseName}${getVersionName(
        data.get('courseDetails', Map())
      )}`,
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'L - Leave',
    ],
    [
      'Report Name',
      'Course Sessions Report',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'Per - Permission',
    ],
    [
      'Exported Date & Time',
      moment(new Date()).format('DD-MMM-YYYY & H:mm:ss'),
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'OD - On-Duty',
    ],
    ['Exported by', exportBy],
    sessionTopicList,
    scheduleDateTimeList,
    headerOnly,
  ];

  rows = rows.concat(rowsData);

  worksheet.columns = headerWithKeys;
  worksheet.addRows(rows);

  const boldSideHeader = (cell) => {
    worksheet.getCell(cell).font = {
      bold: true,
    };
  };
  for (let i = 4; i < 9; i++) {
    boldSideHeader('A' + i);
  }

  const backgroundColor = '6d9eeb';

  const targetRow = worksheet.getRow(11);
  const targetRow1 = worksheet.getRow(10);
  const targetRow2 = worksheet.getRow(9);

  for (let i = 1; i <= headerOnly.length; i++) {
    const cell = targetRow.getCell(i);
    // Set the background fill color for the target column
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: backgroundColor },
    };
    cell.font = {
      color: { argb: 'FFFFFF' },
      bold: true,
    };

    cell.alignment = { vertical: 'top', horizontal: 'left' };

    const lateEnableBgColor = isLateEnable ? 11 : 10;
    if (i > lateEnableBgColor) {
      const cell1 = targetRow1.getCell(i);
      // Set the background fill color for the target column
      cell1.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: backgroundColor },
      };
      cell1.font = {
        color: { argb: 'FFFFFF' },
        bold: true,
      };

      cell1.alignment = { vertical: 'top', horizontal: 'left' };

      const cell2 = targetRow2.getCell(i);
      // Set the background fill color for the target column
      cell2.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: backgroundColor },
      };
      cell2.font = {
        color: { argb: 'FFFFFF' },
        bold: true,
      };

      cell2.alignment = { vertical: 'top', horizontal: 'left' };
    }
  }

  const warningCell = targetRow.getCell(4);
  warningCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FF0000' },
  };

  worksheet.eachRow({ includeEmpty: false }, function (row, rowNumber) {
    if ([1, 2, 3].includes(rowNumber)) {
      row.eachCell({ includeEmpty: false }, function (cell, cellNumber) {
        if (cellNumber < 3) {
          cell.style.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: cellNumber === 2 ? '6d9eeb' : 'FFFFFF' },
          };
          cell.style.font = {
            color: {
              argb: rowNumber === 1 && cellNumber === 2 ? '6d9eeb' : 'FFFFFF',
            },
            size: 15,
            // bold: cellNumber === 2 ? true : false,
          };
        }
      });
    }
  });

  // save workbook to disk

  const imageSource = getCollegeLogo();
  toDataURL(imageSource, function (dataUrl) {
    const imageId2 = workbook.addImage({
      base64: dataUrl,
      extension: 'jpeg',
    });

    worksheet.addImage(imageId2, {
      tl: { col: 0, row: 0 },
      br: { col: 1, row: 3 },
      editAs: 'absolute',
    });
    workbook.xlsx
      .writeBuffer()
      .then((buffer) => {
        saveAs(
          new Blob([buffer]),
          `${programName.replace(' ', '')}_${courseCode.replace(
            ' ',
            ''
          )}_${courseName.replace(' ', '')}.xlsx`
        );
      })
      .catch((err) => console.log('Error writing excel export', err)); //eslint-disable-line
  });
};

export const getInfraName = (item) => {
  return item.get('classModeType', '') === 'offline'
    ? 'Onsite (Offline)'
    : item.get('mode', '');
};

export function getStudents({
  type,
  schedule,
  fetchStatus = 'primaryStatus',
  repeatForLateAttendees = false,
}) {
  const studentData = schedule
    .get('students', List())
    .filter(
      (studentElement) => studentElement.get(fetchStatus, '') !== 'exclude'
    )
    .filter((studentElement) =>
      repeatForLateAttendees
        ? studentElement.get('status', '') === 'absent'
        : studentElement
    )
    .filter((item) =>
      type === 'all'
        ? item
        : type === 'leave'
        ? ['leave', 'on_duty', 'permission'].includes(item.get(fetchStatus, ''))
        : item.get(fetchStatus, '') === type
    ).size;
  return formatSingleDigit(studentData);
}

export function getHeaderData({ attendanceType }) {
  const headerData = {
    primary: {
      name: 'Primary',
      description: '',
      symbol: '1*',
    },
    buzzer: {
      name: 'Buzzer',
      description:
        'Students can able to mark attendance within the respective time by buzzering',
      symbol: 'B*',
    },
    retake_all: {
      name: 'Repeat - for all',
      description:
        'Can able to Re-verify the students presence by authenticating face',
      symbol: 'Re/All',
    },
    retake_absent: {
      name: 'Repeat - for late absentees',
      description:
        'Can able to Re-verify the late absentees presence by authenticating face',
      symbol: 'Re/La',
    },
    surprise_quiz: {
      name: 'Engager',
      description: '',
      symbol: 'E*',
    },
    manual: {
      name: 'Manual',
      description: '',
      symbol: '2*',
    },
  };
  return headerData?.[attendanceType] || headerData?.['primary'];
}

const nameColumnId = 0;
export const statusColumnId = 2;
const disciplinaryRemarksId = 3;

export const firstColumn = fromJS({
  _id: nameColumnId,
  field: 'name',
  headerName: 'FINAL ATTENDANCE REPORT WILL GENERATED BASED ON SELECTION',
  width: 100,
});

export const lastColumn = fromJS({
  _id: statusColumnId,
  field: 'status',
  headerName: 'Status',
  width: 100,
  checked: false,
  component: 'switch',
});

export const disciplinaryRemarks = fromJS({
  _id: disciplinaryRemarksId,
  field: 'DisciplinaryRemarks',
  headerName: 'Disciplinary Remarks',
  width: 100,
});

export let staffHeader = fromJS([
  {
    _id: nameColumnId,
    field: 'name',
    headerName: 'Staff Info',
    width: 100,
  },
  {
    _id: 1,
    field: 'primary',
    headerName: 'primary',
    width: 100,
    checked: false,
  },
  {
    _id: statusColumnId,
    field: 'status',
    headerName: 'Status',
    width: 100,
    checked: false,
    component: 'switch',
  },
]);

export const getPresentStatus = ({ student, column, activeTab }) => {
  const isStaffTab = activeTab === 'staffs' ? 'status' : 'primaryStatus';
  if (column.get('field', '') === 'primary')
    return student.get(isStaffTab, '') === 'present';
  const isChecked =
    student.getIn(['columnData', column.get('_id', ''), 'status'], '') ===
    'present';
  return isChecked;
};

export const nameConcat = (data) => {
  const First = data.get('first', '');
  const last = data.get('last', '');
  return First + ' ' + last;
};

export const getCommonParams = () => ({
  institutionCalendarId: getURLParams('_cid', true),
  merge_status: getURLParams('_merge_status', true),
  admin_course: getURLParams('_admin_courses', true),
  programId: getURLParams('_program_id'),
  term: getURLParams('term', true),
  currentCourseId: getURLParams('_course_id'),
  rotation: getURLParams('rotation', true),
  year: getURLParams('_year_no', true),
  level: getURLParams('_level_no', true),
  rotation_count: getURLParams('rotation_count', true),
});

export const goToSession = (data, history) => {
  const adminCourses = getURLParams('_admin_courses', true);
  const params = new URLSearchParams({
    _id: eString(data.getIn(['session', '_session_id'], '')),
    _schedule_id: eString(data.get('_id', '')),
    _course_id: eString(data.get('_course_id')),
    _program_id: eString(data.get('_program_id')),
    _level_no: eString(data.get('level_no')),
    _merge_status: eString(data.get('merge_status')),
    _year_no: eString(data.get('year_no')),
    term: eString(data.get('term')),
    _cid: eString(data.get('_institution_calendar_id')),
    _type: eString(data.get('type')),
    rotation: eString(data.get('rotation', 'no')),
    rotation_count: eString(data.get('rotation_count', 0) || 0),
    _admin_courses: eString(adminCourses),
    mode: eString(data.get('mode', '')),
    course_code: eString(data.get('course_code', '')),
    course_name: eString(data.get('course_name', '')),
  });

  history.push(`/sessions/document?${params.toString()}`);
};

export const getSessionRowClass = (schedule, userType, authData) => {
  const loggedInUserType = userType === 'staff' ? 'staffs' : 'students';
  const loggedInUserId = userType === 'staff' ? '_staff_id' : '_id';

  const present = schedule
    .get(loggedInUserType, List())
    .filter((item) => item.get(loggedInUserId) === authData.get('_id', ''))
    .some((data) => data.get('status') === 'present');

  if (schedule.get('isActive', '') !== true) {
    return 'text-light-grey';
  }

  if (
    schedule.get('status', '') === 'pending' &&
    schedule.get('mode', '') !== 'onsite' &&
    checkEnableSession(schedule)
  ) {
    return 'text-light-blue';
  }

  if (schedule.get('status', '') === 'completed' && present) {
    return 'text-light-green bg-hovercolor hover_remove';
  }

  return 'tr-change bg-hovercolor';
};

export const getSubjects = (subjects) => {
  return subjects.map((subject) => subject.get('subject_name', '')).join(', ');
};
