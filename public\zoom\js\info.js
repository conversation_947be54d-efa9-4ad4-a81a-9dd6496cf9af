//index.js
window.addEventListener('DOMContentLoaded', function (event) {
  const isLoggedIn = window.localStorage.getItem('loggedIn');
  if (isLoggedIn === null) {
    webSDKReady();
  } else {
    alert('Already you have an opened zoom tab.');
    window.close();
  }
});

function webSDKReady() {
  var testTool = window.testTool;
  if (testTool.isMobileDevice()) {
    vConsole = new VConsole();
  }
  // console.log('checkSystemRequirements');
  // console.log(JSON.stringify(ZoomMtg.checkSystemRequirements()));
  ZoomMtg.preLoadWasm();
  const actualData = localStorage.getItem('_lib');
  if (actualData !== null) {
    var bytes = CryptoJS.AES.decrypt(actualData.toString(), '@#MSK0033#@');
    var digival = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    var tmpArgs = testTool.parseQuery();
    var API_KEY = digival[3].d;
    var API_SECRET = digival[4].e;
    ZoomMtg.generateSignature({
      meetingNumber: digival[5].f,
      apiKey: API_KEY,
      apiSecret: API_SECRET,
      role: 0,
      success: function (res) {
        var data = [{ s: res.result }];
        var siddiq = CryptoJS.AES.encrypt(JSON.stringify(data), '@#MSK0033#@');
        localStorage.setItem('_lib_1', siddiq);
        //var joinUrl = '/meeting.html?' + testTool.serialize(meetingConfig);
        var joinUrl = '/zoom/meeting.html?' + testTool.serialize(tmpArgs);
        window.open(joinUrl, '_self');
      },
    });
  }
}

//meeting.js
window.addEventListener('DOMContentLoaded', function (event) {
  //console.log('DOM fully loaded and parsed');
  const actualData = localStorage.getItem('_lib');
  if (actualData !== null) {
    window.localStorage.setItem('loggedIn', 'true');
    websdkready();
  }
});

function websdkready() {
  const actualData = localStorage.getItem('_lib');
  const sData = localStorage.getItem('_lib_1');
  var bytes = CryptoJS.AES.decrypt(actualData.toString(), '@#MSK0033#@');
  var digival = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  var sBytes = CryptoJS.AES.decrypt(sData.toString(), '@#MSK0033#@');
  var sDigival = JSON.parse(sBytes.toString(CryptoJS.enc.Utf8));
  var testTool = window.testTool;
  var tmpArgs = testTool.parseQuery();
  var meetingConfig = {
    apiKey: digival[3].d,
    meetingNumber: digival[5].f,
    userName: digival[1].b,
    passWord: digival[6].g,
    leaveUrl: '/zoom/leave.html?' + testTool.serialize(tmpArgs),
    role: parseInt(0, 10),
    userEmail: digival[2].c,
    lang: 'en',
    signature: sDigival[0].s || '',
    china: false,
  };
  //console.log('meetingConfig', meetingConfig, digival);
  // a tool use debug mobile device
  if (testTool.isMobileDevice()) {
    vConsole = new VConsole();
  }
  //console.log(JSON.stringify(ZoomMtg.checkSystemRequirements()));
  // it's option if you want to change the WebSDK dependency link resources. setZoomJSLib must be run at first
  // ZoomMtg.setZoomJSLib("https://source.zoom.us/1.9.9/lib", "/av"); // CDN version defaul
  if (meetingConfig.china)
    ZoomMtg.setZoomJSLib('https://jssdk.zoomus.cn/1.9.9/lib', '/av'); // china cdn option
  ZoomMtg.preLoadWasm();
  ZoomMtg.prepareJssdk();
  function beginJoin(signature) {
    ZoomMtg.init({
      leaveUrl: meetingConfig.leaveUrl,
      webEndpoint: meetingConfig.webEndpoint,
      disableCORP: !window.crossOriginIsolated, // default true
      // disablePreview: false, // default false
      isSupportAV: true,
      disableInvite: true,
      meetingInfo: ['topic', 'host'],
      success: function () {
        //console.log(meetingConfig);
        //console.log('signature', signature);
        ZoomMtg.i18n.load(meetingConfig.lang);
        ZoomMtg.i18n.reload(meetingConfig.lang);
        ZoomMtg.join({
          meetingNumber: meetingConfig.meetingNumber,
          userName: meetingConfig.userName,
          signature: signature,
          apiKey: meetingConfig.apiKey,
          userEmail: meetingConfig.userEmail,
          passWord: meetingConfig.passWord,
          success: function (res) {
            //console.log('join meeting success');
            //console.log('get attendeelist');
            ZoomMtg.getAttendeeslist({});
            ZoomMtg.getCurrentUser({
              success: function (res) {
                console.log('success getCurrentUser', res.result.currentUser);
              },
            });
          },
          error: function (res) {
            console.log(res);
          },
        });
      },
      error: function (res) {
        console.log(res);
      },
    });
    ZoomMtg.inMeetingServiceListener('onUserJoin', function (data) {
      console.log('inMeetingServiceListener onUserJoin', data);
    });
    ZoomMtg.inMeetingServiceListener('onUserLeave', function (data) {
      console.log('inMeetingServiceListener onUserLeave', data);
    });
    ZoomMtg.inMeetingServiceListener('onUserIsInWaitingRoom', function (data) {
      console.log('inMeetingServiceListener onUserIsInWaitingRoom', data);
    });
    ZoomMtg.inMeetingServiceListener('onMeetingStatus', function (data) {
      console.log('inMeetingServiceListener onMeetingStatus', data);
    });
  }
  beginJoin(meetingConfig.signature);
}

window.addEventListener('pagehide', function (e) {
  e.preventDefault();
  window.localStorage.removeItem('loggedIn');
  ZoomMtg.leaveMeeting({});
  e.returnValue = '';
});

//leave.js
window.addEventListener('DOMContentLoaded', function (event) {
  localStorage.removeItem('_lib_1');
  localStorage.removeItem('_lib');
  localStorage.removeItem('loggedIn');
});

//common.js
window.addEventListener('DOMContentLoaded', function (event) {
  var testTool = window.testTool;
  var tmpArgs = testTool.parseQuery();
  const userId = atob(tmpArgs?.uid);
  const user = localStorage.getItem('user_Id');
  if (userId !== user) {
    window.location.href = '/';
  }
  checkHasSession();
});
window.addEventListener('contextmenu', (e) => {
  e.preventDefault();
});
function checkHasSession() {
  setInterval(function () {
    const session = localStorage.getItem('_lib');
    if (session === null) {
      window.location.href = '/zoom/leave.html';
    }
  }, 30000);
}
