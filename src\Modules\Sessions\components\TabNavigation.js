import React from 'react';
import PropTypes from 'prop-types';
import Tooltips from 'Widgets/Tooltip/Tooltip';
import { useTranslation } from 'react-i18next';
import { isModuleEnabled, isChatly, isDefaultChat } from '../../../utils';
import { List } from 'immutable';

// MUI Icons imports
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import FolderIcon from '@mui/icons-material/Folder';
import SchoolIcon from '@mui/icons-material/School';
import ChatIcon from '@mui/icons-material/Chat';
import ForumIcon from '@mui/icons-material/Forum';
import PeopleIcon from '@mui/icons-material/People';
import WarningIcon from '@mui/icons-material/Warning';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import AssessmentIcon from '@mui/icons-material/Assessment';
import { selectCourseDeliveryGroupAdmin } from 'ReduxApi/reports/selector';
import { isGroupBasedDeliveryReport } from '../../../utils';
import { useSelector } from 'react-redux';
import SettingsIcon from '@mui/icons-material/Settings';

const TabNavigation = ({
  activeTab,
  onTabChange,
  unReadCount,
  userType,
  isCurrentAcademicYear,
  courseSessionData,
  adminCourses,
}) => {
  const { t } = useTranslation();
  const isDefaultChatEnabled = isDefaultChat();
  const isDigiChatEnabled = isChatly();
  const isGroupBasedDeliveryReportEnabled = isGroupBasedDeliveryReport();
  const courseDeliveryGroupAdmin = useSelector(selectCourseDeliveryGroupAdmin);
  const enableReportsAnalytics = !adminCourses
    ? courseDeliveryGroupAdmin.size > 0 && isGroupBasedDeliveryReportEnabled
    : isGroupBasedDeliveryReportEnabled;
  const tabs = [
    {
      key: 'sessionView',
      label: t('all_session'),
      icon: <VideoLibraryIcon />,
    },
    {
      key: 'documentsView',
      label: t('all_document'),
      icon: <FolderIcon />,
    },
    ...(isModuleEnabled('ACTIVITY_ENABLED')
      ? [
          {
            key: 'activitiesView',
            label: t('all_activity'),
            icon: <SchoolIcon />,
          },
        ]
      : []),
    ...((isDefaultChatEnabled || isDigiChatEnabled) && isCurrentAcademicYear()
      ? [
          {
            key: 'chatView',
            label: isDigiChatEnabled ? 'Chatly' : t('chats'),
            icon: <ChatIcon />,
            badge: unReadCount > 0 ? unReadCount : null,
          },
        ]
      : []),
    ...(isModuleEnabled('DISCUSSION_FORUM')
      ? [
          {
            key: 'discussionView',
            label: t('all_discussion'),
            icon: <ForumIcon />,
          },
        ]
      : []),
    ...(userType === 'staff'
      ? [
          {
            key: 'allStudentsView',
            label: 'ALL STUDENTS',
            icon: <PeopleIcon />,
          },
          {
            key: 'allAssignmentView',
            label: 'ALL ASSIGNMENTS',
            icon: <AssignmentTurnedInIcon />,
          },
          ...(enableReportsAnalytics
            ? [
                {
                  key: 'reportsView',
                  label: 'REPORTS & ANALYTICS',
                  icon: <AssessmentIcon />,
                },
              ]
            : []),
          ...(adminCourses
            ? [
                {
                  key: 'warningNew',
                  label: 'WARNINGS & DENIAL CONFIGURATION',
                  icon: <WarningIcon />,
                  hidden: false,
                },
              ]
            : []),
          ...(adminCourses
            ? [
                {
                  key: 'courseWiseSettingView',
                  label: 'SETTINGS',
                  icon: <SettingsIcon className="settings-icon" />,
                },
              ]
            : []),
        ]
      : []),
  ];

  return (
    <div className="fixed_nav">
      <div className="customize_tab customize_tab_chat">
        <div className="d-flex justify-content-between">
          <div className="d-flex align-items-center">
            {tabs.map((tab) => (
              <Tooltips key={tab.key} title={tab.label}>
                <li
                  onClick={() => onTabChange(tab.key)}
                  className={`tabaligment digi-white active icon ${
                    activeTab === tab.key ? 'tabactive' : ''
                  }`}
                  style={{
                    cursor: 'pointer',
                    padding: '8px 12px',
                    margin: '0 4px',
                    borderRadius: '8px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    minWidth: '60px',
                    minHeight: '60px',
                  }}
                >
                  <div className="d-flex flex-column align-items-center justify-content-center position-relative">
                    <div style={{ marginBottom: '4px' }}>{tab.icon}</div>
                    <span
                      style={{
                        fontSize: '10px',
                        fontWeight: '500',
                        textAlign: 'center',
                        lineHeight: '1.2',
                        maxWidth: '140px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {tab.label}
                    </span>
                    {tab.badge && (
                      <div
                        className="digi-green-box"
                        style={{
                          position: 'absolute',
                          top: '-4px',
                          right: '-4px',
                          border: '1px solid #FFFFFF',
                          borderRadius: '50%',
                          minWidth: '18px',
                          height: '18px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '10px',
                          fontWeight: 'bold',
                        }}
                      >
                        <span className="text-center text-white">
                          {tab.badge}
                        </span>
                      </div>
                    )}
                  </div>
                </li>
              </Tooltips>
            ))}
          </div>
          {courseSessionData.getIn([0, 'restrictCourseAccess'], false) &&
            activeTab === 'sessionView' && (
              <p
                className="my-auto text-light"
                style={{ marginRight: '100px' }}
              >
                Course Restricted{' '}
                <Tooltips
                  title={`You can't access this course right now, you reached ${courseSessionData.getIn(
                    [0, 'warningData'],
                    ''
                  )}`}
                >
                  <i
                    className="fa fa-info-circle pl-2 text-danger"
                    aria-hidden="true"
                  ></i>
                </Tooltips>
              </p>
            )}
        </div>
      </div>
    </div>
  );
};

TabNavigation.propTypes = {
  activeTab: PropTypes.string.isRequired,
  onTabChange: PropTypes.func.isRequired,
  unReadCount: PropTypes.number,
  userType: PropTypes.string.isRequired,
  isCurrentAcademicYear: PropTypes.func.isRequired,
  courseSessionData: PropTypes.instanceOf(List),
  adminCourses: PropTypes.bool,
};

export default TabNavigation;
