/* sample iframe widget load*/
//http://localhost/oracle_facial_offline_poc/liveness_mobile/mobile.html?appname=DC&type=student&employeeOrAcademicId=10000002&platform=android&debug=false&faceurl=https://ecs-auth-staging.digivalitsolutions.com/api/v0/auth/facial-labeled-descriptors?employeeOrAcademicId=

const webcamElement = document.getElementById('webcam');
const canvasElement = document.getElementById('canvas');
const webcam = new Webcam(webcamElement, 'user', canvasElement);

const urlSearchParams = new URLSearchParams(window.location.search);
const params = Object.fromEntries(urlSearchParams.entries());
var totaldescriptions = {};
if (Object.keys(params).length < 4) {
  alert('params not supplied');
}
platform = params.platform;
let bodyClassName = 'digi-ios-font';
platform == 'android' ? (bodyClassName = 'digi-android-font') : null;

const daAuthAppUrl = params.faceurl;

$('.cam1').hide();

$('body').addClass(bodyClassName);

if (params.debug == 'true') {
  $('.debug').show();
}

$('.iclose2,.cancel-track').on('click', function () {
  takeToinitialState();
  sendMesssageToPlatform('close1clicked');
});

$('.iclose').on('click', function () {
  $('.digi-tooltip-container').removeClass('digi-active');
});

$('.info-tooltip').on('click', function () {
  $('.digi-tooltip-container').toggleClass('digi-active');
});

$('.digi-fail-button').on('click', function () {
  takeToinitialState();
  if (platform == 'android' || platform == 'ios') {
    sendMesssageToPlatform('tryagain');
  } else {
    //window.location.href = window.location.href;
    $('.cam1').show();
    $('.cam2').hide();
    webcam.start();
  }
});

$('.cam2').on('click', function () {
  $(this).hide();
  $('.cam1').show();
  webcam.start();
});

$('.digi-info-icon').on('click', function (event) {
  event.stopPropagation();
  $('.digi-tooltip-container,.digi-close-icon-container').toggleClass(
    'digi-active'
  );
});

$('.digi-home-icon').on('click', async function () {
  var picToVerify = webcam.snap();
  $('#snapshotplaceholder').attr('src', picToVerify);
  $('.digi-profile-picture').attr('src', picToVerify);
  //   console.log('result', result);
  $('#taptocapture').html('Loading...');
  let verifyresult = await trainAndVerify(true);
  $('#taptocapture').html('Tap to capture');
  if (
    verifyresult === undefined &&
    totaldescriptions?.descriptors === undefined
  ) {
    sendMesssageToPlatform('facenottrained');
    $('.steps').removeClass('digi-active');
    $('.step5').addClass('digi-active');
  } else if (verifyresult) {
    handleAuthenticationSuccess();
  } else {
    handleAuthenticationFailiure();
  }

  webcam.stop();
  // alert(mine);

  console.log('webcam started');
});

function sendMesssageToPlatform(message) {
  if (params.debug == 'true') {
    alert(platform + message);
    console.log(platform, 'message sent', message);
  }
  //platform = params.platform;
  if (platform == 'ios')
    window.webkit.messageHandlers.Mobile.postMessage(message);
  if (platform == 'android') {
    Mobile.showMessageFromWeb(message);
  }
  if (platform == 'web') window.parent.postMessage(message, '*');
}

function handleAuthenticationSuccess() {
  $('.digi-camera-page').removeClass('digi-active');
  $('.digi-verification-success-page').addClass('digi-active');
  console.log('handle succcses sent');
  setTimeout(function () {
    sendMesssageToPlatform('facesuccess');
  }, 2000);
  setTimeout(function () {
    takeToinitialState();
    $('.digi-verification-success-page').addClass('init-hide');
  }, 5000);
}

function handleAuthenticationFailiure() {
  sendMesssageToPlatform('facefailed');
  $('.steps').removeClass('digi-active');
  $('.step5').addClass('digi-active');
  $('.digi-verification-success-page').addClass('init-hide');
}

function dString(name) {
  if (name !== '') {
    return window.atob(name);
  }
  return '';
}

function dataURLtoFile(dataurl, filename) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = dString(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

async function trainAndVerify(isRealimage) {
  const imgUrl2 = $('#snapshotplaceholder').attr('src');
  const queryImage1 = await faceapi.fetchImage(imgUrl2);
  //console.log('query image', queryImage1);
  const singleResult = await faceapi
    .detectSingleFace(queryImage1)
    .withFaceLandmarks()
    .withFaceDescriptor();
  $('.loader').removeClass('loader');
  $('.init-hide').removeClass('init-hide');
  if (isRealimage && totaldescriptions?.descriptors !== undefined) {
    console.log('checking real image');
    var labeledFaceDescriptors = totaldescriptions?.descriptors.map((x) =>
      faceapi.LabeledFaceDescriptors.fromJSON(totaldescriptions)
    );

    if (singleResult) {
      const faceMatcher2 = new faceapi.FaceMatcher(labeledFaceDescriptors);
      const bestMatch = faceMatcher2.findBestMatch(singleResult.descriptor);
      console.log(
        'best match ',
        bestMatch.toString(),
        '------------',
        bestMatch.distance < 0.4
      );
      return bestMatch.distance < 0.4;
    }
  } else {
    console.log('dummy image trained ');
  }
}

$(document).ready(async function () {
  console.log('webcam is ready');

  //$('.loader').removeClass('loader');

  /*oracle offline facial code starts */
  await loadLibraries();
  loadSingleDescfromDb(params.employeeOrAcademicId);
  trainAndVerify();
  // $('.loader').removeClass('loader');
  // $('.init-hide').removeClass('init-hide');
  /*todo add asymetric authentication before prod to fetch*/
});

function loadSingleDescfromDb(acadamicId) {
  console.log('load from db called');
  $.ajax({
    url: daAuthAppUrl + acadamicId,
    type: 'GET',
    dataType: 'json', // added data type
    success: function (res) {
      totaldescriptions = res.data;
    },
    error: function (err) {
      // alert(JSON.stringify(err));
      console.log('error occured load from db', err);
    },
  });
}

async function loadLibraries() {
  const MODEL_URL = 'assets/weights';
  await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
  await faceapi.loadFaceLandmarkModel(MODEL_URL);
  await faceapi.loadFaceRecognitionModel(MODEL_URL);
  console.log('load facial Libraries done');
}
function onBackPressed() {
  console.log('on back pressed');
  //close camera functionality
  takeToinitialState();
}

function takeToinitialState() {
  webcam.stop();
  $('#snapshotplaceholder').attr('src', '');
  $('.digi-profile-picture').attr('src', '');
  // $('.steps,.digi-verification-success-page').removeClass('digi-active');
  // $('.step1').addClass('digi-active');
  $('.steps').removeClass('digi-active');
  $('.digi-camera-page').addClass('digi-active');
  $('.step1').addClass('digi-active');
  $('.cam1').hide();
  $('.cam2').show();
}
