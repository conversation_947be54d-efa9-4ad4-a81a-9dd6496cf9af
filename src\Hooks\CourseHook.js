import LocalStorageService from 'LocalStorageService';
import { capitalize } from 'Modules/Utils/utils';
import { fetchCourseVersionedDetails } from 'ReduxApi/Home/action';
import {
  selectCourseVersionedDetails,
  selectAcademicYears,
  selectAcademicAdminYears,
} from 'ReduxApi/Home/selectors';
import { selectActiveRole, selectAuthData } from 'ReduxApi/User/selectors';
import { List, Map } from 'immutable';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

export const useCourseHook = () => {
  const currentCourseVerionedDetails = useSelector(
    selectCourseVersionedDetails
  );
  const academicYears = useSelector(selectAcademicYears);
  const academicAdminYears = useSelector(selectAcademicAdminYears);
  const userData = useSelector(selectAuthData);
  const userId = userData.get('_id', '');
  const userType = userData.get('user_type', '');
  const userActiveRole = useSelector(selectActiveRole);
  const roleId = userActiveRole.get('_id', '');
  const dispatch = useDispatch();
  useEffect(() => {
    if (!currentCourseVerionedDetails.get(roleId, Map()).size) {
      const currentCalendarIds = academicYears.size
        ? academicYears.map(
            (academicYearElement) =>
              `inistitutionCalendarIds[]=${encodeURIComponent(
                academicYearElement.get('_id', '')
              )}`
          )
        : academicAdminYears.size
        ? academicAdminYears.map(
            (academicAdminYearElement) =>
              `inistitutionCalendarIds[]=${encodeURIComponent(
                academicAdminYearElement.get('_id', '')
              )}`
          )
        : List();
      const checkApiCredentials = currentCalendarIds.size && userId && userType;
      if (checkApiCredentials && (userType === 'student' || roleId)) {
        dispatch(
          fetchCourseVersionedDetails(
            currentCalendarIds,
            userId,
            userType,
            roleId
          )
        );
      }
    }
  }, [academicYears, academicAdminYears, userActiveRole]); //eslint-disable-line
};

export const useCourseVersionHook = () => {
  const courseVersionedDetails = useSelector(selectCourseVersionedDetails);
  const courseNameWithVersion = (courseId, existingCourseName = '') => {
    const roleId = LocalStorageService.getCustomToken('role_id');
    let currentCourseDetail = courseVersionedDetails.getIn(
      [roleId, courseId],
      Map()
    );
    if (!currentCourseDetail.size) {
      return existingCourseName;
    }
    let courseName = currentCourseDetail.get('courseName', '');
    let versionedIds = currentCourseDetail.get('versionedCourseIds', List());
    let versionName = capitalize(currentCourseDetail.get('versionName', ''));
    let isVersioned = currentCourseDetail.get('versioned', false);
    return `${courseName}${
      (isVersioned || versionedIds.size) && versionName
        ? ' - ' + versionName
        : ''
    }`;
  };
  return [courseNameWithVersion];
};
