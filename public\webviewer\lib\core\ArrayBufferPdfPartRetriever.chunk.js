/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[4],{367:function(ha,ea,f){f.r(ea);ha=f(44);f=f(318);var ba=function(){function f(f){this.buffer=f;this.fileSize=null===f||void 0===f?void 0:f.byteLength}f.prototype.getFileData=function(f){f(new Uint8Array(this.buffer))};f.prototype.getFile=function(){return Promise.resolve(null)};return f}();Object(ha.a)(ba);Object(f.a)(ba);Object(f.b)(ba);ea["default"]=ba}}]);}).call(this || window)
