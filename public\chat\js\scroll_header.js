let lastScrollTop = 0;

window.addEventListener('scroll', function () {
  let currentScroll = window.pageYOffset || document.documentElement.scrollTop;

  const previewForm = document.getElementById('preview-form');
  const previewFormShow = previewForm.style.display === 'block';

  if (currentScroll > lastScrollTop) {
    // Scroll down
    if (['Q360'].includes(currentActiveMenu.name)) {
      if (previewFormShow && lastScrollTop > 500) {
        document.getElementById('selected-form').style.display = 'block';
      } else {
        document.getElementById('selected-form').style.display = 'none';
      }
    } else {
      document.querySelector('.fix-header').classList.add('hidden');
    }
  } else {
    // Scroll up
    if (!['Q360'].includes(currentActiveMenu.name)) {
      document.querySelector('.fix-header').classList.remove('hidden');
    } else {
      if (previewFormShow && lastScrollTop > 500) {
        document.getElementById('selected-form').style.display = 'block';
      } else {
        document.getElementById('selected-form').style.display = 'none';
      }
    }
  }

  lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
});
