import React, { useState, Suspense, lazy } from 'react';
import { Map, List } from 'immutable';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';

import ArrowRightIcon from '@mui/icons-material/ArrowRight';

import StarRatings from '../../Shared/StarRatings';
import {
  formattedFullName,
  Capitalize,
  getUTCSessionTiming,
  getZoomDuration,
  getTimeSlotDuration,
  getZoomPercentage,
  dString,
  getURLParams,
  CapitalizeAll,
  getFormattedGroupName,
  studentGroupRename,
  getStudentGroupsNew,
  eString,
  removeURLParams,
} from '../../../ReduxApi/util';
import { useTranslation } from 'react-i18next';
import SessionButton from './SessionButton';
import { splitNameArray } from '../../../Modules/Utils/utils';
import Tooltips from '../../../Widgets/Tooltip/Tooltip';
import { getClassName, getTranslatedDuration } from '../../Utils/utils.js';
import i18n from 'i18next';
import {
  indVerRename,
  getEnvLabelChanged,
  isManualAttendanceEnabled,
  isMissedSessionModuleEnabled,
  isDcMissedSessionEnabled,
  studentGroupViewList,
} from '../../../utils';
import { Button } from 'react-bootstrap';
import { Box, FormControl, MenuItem, Select } from '@mui/material';
import SuccessImg from '../../../Assets/icons/add_circle.svg';
import FailedImg from '../../../Assets/icons/groupicon.svg';
import ReTakeIcon from 'Assets/icons/Attendance_verification_icon.svg';
import BuzzerIcon from 'Assets/icons/buzzerVerification.svg';

import { getInfraName } from './utils';
import { ColorThemeSelect } from '../../../designUtils';

const MissedToCompleteModal = lazy(() =>
  import('../modal/MissedToCompleteModal')
);
const RevokeModal = lazy(() => import('../modal/RevokeModal'));
const RetakeAlertModal = lazy(() => import('../modal/RetakeAlertModal'));

const attendanceOptions = [
  // { value: '', label: 'Verify with' },
  { value: 'buzzer', label: 'Buzzer' },
  { value: 'retake_all', label: 'Repeat for All' },
  { value: 'retake_absent', label: 'Repeat for Late Attendees' },
];
function SessionCard(props) {
  const {
    toggleMoreInfo,
    schedule,
    authData,
    startSession,
    endSession,
    viewReports,
    fetchApi,
    resetSession,
    sessionRatingOpen,
    openPopUp,
    updateMissedSession,
    warning,
    courseRestriction,
  } = props;
  const { t } = useTranslation();
  const history = useHistory();
  const [showArray, setShowArray] = useState([]);
  const [missedToCompleteOpen, setMissedToCompleteOpen] = useState(false);
  const [revokeOpen, setRevokeOpen] = useState(false);
  const programId = dString(getURLParams('_program_id'));
  const [attendanceOption, setAttendanceOption] = useState('');
  const [openAlertModal, setOpenAlertModal] = useState(false);

  const handleRetakes = (e) => {
    const value = e.target.value;
    setAttendanceOption(value);
    if (value !== '') setOpenAlertModal(true);
  };

  const handleRetakeRedirect = (e) => {
    const search = window.location.search;
    history.replace({
      pathname: `/sessions/onsite/${eString(attendanceOption)}/${eString(
        'new'
      )}`,
      search: search,
      state: { load: false },
    });
  };

  function getStudents(type) {
    return schedule
      .get('students', List())
      .filter(
        (studentElement) => studentElement.get('status', '') !== 'exclude'
      )
      .filter((item) =>
        type === 'all'
          ? item
          : type === 'leave'
          ? ['leave', 'on_duty', 'permission'].includes(item.get('status'))
          : item.get('status') === type
      ).size;
  }

  const loggedStudentData =
    authData.get('user_type', 'student') === 'student'
      ? schedule
          .get('students', List())
          .find((item) => item.get('_id') === authData.get('_id', ''))
      : Map();

  function makeOpen(id) {
    const findAlready = showArray.find((item) => item === id);
    if (findAlready !== undefined) {
      const filteredValue = showArray.filter((item) => item !== id);
      setShowArray(filteredValue);
    } else {
      const arrayValue = [...showArray, id];
      setShowArray(arrayValue);
    }
  }

  function enableSwitch() {
    if (
      schedule.get('status', '') === 'completed' &&
      authData.get('user_type') === 'student'
    ) {
      const scheduleId = schedule.get('_id', '');
      return (
        <Tooltips title={'My Session Durations'}>
          <i
            className={`fa fa-angle-${
              showArray.includes(scheduleId) ? 'down' : 'up'
            } pl-4 pr-4 icon`}
            style={{ fontSize: '20px', fontWeight: 'bold' }}
            aria-hidden="true"
            onClick={() => makeOpen(schedule.get('_id', ''))}
          ></i>
        </Tooltips>
      );
    }
  }

  function getStudentDurations() {
    const scheduleId = schedule.get('_id', '');
    if (
      schedule.get('status', '') === 'completed' &&
      authData.get('user_type') === 'student' &&
      showArray.includes(scheduleId)
    ) {
      return (
        <>
          {loggedStudentData.get('time', '') !== '' && (
            <>
              <p className="mb-0 mt-1 text-gray">
                My Session Duration -{' '}
                {getTimeSlotDuration(
                  loggedStudentData.get('time', 0),
                  schedule.getIn(['sessionDetail', 'stop_time'], '')
                )}
              </p>
              {schedule.getIn(['zoomDetail', 'zoomTotalDuration'], '') !==
                '' && (
                <p className="mb-0 mt-1 text-gray">
                  Total Zoom Duration -{' '}
                  {getZoomDuration(
                    schedule.getIn(['zoomDetail', 'zoomTotalDuration'], 0)
                  )}
                </p>
              )}
              {schedule.getIn(['teamsDetail', 'teamsTotalDuration'], '') !==
                '' && (
                <p className="mb-0 mt-1 text-gray">
                  Total Teams Duration -{' '}
                  {getZoomDuration(
                    schedule.getIn(['teamsDetail', 'teamsTotalDuration'], 0)
                  )}
                </p>
              )}
              {loggedStudentData.get('duration', '') !== '' && (
                <p className="mb-0 mt-1 text-gray">
                  Participated{' '}
                  {Capitalize(schedule.get('remotePlatform', 'zoom'))} Duration
                  - {getZoomDuration(loggedStudentData.get('duration', 0))}
                </p>
              )}
              {loggedStudentData.get('percentage', '') !== '' && (
                <p className="mb-0 mt-1 text-gray">
                  {i18n.t('session_atten')} -{' '}
                  {getZoomPercentage(loggedStudentData.get('percentage', 0))}
                </p>
              )}
            </>
          )}
        </>
      );
    }
  }

  const handleSave = ({ status, isRevoke = false }) => {
    const requestData = {
      schedule_id: schedule.get('merge_status', false)
        ? schedule
            .get('merge_with', List())
            .map((item) => item.get('schedule_id', ''))
            .concat(schedule.get('_id', ''))
        : [schedule.get('_id', '')],
      staff_id: authData.get('_id', ''),
      ...(isRevoke === false && { attendanceStatus: status }),
      isRevoke: isRevoke,
    };
    const callBack = () => {
      fetchApi();
      setMissedToCompleteOpen(false);
      setRevokeOpen(false);
    };
    updateMissedSession(requestData, callBack);
  };

  function getRetakeName(modeBy) {
    return (
      attendanceOptions.find((optionElement) => optionElement.value === modeBy)
        ?.label || ''
    );
  }

  function restrictCourseDetails() {
    return (
      <>
        {!['on_duty', 'permission', 'leave', 'present'].includes(
          loggedStudentData.get('status', '')
        ) &&
          ['ongoing', 'pending'].includes(schedule.get('status', '')) && (
            <div className="text-red f-15 text-center bg-light ">
              {"You Can't Join this session"}
              <div className="text-secondary f-10">
                {' '}
                The Course has been Restricted{' '}
                <Tooltips
                  title={`You can't access this course right now, you reached ${warning}`}
                >
                  <i
                    className="fa fa-info-circle pl-2 text-danger"
                    aria-hidden="true"
                  ></i>
                </Tooltips>
              </div>
            </div>
          )}
        {authData.get('user_type', '') === 'student' &&
        !['ongoing', 'pending'].includes(schedule.get('status', '')) ? (
          schedule.get('isActive', '') !== true ? (
            <div className="f-18 text-center text-danger">
              {i18n.t('session_canceled')}
            </div>
          ) : schedule.get('status', '') === 'missed' ? (
            <div className="f-18 text-center text-warning">
              {t('session_missed')}
              {schedule.get('classModeType', '') === 'offline' &&
                ' - Offline Session'}
            </div>
          ) : (
            <>
              {loggedStudentData.get('status', '') === 'present' ? (
                <div className="f-18 text-center text-green">
                  <img
                    style={{ marginTop: '-3px' }}
                    src={SuccessImg}
                    alt="Digi-class"
                  />{' '}
                  {t('attendance')} {t('marked')} {t('successfully')}
                </div>
              ) : (
                <>
                  {['on_duty', 'permission', 'leave', 'absent'].includes(
                    loggedStudentData.get('status', '')
                  ) ? (
                    <div
                      className={`${
                        ['leave', 'absent'].includes(
                          loggedStudentData.get('status', '')
                        )
                          ? 'text-red'
                          : 'text-green'
                      } f-18 text-center`}
                    >
                      {loggedStudentData.get('status', '') === 'absent' && (
                        <img
                          style={{ marginTop: '-3px' }}
                          src={FailedImg}
                          alt="Digi-class"
                        />
                      )}{' '}
                      {CapitalizeAll(
                        loggedStudentData.get('status', '').replace('_', ' ')
                      )}
                    </div>
                  ) : (
                    <div className="text-red f-18 text-center">
                      <img
                        style={{ marginTop: '-3px' }}
                        src={FailedImg}
                        alt="Digi-class"
                      />{' '}
                      {t('absent')}
                    </div>
                  )}
                </>
              )}
            </>
          )
        ) : (
          ''
        )}
      </>
    );
  }

  const primaryAttendanceMode = schedule.getIn(
    ['sessionDetail', 'attendance_mode'],
    ''
  );
  const primaryAttendanceStartBy = schedule.getIn(
    ['sessionDetail', 'startBy'],
    ''
  );
  const retakeAttendanceMode = schedule.getIn(['retakeDetails', 'status'], '');
  const retakeAttendanceStartBy = schedule.getIn(
    ['retakeDetails', '_staff_id'],
    ''
  );

  const isRetake =
    schedule.get('isRetake', false) ||
    schedule
      .get('students', List())
      .every((student) => student.get('primaryStatus', '') === 'present');
  function onSiteRedirection(status) {
    const search = window.location.search;
    history.replace({
      pathname: `/sessions/onsite/${eString(status)}/${eString('old')}`,
      search: search,
      state: { load: false },
    });
  }

  function onSiteReports() {
    let search = window.location.search;
    search = removeURLParams(search, ['retakeId', 'retakeName']);
    history.push({
      pathname: `/sessions/onsite-reports`,
      search: search,
      state: { load: false },
    });
  }

  function getAttendanceVerification() {
    if (
      schedule.get('mode', '') !== 'onsite' ||
      (schedule.get('mode', 'remote') === 'onsite' &&
        schedule.get('scheduleStartFrom', '') !== 'web')
    )
      return <></>;
    const onsiteSession =
      schedule.get('mode', '') === 'onsite' &&
      schedule.get('status', '') === 'ongoing' &&
      authData.get('user_type', '') === 'staff';
    const userId = authData.get('_id', '');
    if (
      primaryAttendanceMode === 'ongoing' &&
      primaryAttendanceStartBy === userId
    ) {
      return (
        <div className="p-2 mb-2" style={{ backgroundColor: '#EDF6FF' }}>
          <p
            className="text-skyblue mb-0 f-15 icon"
            onClick={() => onSiteRedirection('primary')}
          >
            Go to Primary Attendance
          </p>
        </div>
      );
    }

    if (
      retakeAttendanceMode === 'running' &&
      retakeAttendanceStartBy === userId
    ) {
      const modeBy = schedule.getIn(['retakeDetails', 'modeBy'], '');
      return (
        <div className="p-2 mb-2" style={{ backgroundColor: '#EDF6FF' }}>
          <p
            className="text-skyblue mb-0 f-15 icon"
            onClick={() => onSiteRedirection(modeBy)}
          >
            Go to {getRetakeName(modeBy)} Attendance
          </p>
        </div>
      );
    }

    const isOnGoingAttendance =
      schedule.getIn(['sessionDetail', 'attendance_mode'], '') === 'ongoing' ||
      schedule.getIn(['retakeDetails', 'status'], '') === 'running';
    if (isOnGoingAttendance) return <></>;

    return onsiteSession && primaryAttendanceMode === 'completed' ? (
      <div className="p-2 mb-2" style={{ backgroundColor: '#EDF6FF' }}>
        <span className="text-gray f-14">
          <>Attendance Verification</>
        </span>
        <p
          className="text-skyblue mb-0 f-15 icon float-right"
          onClick={() => onSiteReports()}
        >
          View Report
        </p>
        <p className="mb-0">Verify Student Presence</p>
        <div className="row">
          <div className="col">
            <FormControl fullWidth variant="standard">
              <Select
                id="verify-with-label"
                value={attendanceOption}
                onChange={handleRetakes}
                IconComponent={() => null}
                sx={{
                  '& .MuiSelect-select:placeholder': {
                    backgroundColor: 'white',
                    paddingLeft: '8px',
                  },
                  ...ColorThemeSelect,
                }}
                MenuProps={{
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'right',
                  },
                }}
                renderValue={(selected) => {
                  return (
                    <div
                      style={{
                        backgroundColor: 'white',
                        padding: '8px 10px',
                        width: '109%',
                      }}
                    >
                      Verify With <ArrowRightIcon sx={{ float: 'right' }} />
                    </div>
                  );
                }}
                displayEmpty
              >
                <MenuItem disabled value="">
                  Verify With
                </MenuItem>
                {attendanceOptions.map((option) => (
                  <MenuItem
                    key={option.value}
                    value={option.value}
                    disabled={isRetake && option.value === 'retake_absent'}
                  >
                    {option.value !== '' && option.value === 'buzzer' ? (
                      <img
                        src={BuzzerIcon}
                        alt="ReTakeIcon"
                        className="m-2 pr-1"
                      />
                    ) : option.value !== '' ? (
                      <img src={ReTakeIcon} alt="ReTakeIcon" className="m-2" />
                    ) : (
                      ''
                    )}{' '}
                    {option.label}{' '}
                    {isRetake && option.value === 'retake_absent' ? (
                      <span className="text-red pl-1">(Already Utilized)</span>
                    ) : (
                      ''
                    )}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
        </div>
      </div>
    ) : (
      ''
    );
  }

  const checkMissedToComplete =
    isMissedSessionModuleEnabled() &&
    authData.get('user_type', 'student') !== 'student' &&
    schedule.get('isMissedToSchedule', false) &&
    isDcMissedSessionEnabled();

  return (
    <div className="col-md-6 col-lg-4 col-xl-4 p-3">
      <div className="bg-white border-radius-8 text-initial p-3">
        <div className="d-flex justify-content-between border-bottom">
          <p className="f-18 bold "> {t('session_detail')} </p>
          <div className="">
            <p
              className="text-skyblue mb-0 mt-1 f-15 icon"
              onClick={() => toggleMoreInfo(schedule)}
            >
              {t('more_info')}{' '}
              <i className="fa fa-info-circle pl-2" aria-hidden="true"></i>
            </p>
          </div>
        </div>

        {schedule.get('status', '').toLowerCase() !== 'completed' ? (
          <>
            <div className="pb-2 pt-2">
              <span className="text-gray f-14"> {t('session_timing')}</span>
              <p className="mb-0 ">
                {' '}
                {getTranslatedDuration(
                  getUTCSessionTiming({
                    schedule: schedule,
                    secondFormat: 'hh:mm A',
                  })
                )}
              </p>
            </div>

            <div className="pb-2 pt-2">
              <span className="text-gray f-14"> {t('student_group')}</span>
              <p className="mb-0 ">
                {schedule.get('type', '') !== 'regular'
                  ? studentGroupViewList(
                      schedule.get('student_groups', List()),
                      programId
                    )
                      .entrySeq()
                      .map(([groupName, sGroup]) => {
                        return (
                          getFormattedGroupName(
                            studentGroupRename(groupName, programId),
                            2
                          ) + `-${sGroup.get('session_group')}`
                        );
                      })
                      .join(', ')
                  : getStudentGroupsNew(
                      schedule.get('student_groups', List()),
                      programId
                    )}
                <span className="text-nowrap">
                  ({schedule.get('students', List()).size} {i18n.t('students')})
                </span>
              </p>
            </div>

            <div className="pb-2 pt-2">
              <span className="text-gray f-14">
                {' '}
                {schedule.get('staffs', List()).size > 1
                  ? t('multi_staff')
                  : t('staff')}{' '}
              </span>
              {schedule.get('staffs', List()).map((staff, staffIndex) => (
                <p key={staffIndex} className="mb-0 ">
                  {' '}
                  {formattedFullName(staff.get('staff_name', Map()).toJS())}
                  {authData.get('_id', '') === staff.get('_staff_id')
                    ? t('you')
                    : ''}
                </p>
              ))}
            </div>
            {isManualAttendanceEnabled() &&
              schedule.get('attendanceTakingStaff', List()).size > 0 && (
                <div className="pb-2 pt-2">
                  <span className="text-gray f-14">
                    {' '}
                    {t(
                      `Secondary Attendance staff${
                        schedule.get('attendanceTakingStaff', List()).size > 1
                          ? 's'
                          : ''
                      }`
                    )}
                  </span>
                  {schedule
                    .get('attendanceTakingStaff', List())
                    .map((staff, staffIndex) => {
                      return (
                        <p key={staffIndex} className="mb-0 ">
                          {' '}
                          {formattedFullName(
                            staff.get('staffName', Map()).toJS()
                          )}
                          {authData.get('_id', '') === staff.get('staffId')
                            ? t('you')
                            : ''}
                        </p>
                      );
                    })}
                </div>
              )}

            <div className="pb-2 pt-2">
              <span className="text-gray f-14">
                {' '}
                {getEnvLabelChanged() ? (
                  <>
                    Infra ({' '}
                    {Capitalize(
                      indVerRename(
                        schedule.get('mode', 'remote'),
                        schedule.get('_program_id', '')
                      )
                    )}{' '}
                    )
                  </>
                ) : (
                  <>
                    {t('infra')} ({' '}
                    {t(Capitalize(schedule.get('mode', 'remote')))} )
                  </>
                )}
              </span>
              <p className="mb-0" style={{ wordBreak: 'break-word' }}>
                {' '}
                {schedule.get('infra_name', '') !== ''
                  ? splitNameArray(schedule.get('infra_name', ''), 'http', 0)
                  : t('none')}
              </p>
            </div>
            {getAttendanceVerification()}
          </>
        ) : (
          <>
            <div className="pt-2 pb-2">
              {authData.get('user_type', '') === 'student' && (
                <div className="d-flex justify-content-center pt-3">
                  {t('your_rating')}
                </div>
              )}
              <div className="d-flex justify-content-center">
                {schedule.getIn(['feedBack', 'avgRating'], 0) ||
                loggedStudentData.getIn(['feedBack', 'rating'], 0) ? (
                  <div className={getClassName('pr-2-arabic', 'pr-2', 'f-30')}>
                    {' '}
                    {authData.get('user_type', '') === 'staff'
                      ? schedule.getIn(['feedBack', 'avgRating'], 0)
                      : loggedStudentData.getIn(['feedBack', 'rating'], 0)}
                    /5{' '}
                  </div>
                ) : (
                  ''
                )}
                <StarRatings
                  counts={
                    authData.get('user_type', '') === 'staff'
                      ? parseFloat(schedule.getIn(['feedBack', 'avgRating'], 0))
                      : parseFloat(
                          loggedStudentData.getIn(['feedBack', 'rating'], 0)
                        )
                  }
                  size={30}
                  isEnabled={false}
                />
              </div>
              <div className="text-center">
                <b>
                  {' '}
                  {schedule.getIn(['feedBack', 'totalFeedback'], 0) !== 0 ? (
                    <>
                      {authData.get('user_type', '') === 'staff'
                        ? `${schedule.getIn(
                            ['feedBack', 'totalFeedback'],
                            0
                          )} ${i18n.t('total_feedback')}   `
                        : '-'}{' '}
                    </>
                  ) : (
                    <>
                      {authData.get('user_type', '') === 'staff' &&
                        i18n.t('student_rated')}
                    </>
                  )}
                </b>
              </div>
              <div className="text-center pb-3 pt-1">
                <p className=" mb-0 mt-1 text-gray">
                  {getUTCSessionTiming({
                    schedule: schedule,
                    secondFormat: 'hh:mm A',
                  })}
                  {enableSwitch()}
                </p>
                {getStudentDurations()}
                <p className="mb-0 mt-1 f-14 text-gray">
                  {schedule.get('type', '') !== 'regular'
                    ? studentGroupViewList(
                        schedule.get('student_groups', List()),
                        programId
                      )
                        .entrySeq()
                        .map(([groupName, sGroup]) => {
                          return (
                            getFormattedGroupName(
                              studentGroupRename(groupName, programId),
                              2
                            ) + `-${sGroup.get('session_group')}`
                          );
                        })
                        .join(', ')
                    : getStudentGroupsNew(
                        schedule.get('student_groups', List()),
                        programId
                      )}
                  ( {schedule.get('students', List()).size} {t('students')} )
                </p>
              </div>
              {authData.get('user_type', '') === 'student' && (
                <>
                  <div className="pb-3 text-center">
                    <span className="text-gray f-14">
                      {' '}
                      {schedule
                        .get('subjects', List())
                        .map((subject) => subject.get('subject_name'))
                        .join(', ')}{' '}
                      *{' '}
                    </span>
                    <span className="text-gray f-14">
                      {' '}
                      {Capitalize(
                        indVerRename(
                          getInfraName(schedule),
                          schedule.get('_program_id', '')
                        )
                      )}{' '}
                      *{' '}
                      {schedule.get('infra_name', '') !== ''
                        ? splitNameArray(
                            schedule.get('infra_name', ''),
                            'http',
                            0
                          )
                        : t('none')}
                    </span>
                  </div>
                  {schedule.get('staffs', List()).map((staff, staffIndex) => (
                    <p key={staffIndex} className="mb-0 text-center">
                      {' '}
                      {formattedFullName(staff.get('staff_name', Map()).toJS())}
                      {authData.get('_id', '') === staff.get('_staff_id')
                        ? i18n.t('you')
                        : ''}
                    </p>
                  ))}
                </>
              )}

              {authData.get('user_type', '') === 'staff' && (
                <div className="text-center pb-4">
                  <p className=" mb-0 mt-1 text-gray">
                    {getStudents('present')} / {getStudents('all')}{' '}
                    {i18n.t('students')} {i18n.t('present')}
                  </p>
                  <p className=" mb-0 mt-1 f-14 text-gray">
                    {getStudents('leave')} {i18n.t('leave_permission')} •{' '}
                    {getStudents('absent')} {i18n.t('absent')}
                  </p>
                </div>
              )}
            </div>
          </>
        )}

        <div className="border-top">
          <div
            className={
              checkMissedToComplete &&
              (schedule.get('status', '') === 'missed' ||
                schedule.get('isMissedToComplete', false))
                ? 'd-flex justify-content-between pt-3'
                : 'pt-3'
            }
          >
            {authData.get('user_type', '') === 'staff' ? (
              <SessionButton
                authData={authData}
                schedule={schedule}
                startSession={startSession}
                endSession={endSession}
                viewReports={viewReports}
                fetchApi={fetchApi}
                resetSession={resetSession}
                sessionRatingOpen={sessionRatingOpen}
                openPopUp={openPopUp}
                warning={warning}
                courseRestriction={courseRestriction}
              />
            ) : loggedStudentData.get('isRestricted', false) &&
              loggedStudentData.get('status') === 'absent' ? (
              <div className="text-red f-18 text-center">
                {t(loggedStudentData.get('status', 'absent'))} (Course
                Restricted)
              </div>
            ) : courseRestriction ? (
              restrictCourseDetails()
            ) : (
              <SessionButton
                authData={authData}
                schedule={schedule}
                startSession={startSession}
                endSession={endSession}
                viewReports={viewReports}
                fetchApi={fetchApi}
                resetSession={resetSession}
                sessionRatingOpen={sessionRatingOpen}
                openPopUp={openPopUp}
                warning={warning}
                courseRestriction={courseRestriction}
              />
            )}
            {checkMissedToComplete && (
              <div>
                {schedule.get('status', '') === 'missed' ? (
                  <Button
                    variant={'outline-primary'}
                    size="lg"
                    onClick={() => setMissedToCompleteOpen(true)}
                    className="ml-2"
                  >
                    <Box sx={{ whiteSpace: 'pre-wrap' }}>
                      Missed to complete
                    </Box>
                  </Button>
                ) : schedule.get('isMissedToComplete', false) ? (
                  <Button
                    block
                    variant={'outline-primary'}
                    size="lg"
                    onClick={() => setRevokeOpen(true)}
                    className="ml-2"
                  >
                    Revoke
                  </Button>
                ) : (
                  ' '
                )}
              </div>
            )}

            {missedToCompleteOpen && (
              <Suspense fallback="">
                <MissedToCompleteModal
                  open={missedToCompleteOpen}
                  setMissedToCompleteOpen={setMissedToCompleteOpen}
                  handleSave={handleSave}
                />
              </Suspense>
            )}

            {revokeOpen && (
              <Suspense fallback="">
                <RevokeModal
                  open={revokeOpen}
                  setRevokeOpen={setRevokeOpen}
                  handleSave={handleSave}
                />
              </Suspense>
            )}
            {openAlertModal ? (
              <Suspense fallback="">
                <RetakeAlertModal
                  open={openAlertModal}
                  cancelModal={() => {
                    setOpenAlertModal();
                    setAttendanceOption('');
                  }}
                  handleRetakeRedirect={handleRetakeRedirect}
                />
              </Suspense>
            ) : (
              ''
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

SessionCard.propTypes = {
  authData: PropTypes.instanceOf(Map),
  schedule: PropTypes.instanceOf(Map),
  toggleMoreInfo: PropTypes.func,
  startSession: PropTypes.func,
  endSession: PropTypes.func,
  viewReports: PropTypes.func,
  fetchApi: PropTypes.func,
  resetSession: PropTypes.func,
  sessionRatingOpen: PropTypes.func,
  openPopUp: PropTypes.func,
  updateMissedSession: PropTypes.func,
  warning: PropTypes.string,
  courseRestriction: PropTypes.bool,
};

export default SessionCard;
