body, html {
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
  background-color: #f4f6f8;
}

#details-content h3 {
  margin-bottom: 10px;
  color: #00AFAA;
}

.students-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.students-list {
  flex: 1;
  margin-right: 10px;
}

.students-list h4 {
  background-color: #00AFAA;
  padding: 10px;
  margin: 0;
  font-size: 16px;
  border-radius: 4px;
  color: #ffffff;
}

.students-list ul {
  /* list-style: none; */
  padding: 0;
  margin: 0;
  background-color: #fff;
  border: 1px solid #ddd;
  max-height: 300px;
  overflow-y: auto;
}

.students-list li {
  padding: 8px 10px;
  border-bottom: 1px solid #ddd;
  color: #333;
}

.students-list li:last-child {
  border-bottom: none;
}

.students-list li:nth-child(odd) {
  background-color: #f9f9f9;
}

#no-exams-grid, #no-sessions-grid, #no-exams-grid-filter, #no-sessions-grid-filter .no-exam {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vh;
  /* margin-top: 20%; */
  margin-left: 10%;
}

#no-exams-grid, #no-sessions-grid, #no-exams-grid-filter, #no-sessions-grid-filter .no-exam .text-container { 
  text-align: center;
  font-weight: bold;
  color: #ffbb00; 
  border: 2px solid #ffbb00;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  max-width: 400px; 
  background-color: #fff; 
}

#no-exams-grid, #no-sessions-grid, #no-exams-grid-filter, #no-sessions-grid-filter .no-exam .text-container i {
  margin-bottom: 15px; 
}

.color-picker input {
  width: 50%;
}

#current-time {
  padding-left: 5px;
}

.header {
  background-color: rgb(0, 175, 170);
  color: white;
  text-align: center;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header .container {
  flex: 1;
}

.footer {
  background-color: rgb(0, 175, 170);
  color: white;
  text-align: center;
}

.instruction-wrapper {  
  margin-left: 5px;
}

#current-date-time {
  font-size: 18px;
  color: #ddd;
}

#current-date {
  padding-right: 5px;
}

.slider-container {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: auto; /* Adjust based on content */
}

.slider {
  display: flex;
  transition: transform 0.5s ease-in-out;
  width: 200%; /* Double the width for sliding effect */
}

.slide {
  width: 50%; /* Each slide takes up half of the slider */
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}

.status-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  padding: 20px;
  text-align: left;
  cursor: pointer;
  max-width: none;
}

.status-card i {
  font-size: 24px;
  margin-bottom: 10px;
}

.status-card h3 {
  margin: 10px 0;
  font-size: 18px;
  color: #333;
}

.status-card p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.sidebar {
  position: fixed;
  right: -100%;
  top: 0;
  width: 100%;
  max-width: 40%;
  height: 100%;
  background-color: #ffffff;
  box-shadow: -2px 0 5px rgba(0,0,0,0.2);
  transition: right 0.3s ease-in-out;
  padding: 20px;
  z-index: 1000;
  overflow-y: auto; /* Enable vertical scrolling */
}

.sidebar.open {
  right: 0;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sidebar h2 {
  margin-top: 0;
  color: #333;
}

.sidebar button.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  align-self: flex-end;
  cursor: pointer;
  transition: transform 0.2s, color 0.2s;
}

.sidebar button.close-btn:hover {
  transform: scale(1.2);
  color: red; 
}

.sidebar button.close-btn:active {
  transform: scale(0.9);
}

footer {
  position: fixed;
  bottom: 0;
  width: 100%;
}

#current-date-time {
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-icon {
  margin-left: 10px;
  font-size: 24px;
  cursor: pointer;
}

#settings-sidebar {
  position: fixed;
  right: -100%;
  top: 0;
  width: 100%;
  max-width: 400px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: -2px 0 5px rgba(0,0,0,0.2);
  transition: right 0.3s ease-in-out;
  padding: 20px;
  z-index: 1000;
  overflow-y: auto; /* Enable vertical scrolling */
}

#settings-sidebar.open {
  right: 0;
}

.filter-section {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.filter-panel {
  margin-top: 20px;
}

.filter-panel h3 {
  margin-bottom: 15px;
}

.filter-panel label {
  display: block;
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.filter-panel button {
  margin-top: 20px;
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background-color: #1877F2;
  color: white;
  font-size: 16px;
}

.students-container {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.students-list {
  width: 45%;
}

.students-list li {
  margin-bottom: 5px;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.status-card.ok {
  border-left: 10px solid #48bb78; /* Green */
}

.status-card.warn {
  border-left: 10px solid #ed8936; /* Orange */
}

.status-card.failed {
  border-left: 10px solid #e53e3e; /* Red */
}

.status-card.ongoing {
  border-left: 10px solid #4299e1; /* Blue */
}

.status-card.upcoming {
  border-left: 10px solid #ecc94b; /* Yellow */
}

.status-card.late {
  border-left: 10px solid #e53e3e; /* Red */
}

.status-card.completed {
  border-left: 10px solid #48bb78; /* Green */
}
.status-card.Ended {
  border-left: 10px solid #48bb78; /* Green */
}
.status-card.ON_GOING {
  border-left: 10px solid #4299e1; /* Blue */
}
.status-card.NOT_STARTED {
  border-left: 10px solid #ed8936; /* Orange */
}
.nogrid{
  display: flex !important;

}

#no-exams-grid{
  margin: 0 auto;
}

.user-avatar-container {
  display: flex;
  align-items: center;
  margin-right: 1.5rem !important;
  height: 100%;
  position: relative;
}

.user-avatar {
  width: 38px;
  height: 38px;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.3);
}

.user-avatar:hover {
  transform: scale(1.05);
}

#user-Initial {
  color: #00AFAA;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 240px;
  z-index: 1000;
  padding: 0;
  overflow: hidden;
  border: 1px solid rgba(0, 175, 170, 0.1);
  animation: dropdownFade 0.2s ease-in-out;
  margin-top: 10px;
}

.user-dropdown::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 15px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}

@keyframes dropdownFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.btn-primary {
  color: #fff !important;
  background-color: #00AFAA !important;
  border-color: #00AFAA !important;
}

.user-info {
  padding: 16px;
  width: 100%;
  overflow: hidden;
  background: #00AFAA;
  color: white;
}

.user-name, .user-id {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  padding: 2px 0;
}

.user-name {
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
  font-size: 1.1em;
}

.user-id {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9em;
}

.dropdown-item {
  padding: 12px 16px;
  width: 100%;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.dropdown-item i {
  width: 20px;
  text-align: center;
  color: #00AFAA;
}

.dropdown-item:hover {
  background-color: #f0f7ff;
  color: #00AFAA;
}

#logoutButton {
  border-top: 1px solid #eee;
  color: #dc3545;
}

#logoutButton i {
  color: #dc3545;
}

#logoutButton:hover {
  background-color: #fff5f5;
  color: #dc3545;
}

.refresh-time-container {
  padding: 10px 0;
}

.refresh-time-container input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 5px;
}

.refresh-time-container small {
  color: #666;
  font-size: 0.8em;
}

.refresh-time-container input:focus {
  border-color: #1877F2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
}

.main-content.hidden {
    display: none !important;
}

#clearCache {
  margin: 15px 0;
  color: #1877F2;
  cursor: pointer;
}

.tab-container {
  background: #f4f6f8;
  border-radius: 4px;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 20px;
}

.tab-container a {
  font-size: 20px;
}

.nav-tabs {
  border: none;
  padding: 0;
  display: flex;
}

.nav-tabs .nav-item {
  margin: 0;
}

.nav-tabs .nav-link {
  color: #666;
  border: none;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  color: #00AFAA;
  background: #b1ced8;
}

.nav-tabs .nav-link.active {
  border-top: 4px solid #00AFAA; 
  background-color: #fff;        
  color: #00AFAA;                
  border-left: none;           
  border-right: none;            
  border-bottom: none;          
}

.tab-content {
  background: #fff;
  border-radius: 4px;
  min-height: calc(100vh - 215px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-link {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-link .counter {
  background: rgba(0,0,0,0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.9em;
}

.nav-link.active .counter {
  background: rgba(255,255,255,0.2);
}