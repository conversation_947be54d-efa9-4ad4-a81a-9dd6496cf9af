/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[21],{380:function(ha,ea,f){f.r(ea);var ba=f(2),z=f(12),fa=f(1);ha=f(44);var da=f(31),ca=f(10);f=function(){function f(){this.init()}f.prototype.init=function(){this.I0=!1;this.Zf=this.nk=this.connection=null;this.uq={};this.ea=this.mC=null};f.prototype.Kea=function(f){for(var w=this,e=0;e<f.length;++e){var h=f[e];switch(h.at){case "create":this.uq[h.author]||(this.uq[h.author]=h.aName);this.x7(h);break;case "modify":this.ea.Hm(h.xfdf).then(function(e){w.ea.Db(e[0])});
break;case "delete":this.ea.Hm("<delete><id>"+h.aId+"</id></delete>")}}};f.prototype.x7=function(f){var w=this;this.ea.Hm(f.xfdf).then(function(e){e=e[0];e.authorId=f.author;w.ea.Db(e);w.ea.trigger(z.a.UPDATE_ANNOTATION_PERMISSION,[e])})};f.prototype.Z6=function(f,w,e){this.nk&&this.nk(f,w,e)};f.prototype.preloadAnnotations=function(f){this.addEventListener("webViewerServerAnnotationsEnabled",this.Z6.bind(this,f,"add",{imported:!1}),{once:!0})};f.prototype.initiateCollaboration=function(x,w,e){var h=
this;if(x){h.Zf=w;h.ea=e.ua();e.addEventListener(z.c.DOCUMENT_UNLOADED,function(){h.disableCollaboration()});h.jfa(x);var r=new XMLHttpRequest;r.addEventListener("load",function(){if(200===r.status&&0<r.responseText.length)try{var e=JSON.parse(r.responseText);h.connection=exports.mb.Kfa(Object(da.i)(h.Zf,"blackbox/"),"annot");h.mC=e.id;h.uq[e.id]=e.user_name;h.ea.UI(e.id);h.connection.xJ(function(e){e.t&&e.t.startsWith("a_")&&e.data&&h.Kea(e.data)},function(){h.connection.send({t:"a_retrieve",dId:x});
h.trigger(f.Events.WEBVIEWER_SERVER_ANNOTATIONS_ENABLED,[h.uq[e.id],h.mC])},function(){h.disableCollaboration()})}catch(ja){Object(fa.f)(ja.message)}});r.open("GET",Object(da.i)(this.Zf,"demo/SessionInfo.jsp"));r.withCredentials=!0;r.send();h.I0=!0;h.ea.$T(function(e){return h.uq[e.Author]||e.Author})}else Object(fa.f)("Document ID required for collaboration")};f.prototype.disableCollaboration=function(){this.nk&&(this.ea.removeEventListener(ca.a.Events.ANNOTATION_CHANGED,this.nk),this.nk=null);this.connection&&
this.connection.lF();this.ea&&this.ea.UI("Guest");this.init();this.trigger(f.Events.WEBVIEWER_SERVER_ANNOTATIONS_DISABLED)};f.prototype.jfa=function(f){var w=this;this.nk&&this.ea.removeEventListener(ca.a.Events.ANNOTATION_CHANGED,this.nk);this.nk=function(e,h,r){return Object(ba.b)(this,void 0,void 0,function(){var x,y,n,z,ca,da,ea,fa,ha;return Object(ba.d)(this,function(aa){switch(aa.label){case 0:if(r.imported)return[2];x={t:"a_"+h,dId:f,annots:[]};return[4,w.ea.NO()];case 1:y=aa.ha();"delete"!==
h&&(n=(new DOMParser).parseFromString(y,"text/xml"),z=new XMLSerializer);for(ca=0;ca<e.length;ca++)da=e[ca],fa=ea=void 0,"add"===h?(ea=n.querySelector('[name="'+da.Id+'"]'),fa=z.serializeToString(ea),ha=null,da.InReplyTo&&(ha=w.ea.rg(da.InReplyTo).authorId||"default"),x.annots.push({at:"create",aId:da.Id,author:w.mC,aName:w.uq[w.mC],parent:ha,xfdf:"<add>"+fa+"</add>"})):"modify"===h?(ea=n.querySelector('[name="'+da.Id+'"]'),fa=z.serializeToString(ea),x.annots.push({at:"modify",aId:da.Id,xfdf:"<modify>"+
fa+"</modify>"})):"delete"===h&&x.annots.push({at:"delete",aId:da.Id});0<x.annots.length&&w.connection.send(x);return[2]}})})}.bind(w);this.ea.addEventListener(ca.a.Events.ANNOTATION_CHANGED,this.nk)};f.Events={WEBVIEWER_SERVER_ANNOTATIONS_ENABLED:"webViewerServerAnnotationsEnabled",WEBVIEWER_SERVER_ANNOTATIONS_DISABLED:"webViewerServerAnnotationsDisabled"};return f}();Object(ha.a)(f);ea["default"]=f}}]);}).call(this || window)
