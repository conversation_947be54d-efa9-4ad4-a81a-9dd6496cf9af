/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[7],{369:function(ha,ea,f){f.r(ea);var ba=f(2);ha=f(44);var z=f(140),fa=f(318),da=f(191),ca=window;f=function(){function f(f,w){this.XQ=function(e){e=e.split(".");return e[e.length-1].match(/(jpg|jpeg|png|gif)$/i)};w=w||{};this.url=f;this.filename=w.filename||f;this.nf=w.customHeaders;this.Xea=!!w.useDownloader;this.withCredentials=!!w.withCredentials}f.prototype.LB=function(f){this.nf=f};f.prototype.getFileData=function(x){var w=
this,e=this,h=new XMLHttpRequest,r=0===this.url.indexOf("blob:")?"blob":"arraybuffer";h.open("GET",this.url,!0);h.withCredentials=this.withCredentials;h.responseType=r;this.nf&&Object.keys(this.nf).forEach(function(e){h.setRequestHeader(e,w.nf[e])});var y=/^https?:/i.test(this.url);h.addEventListener("load",function(h){return Object(ba.b)(this,void 0,void 0,function(){var n,r,w,aa,ca,ea;return Object(ba.d)(this,function(ba){switch(ba.label){case 0:if(200!==this.status&&(y||0!==this.status))return[3,
10];e.trigger(f.Events.DOCUMENT_LOADING_PROGRESS,[h.loaded,h.loaded]);if("blob"!==this.responseType)return[3,4];n=this.response;return e.XQ(e.filename)?[4,Object(da.b)(n)]:[3,2];case 1:return r=ba.ha(),e.fileSize=r.byteLength,x(new Uint8Array(r)),[3,3];case 2:w=new FileReader,w.onload=function(f){f=new Uint8Array(f.target.result);e.fileSize=f.length;x(f)},w.readAsArrayBuffer(n),ba.label=3;case 3:return[3,9];case 4:ba.vl.push([4,8,,9]);aa=new Uint8Array(this.response);if(!e.XQ(e.filename))return[3,
6];n=new Blob([aa.buffer]);return[4,Object(da.b)(n)];case 5:return r=ba.ha(),e.fileSize=r.byteLength,x(new Uint8Array(r)),[3,7];case 6:e.fileSize=aa.length,x(aa),ba.label=7;case 7:return[3,9];case 8:return ba.ha(),e.trigger(f.Events.ERROR,["pdfLoad","Out of memory"]),[3,9];case 9:return[3,11];case 10:ca=h.currentTarget,ea=Object(z.b)(ca),e.trigger(f.Events.ERROR,["pdfLoad",this.status+" "+ca.statusText,ea]),ba.label=11;case 11:return e.Iw=null,[2]}})})},!1);h.onprogress=function(h){e.trigger(f.Events.DOCUMENT_LOADING_PROGRESS,
[h.loaded,0<h.total?h.total:0])};h.addEventListener("error",function(){e.trigger(f.Events.ERROR,["pdfLoad","Network failure"]);e.Iw=null},!1);h.send();this.Iw=h};f.prototype.getFile=function(){var f=this;return new Promise(function(w){ca.utils.isJSWorker&&w(f.url);if(f.Xea){var e=Object(ba.a)({url:f.url},f.nf?{customHeaders:f.nf}:{});w(e)}w(null)})};f.prototype.abort=function(){this.Iw&&(this.Iw.abort(),this.Iw=null)};f.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress",ERROR:"error"};return f}();
Object(ha.a)(f);Object(fa.a)(f);Object(fa.b)(f);ea["default"]=f}}]);}).call(this || window)
