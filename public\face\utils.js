const usersList = [
  {
    name: 'D0033<PERSON>',
    staffName: 'siddiq',
    id: '6730cc0ae88b8edfd5064493',
  },
  {
    name: 'EA101',
    staffName: 'staff01',
    id: '656037924c84ea69939c40b2',
  },
  {
    name: 'EA102',
    staffName: 'staff02',
    id: '656037924c84eae3379c40b3',
  },
  {
    name: 'EA103',
    staffName: 'staff03',
    id: '656037924c84ea47269c40b4',
  },
  {
    name: 'EA104',
    staffName: 'staff04',
    id: '656037924c84ea427a9c40b5',
  },
  {
    name: 'EA105',
    staffName: 'staff05',
    id: '656037924c84ea99b19c40b6',
  },
  {
    name: 'EA106',
    staffName: 'staff06',
    id: '656037924c84eaa6f29c40b7',
  },
  {
    name: 'EA111',
    staffName: 'staff11',
    id: '656037924c84eac44e9c40bc',
  },
  {
    name: 'EA116',
    staffName: 'staff16',
    id: '656037924c84ea41dc9c40c1',
  },
  {
    name: 'EA117',
    staffName: 'staff17',
    id: '656037924c84ea6ac89c40c2',
  },
  {
    name: 'EA119',
    staffName: 'staff19',
    id: '656037924c84ea08db9c40c4',
  },
  {
    name: 'EA125',
    staffName: 'staff25',
    id: '656037924c84ea48099c40ca',
  },
  {
    name: 'EA130',
    staffName: 'staff30',
    id: '656037924c84ea31309c40cf',
  },
  {
    name: 'EA135',
    staffName: 'staff35',
    id: '656037924c84ea0bb29c40d4',
  },
  {
    name: 'EA138',
    staffName: 'staff38',
    id: '656037924c84ea890a9c40d7',
  },
  {
    name: 'EA139',
    staffName: 'staff39',
    id: '656037924c84ea35659c40d8',
  },
  {
    name: 'EA144',
    staffName: 'staff44',
    id: '656037924c84ea11cb9c40dd',
  },
  {
    name: 'EA150',
    staffName: 'staff50',
    id: '656037924c84ea12c59c40e3',
  },
  {
    name: 'EA113',
    staffName: 'staff13',
    id: '656037924c84ea802c9c40be',
  },
  {
    name: 'EA114',
    staffName: 'staff14',
    id: '656037924c84ea44309c40bf',
  },
  {
    name: 'EA118',
    staffName: 'staff18',
    id: '656037924c84ead6fe9c40c3',
  },
  {
    name: 'EA131',
    staffName: 'staff31',
    id: '656037924c84ea76f19c40d0',
  },
  {
    name: 'EA132',
    staffName: 'staff32',
    id: '656037924c84ea4ba89c40d1',
  },
  {
    name: 'EA133',
    staffName: 'staff33',
    id: '656037924c84ea3bb69c40d2',
  },

  {
    name: '9876543243',
    staffName: 'Test34',
    id: '60a4e86750e7141077c9cb3c',
  },
  {
    name: '9876543262',
    staffName: 'Test53',
    id: '60a4e86750e7142a23c9cb4f',
  },
  {
    name: '9876543254',
    staffName: 'Test45',
    id: '60a4e86750e7146af9c9cb47',
  },
  {
    name: '9876543238',
    staffName: 'Test29',
    id: '60a4e86750e714a002c9cb37',
  },
  {
    name: '9876543213',
    staffName: 'Test4',
    id: '60a4e86750e7147382c9cb1e',
  },
  {
    name: '9876543235',
    staffName: 'Test26',
    id: '60a4e86750e714513ac9cb34',
  },
  {
    name: '9876543255',
    staffName: 'Test46',
    id: '60a4e86750e7146349c9cb48',
  },
  {
    name: '9876543236',
    staffName: 'Test27',
    id: '60a4e86750e7142485c9cb35',
  },
  {
    name: '9876543228',
    staffName: 'Test19',
    id: '60a4e86750e71411f9c9cb2d',
  },
  {
    name: '9876543244',
    staffName: 'Test35',
    id: '60a4e86750e71480a5c9cb3d',
  },
];

function getQueryParam(name) {
  const params = new URLSearchParams(window.location.search);
  return params.get(name); // Returns the value of the parameter, or null if not found
}
