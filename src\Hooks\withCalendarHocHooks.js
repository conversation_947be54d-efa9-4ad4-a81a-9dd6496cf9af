import useCalendar from './CalendarHook';
import React from 'react';

function withCalendarHooks(Component) {
  const InjectedCurrentCalendar = function (props) {
    const {
      isCurrentAcademicYear,
      currentCalendarId,
      isAdminCourse,
      academicYears,
    } = useCalendar();
    return (
      <Component
        {...props}
        isAdminCourse={isAdminCourse}
        academicYears={academicYears}
        isCurrentAcademicYear={isCurrentAcademicYear}
        currentCalendarId={currentCalendarId}
      />
    );
  };
  return InjectedCurrentCalendar;
}

export default withCalendarHooks;
