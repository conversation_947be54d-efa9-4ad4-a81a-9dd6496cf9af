/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[15],{373:function(ha,ea,f){function ba(e,f,h){f.endsWith("/")||(f+="/");h=h||{};var n=h.disableWebsockets||!1;this.OU=h.singleServerMode||!1;null!=h.customQueryParameters&&Object(aa.b)("wvsQueryParameters",h.customQueryParameters);f.endsWith("blackbox/")||(f+="blackbox/");this.nn=h.uploadData||null;this.Aw=h.uriData||null;this.AN=h.cacheKey||null;this.SR=h.officeLocale||null;this.JE=Object(w.a)(f,null,n);this.Zf=f;this.$E=e;this.vf=
null;this.Ej=z();this.fm=z();this.Jy=!1;this.lg=this.Vd=this.pe=this.af=null;this.Bf=[];this.sz=[];this.cache={};this.timeStamp=0;this.Uf=[];this.nh=[];this.vF=null;this.SE=!1;this.QI=this.id=null;this.uH=this.vQ=fa;this.CB=0;this.xG=!1;this.r9=1;this.HA={};this.os(!0)}function z(){var e={promise:null,resolve:null,reject:null,state:0,result:null,request:null,zr:function(){return 1===(e.state&1)},n8:function(){return 2===(e.state&2)},di:function(){return!e.n8()&&!e.zr()},T7:function(){return 4===(e.state&
4)},fU:function(){e.state|=4}};e.promise=new Promise(function(f,h){e.resolve=function(){if(0===e.state||4===e.state)e.state=1,e.result=arguments[0],f.apply(null,arguments)};e.reject=function(){if(0===e.state||4===e.state)e.state=2,h.apply(null,arguments)}});return e}function fa(){return!1}function da(e,f,h){if(!(f in n))return!0;f=n[f];for(var r=0;r<f.length;r++){var w=e;var x=f[r];var y=h;if(x.name in w){var z="",aa=!1;w=w[x.name];switch(x.type){case "s":z="String";aa=Object(ca.isString)(w);break;
case "a":z="Array";aa=Object(ca.isArray)(w);break;case "n":z="Number";aa=Object(ca.isNumber)(w)&&Object(ca.isFinite)(w);break;case "o":z="Object",aa=Object(ca.isObject)(w)&&!Object(ca.isArray)(w)}aa||y.reject('Expected response field "'+x.name+'" to have type '+z);x=aa}else y.reject('Response missing field "'+x.name+'"'),x=!1;if(!x)return!1}return!0}f.r(ea);var ca=f(0);f.n(ca);var y=f(1);ha=f(44);var x=f(31),w=f(391),e=f(78),h=f(318),r=f(97),aa=f(39),ja=f(140),n={pages:[{name:"pages",type:"a"}],pdf:[{name:"url",
type:"s"}],docmod:[{name:"url",type:"s"},{name:"rID",type:"s"}],health:[],tiles:[{name:"z",type:"n"},{name:"rID",type:"n"},{name:"tiles",type:"a"},{name:"size",type:"n"}],annots:[{name:"url",type:"s"},{name:"name",type:"s"}],image:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],text:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}]};ba.prototype=Object(ca.extend)(ba.prototype,{T1:function(){var e=this;return new Promise(function(f,h){var n=new XMLHttpRequest;n.open("GET",
e.Zf+"ck");n.withCredentials=e.No();n.onreadystatechange=function(){n.readyState===XMLHttpRequest.DONE&&(200===n.status?f():h())};n.send()})},Lca:function(e,f){this.vQ=e||fa;this.uH=f||fa},RN:function(){var e=this;this.fm=z();this.Ej=z();return this.JE.lF().then(function(){e.Jy=!1;e.id=null;e.SE=!1;return e.T1()})},CI:function(){this.vQ();this.OG();this.af&&(this.af.di()?this.wf(this.af.request):this.af.zr()&&this.uH(this.af.result.url,"pdf")&&(this.af=null,this.AT()));this.lg&&this.lg.di()&&this.wf(this.lg.request);
this.pe&&this.pe.di()?this.wf(this.pe.request):this.Vd&&this.Vd.di()&&this.kQ(this.Vd.request);var e;for(e=0;e<this.Uf.length;e++)this.Uf[e]&&this.Uf[e]&&(this.Uf[e].di()?this.wf(this.Uf[e].request):this.Uf[e].zr()&&this.uH(this.Uf[e].result.url,"image")&&(this.Uf[e]=null,this.zB(e)));for(e=0;e<this.nh.length;e++)this.nh[e]&&this.nh[e]&&this.nh[e].di()&&!this.nh[e].T7()&&this.wf(this.nh[e].request);for(e=0;e<this.Bf.length;e++)this.Bf[e]&&this.Bf[e].di()&&this.wf(this.Bf[e].request)},OG:function(e){var f=
this;this.Jy||(this.timeStamp=Date.now(),this.JE.xJ(function(e){f.F9(e)},function(){return null},function(){return null},!0).then(function(){clearInterval(f.qB);f.qB=null},function(h){f.Jy=!1;if(!f.qB){var n=0;f.SE=!0;f.QI=0;f.qB=setInterval(function(){2>n++?f.CI():(clearInterval(f.qB),e&&e.reject(r.a),Object(y.f)("WebViewerServer connection failed:"+h))},5E3)}}),this.Jy=!0)},Uea:function(){var e=this,f=createPromiseCapability();if(this.nn){var h=new FormData;h.append("file",this.nn.fileHandle,this.nn.fileHandle.name);
var n=this.nn.loadCallback;var r="upload";var w=this.nn.extension}else if(this.Aw){h={uri:this.Aw.uri,Lia:this.Aw.shareId};h=Object.keys(h).map(function(e){return e+"="+(h[e]?encodeURIComponent(h[e]):"")}).join("&");var y="application/x-www-form-urlencoded; charset=UTF-8";n=this.Aw.loadCallback;r="url";w=this.Aw.extension}else return Promise.resolve();var z=new XMLHttpRequest,aa=Object(x.i)(e.Zf,"AuxUpload");aa=Object(ja.a)(aa)+"&type="+r+"&ext="+w;z.open("POST",aa);z.withCredentials=this.No();y&&
z.setRequestHeader("Content-Type",y);z.addEventListener("load",function(){if(z.readyState===z.DONE&&200===z.status){var h=JSON.parse(z.response);e.$E=h.uri;n(h);f.resolve(h)}});z.addEventListener("error",function(){f.reject(z.statusText+" "+JSON.stringify(z))});this.nn&&null!=this.nn.onProgress&&(z.upload.onprogress=function(f){e.nn.onProgress(f)});z.send(h);return f.promise},tba:function(e){this.password=e||null;this.Ej.zr()||(this.Ej=z(),this.wf({t:"pages"}));return this.Ej.promise},Uv:function(e){this.vF=
e||null;this.Ej.zr()||(this.OG(this.Ej),this.wf({t:"pages"}));return this.Ej.promise},TN:function(e){e=Object.assign(e,{uri:encodeURIComponent(this.$E)});this.vF&&(e.ext=this.vF);this.vf&&(e.c=this.vf);this.password&&(e.pswd=this.password);this.AN&&(e.cacheKey=this.AN);this.SR&&(e.locale=this.SR);return e},wf:function(e){e=this.TN(e);this.JE.send(e)},uk:function(e){return e},F9:function(f){var h=this,n=f.data,r=f.err,w=f.t;switch(w){case "upload":r?h.Vea.reject(r):h.Vea.resolve("Success");break;case "pages":r?
h.Ej.reject(r):da(n,w,h.Ej)&&h.Ej.resolve(n);break;case "config":r?h.fm.reject(r):da(n,w,h.fm)&&(n.id&&(h.id=n.id),n.auth&&(f=Object(aa.a)("wvsQueryParameters"),f.auth=n.auth,Object(aa.b)("wvsQueryParameters",f)),n.serverVersion&&(h.qfa=n.serverVersion,Object(y.g)("[WebViewer Server] server version: "+h.qfa)),n.serverID?(h.CB=n.serverID===h.QI&&h.xG?h.CB+1:0,h.QI=n.serverID):h.CB=0,h.xG=!1,h.fm.resolve(n));break;case "health":r?h.fm.reject(r):da(n,w,h.fm)&&(n=n.unhealthy,h.OU&&n?Object(y.i)("Server failed health check. Single server mode ignoring check."):
!h.gha&&n&&1>=h.CB&&(h.xG=!0,h.RN().then(function(){h.CI()},function(){h.CI()})));break;case "pdf":n.url=Object(ja.a)(h.Zf+"../"+encodeURI(n.url));r?h.af.reject(r):da(n,w,h.af)&&h.af.resolve(n);break;case "docmod":n.url=Object(ja.a)(h.Zf+"../"+encodeURI(n.url));r?h.HA[n.rID].reject(r):da(n,w,h.af)&&h.HA[n.rID].resolve(n);break;case "xod":if(r)this.pe&&this.pe.di()&&this.pe.reject(r),this.Vd&&this.Vd.di()&&this.Vd.reject(r);else if(n.notFound)n.noCreate||this.pe&&this.pe.di()&&this.pe.resolve(n),this.Vd&&
this.Vd.di()&&this.Vd.resolve(n);else{n.url&&(n.url=Object(ja.a)(h.Zf+"../"+encodeURI(n.url)));if(!this.Vd||this.Vd.zr())this.Vd=z(),this.Vd.request={t:"xod",noCreate:!0};this.pe||(this.pe=z(),this.pe.request={t:"xod"});this.Vd.resolve(n);this.pe.resolve(n)}break;case "annots":if(r)h.lg.reject(r);else if(da(n,w,h.lg)){h.lg.fU();var x=new XMLHttpRequest;f=h.Zf+"../"+encodeURI(n.url);var ba=n.hasAppearance?Object(ja.a)(f+".xodapp"):null;x.open("GET",Object(ja.a)(f));x.responseType="text";x.withCredentials=
this.No();x.addEventListener("load",function(){x.readyState===x.DONE&&200===x.status&&h.lg.resolve({TJ:x.response,lN:ba})});x.addEventListener("error",function(){h.lg.reject(x.statusText+" "+JSON.stringify(x))});x.send()}break;case "image":var ca=this.Uf[n.p];r?ca.promise.reject(r):da(n,w,ca)&&(ca.result=n,ca.result.url=Object(ja.a)(h.Zf+"../"+encodeURI(ca.result.url)),ca.resolve(ca.result));break;case "tiles":ca=n.rID;f=this.Bf[ca];this.Bf[ca]=null;this.sz.push(ca);if(r)f.reject(r);else if(da(n,
w,f)){for(r=0;r<n.tiles.length;r++)n.tiles[r]=Object(ja.a)(h.Zf+"../"+encodeURI(n.tiles[r]));f.resolve(n)}break;case "text":ca=this.nh[n.p];if(r)ca.reject(r);else if(da(n,w,ca)){ca.fU();var ea=new XMLHttpRequest;n=Object(ja.a)(h.Zf+"../"+encodeURI(n.url));ea.open("GET",n);ea.withCredentials=this.No();ea.addEventListener("load",function(){ea.readyState===ea.DONE&&200===ea.status&&(ca.result=JSON.parse(ea.response),ca.resolve(ca.result))});ea.addEventListener("error",function(e){ca.reject(ea.statusText+
" "+JSON.stringify(e))});ea.send()}break;case "progress":"loading"===n.t&&h.trigger(e.a.Events.DOCUMENT_LOADING_PROGRESS,[n.bytes,n.total])}},sP:function(){this.OG(this.fm);return this.fm.promise},X4:function(){this.lg||(this.lg=z(),this.lg.request={t:"annots"},this.wf(this.lg.request));return this.lg.promise},zB:function(e){this.Uf[e]||(this.Uf[e]=z(),this.Uf[e].request={t:"image",p:e},this.wf(this.Uf[e].request));return this.Uf[e].promise},uba:function(e){this.nh[e]||(this.nh[e]=z(),this.nh[e].request=
{t:"text",p:e},this.wf(this.nh[e].request));return this.nh[e].promise},vba:function(e,f,h,n){var r=this.Bf.length;this.sz.length&&(r=this.sz.pop());this.Bf[r]=z();this.Bf[r].request={t:"tiles",p:e,z:f,r:h,size:n,rID:r};this.wf(this.Bf[r].request);return this.Bf[r].promise},AT:function(){this.af||(this.af=z(),this.af.request={t:"pdf"},this.SE?this.af.resolve({url:this.$E}):this.wf(this.af.request));return this.af.promise},JP:function(e){var f=this,h=new XMLHttpRequest,n=Object(ja.a)(this.Zf+"aul")+
"&id="+this.id,r=new FormData,w={};e.annots&&(w.annots="xfdf");e.watermark&&(w.watermark="png");e.redactions&&(w.redactions="redact");w={t:"docmod",reqID:this.r9++,parts:w};e.print&&(w.print=!0);var x=this.TN(w);r.append("msg",JSON.stringify(x));return Promise.all([e.annots,e.watermark,e.redactions].map(function(e){return Promise.resolve(e)})).then(function(e){var w=e[0],y=e[1],aa=e[2];w&&r.append("annots",w);y&&r.append("watermark",e.watermark);aa&&r.append("redactions",aa);f.HA[x.reqID]=z();h.open("POST",
n);h.withCredentials=f.No;e=new Promise(function(e,f){h.onreadystatechange=function(){4===h.readyState&&(200===h.status?e():f("An error occurred while sending down annotation data to the server"))}});h.send(r);return e.then(function(){return f.HA[x.reqID].promise})})},kQ:function(){this.Vd||(this.Vd=z(),this.Vd.request={t:"xod",noCreate:!0},this.wf(this.Vd.request));return this.Vd.promise},wba:function(){this.pe||(this.pe=z(),this.pe.request={t:"xod"},this.wf(this.pe.request));return this.pe.promise},
Xm:function(){return!0},request:function(){},WS:function(){},abort:function(){for(var e=0;e<this.Bf.length;e++)this.Bf[e]&&(this.Bf[e].resolve(null),this.Bf[e]=null,this.sz.push(e));this.close()},LB:function(e){this.vf=this.vf||{};this.vf.headers=e},Dha:function(){return this.vf?Object(ca.omit)(this.vf.headers,["Cookie","cookie"]):null},os:function(e){this.vf=this.vf||{};this.vf.internal=this.vf.internal||{};this.vf.internal.withCredentials=e},No:function(){return this.vf&&this.vf.internal?this.vf.internal.withCredentials:
null},getFileData:function(){return Promise.reject()}});Object(ha.a)(ba);Object(h.a)(ba);Object(h.b)(ba);ea["default"]=ba},391:function(ha,ea,f){function ba(f,x,w){function e(e,f){function h(e){r().then(function(f){ba&&!ea?setTimeout(function(){h(e)},1):f.send(JSON.stringify(e))})}function n(e,f,n,r){var ia=window.createPromiseCapability(),ja=!1,ka=ia;y=e;z=f;aa=n;x=null;r&&(e=Object(da.a)("wvsQueryParameters"),e.bcid=Object(fa.j)(8),Object(da.b)("wvsQueryParameters",e));try{e=ha?ma+"/"+ha:ma+"/ws";
e=Object(ca.a)(e);var la=new WebSocket(e);la.onopen=function(){ia.resolve();ja=!0;ia=null;ba=!1;w.resolve(la);z&&z()};la.onerror=function(e){ba=ea=!0;ia&&ia.reject(e);x&&x.reject()};la.onclose=function(){w=window.createPromiseCapability();ba=!0;x||(x=window.createPromiseCapability());x.resolve();aa&&aa();y&&ja&&y({t:"health",data:{unhealthy:!0,isDead:!0}})};la.onmessage=function(e){e&&e.data&&(e=JSON.parse(e.data),e.hb?h({hb:!0}):e.end?close():y(e))}}catch(Da){ia.reject(Da),ia=null}return ka.promise}
function r(){ba&&y&&n(y);return w.promise}var w=window.createPromiseCapability(),x=null,y,z,aa=null,ba=!1,ea=!1,ha=f,ma=function(e){var f=e.indexOf("://"),h="ws://";0>f?f=0:(5===f&&(h="wss://"),f+=3);var n=e.lastIndexOf("/");0>n&&(n=e.length);return h+e.slice(f,n)}(e);return{send:h,xJ:n,lF:function(){return x?x.promise:r().then(function(e){x=window.createPromiseCapability();y=null;e.close();return x.promise})}}}function h(e){var f=e.lastIndexOf("/");0>f&&(f=e.length);return e.slice(0,f)}return window.WebSocket&&
!w?e(f,x):function(e,f){function r(e){(da?da.promise:Promise.resolve(ba)).then(function(f){var h=new XMLHttpRequest,n=aa?y+"/"+aa+"pf":y+"/pf";n=Object(ca.a)(n)+"&id="+f;f=new FormData;f.append("data",JSON.stringify(e));h.open("POST",n);h.withCredentials=!0;h.send(f)})}function n(){ba=0;da||(da=window.createPromiseCapability())}function w(){x=new XMLHttpRequest;var e=y+"/pf";e+=0!==ba?"?id="+ba+"&uc="+ma:"?uc="+ma;ma++;x.open("GET",e,!0);x.withCredentials=!0;x.setRequestHeader("Cache-Control","no-cache");
x.setRequestHeader("X-Requested-With","XMLHttpRequest");var f=x,h=!1;x.onreadystatechange=function(){a:if(3<=f.readyState&&!h){try{var e=f.responseText.length}catch(Ca){Object(z.g)("caught exception");break a}if(0<e)try{var x=f.responseText.split("\n");for(x[x.length-1]&&x.pop();0<x.length&&3>x[x.length-1].length;)"]"===x.pop()&&n();0<x.length&&3>x[0].length&&x.shift();for(e=0;e<x.length;++e)x[e].endsWith(",")&&(x[e]=x[e].substr(0,x[e].length-1));0===ba&&0<x.length&&(ba=JSON.parse(x.shift()).id,e=
da,da=null,e.resolve(ba));var y;for(e=0;e<x.length;++e)(y=JSON.parse(x[e]))&&y.end?close():y&&y.hb&&y.id===ba?r({hb:!0}):ha(y)}catch(Ca){}ea||(h=!0,w())}};x.send()}var x,y=h(e),aa=f,ba=0,da=window.createPromiseCapability(),ea=!1,fa=null,ha=null,ma=0;return{send:r,xJ:function(e,f,h){ha=e;fa=h;ea=!1;n();w();f&&f();return Promise.resolve()},lF:function(){n();ha=null;ea=!0;fa&&fa();x.abort();return Promise.resolve()}}}(f,x)}f.d(ea,"a",function(){return ba});var z=f(1),fa=f(31),da=f(39),ca=f(140)}}]);}).call(this || window)
