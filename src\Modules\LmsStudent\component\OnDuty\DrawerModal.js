import React, { useState } from 'react';
import { Table } from 'react-bootstrap';
import {
  Avatar,
  Box,
  Divider,
  Drawer,
  Grid,
  Popover,
  Stack,
  Typography,
} from '@mui/material';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import moment from 'moment';
import { keysForProfileStatus, staffFullNameLms } from './LmsUtils';
import { useDispatch, useSelector } from 'react-redux';
import MButton from 'Widgets/FormElements/material/Button';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import Input from 'Widgets/FormElements/Input/Input';
import CloseIcon from '@mui/icons-material/Close';
import DocumentPopOverDelivery from './DocumentPopOverDelivery';
import { getVersionName, jsUcFirstAll } from 'utils';
import WarningAmberTwoToneIcon from '@mui/icons-material/WarningAmberTwoTone';
import { selectAuthData } from 'ReduxApi/User/selectors';
import {
  getLmsListData,
  getLmsSetting,
  UpdateStatusOnDuty,
} from 'ReduxApi/LmsStudent/action';
import { Capitalize } from 'ReduxApi/util';
import InfoIcon from '@mui/icons-material/Message';
const DrawerModal = ({
  viewDrawer,
  setViewDrawer,
  viewScheduleList,
  currentGroupedDate,
  sessionWiseData,
  type,
}) => {
  const authData = useSelector(selectAuthData);
  const dispatch = useDispatch();
  const student_id = authData.get('_id', '');
  const [popupClick, setPopupClick] = useState(
    Map({ status: false, selectedData: Map() })
  );
  const [reason, setReason] = useState('');
  const handlePopupOpen = (selectedData) => {
    setPopupClick((prev) =>
      prev.set('status', true).set('selectedData', selectedData)
    );
  };

  const handleConfirmPopup = () => {
    const categoryIds = sessionWiseData
      .get(viewDrawer.get('selectedKeys', ''))
      .map((schedule) => {
        return schedule.get('categoryId', '');
      });

    const typeIds = sessionWiseData
      .get(viewDrawer.get('selectedKeys', ''))
      .map((schedule) => {
        return schedule.get('typeId', '');
      });

    const bulkCancelIds = sessionWiseData
      .get(viewDrawer.get('selectedKeys', ''))
      .find(
        (schedule) =>
          schedule.getIn(['scheduleId', '_id'], '') ===
          popupClick.getIn(['selectedData', 'scheduleId'], '')
      );
    const formData = {
      id: student_id,
      reason,
      categoryId: categoryIds,
      approvalStatus: 'Cancelled',
      typeId: typeIds,
      bulkCancelId: [bulkCancelIds.get('_id', '')],
    };

    const callBack = () => {
      setPopupClick((prev) => prev.set('status', false));
      handleDrawerClose();
      dispatch(getLmsListData({ _id: student_id, type, cb }));
    };

    const cb = () => {
      dispatch(getLmsSetting({ type }));
    };
    dispatch(UpdateStatusOnDuty(formData, type, callBack));
  };
  const handleClosePopup = () => {
    setReason('');
    setPopupClick((prev) => prev.set('status', false));
  };
  const startEndDate = `${
    viewDrawer.get('selectedKeys', '')?.split('/')[2]
  } - ${viewDrawer.get('selectedKeys', '')?.split('/')[3]}`;

  const drawerPaperW40 = {
    '& .MuiDrawer-paper': { width: '90vw' },
  };
  const handleDrawerClose = () => {
    setViewDrawer((prev) => prev.set('status', false));
  };

  const getLeaveApplicationStatus = (scheduleId) => {
    const currentStatus = currentGroupedDate.find(
      (currentGroupedDateElement) =>
        currentGroupedDateElement.getIn(['scheduleId', '_id'], '') ===
        scheduleId
    );
    const approvalStatus = currentStatus?.get('approvalStatus', '')
      ? currentStatus?.get('approvalStatus', '')
      : '';
    const reasonStatus = currentStatus?.getIn(['approvalFrom', 0, 'reason'], '')
      ? currentStatus?.getIn(['approvalFrom', 0, 'reason'], '')
      : '';
    const date = currentStatus?.getIn(['approvalFrom', 0, 'date'], '')
      ? currentStatus?.getIn(['approvalFrom', 0, 'date'], '')
      : '';
    return {
      status: Capitalize(
        approvalStatus === 'Cancelled' ||
          approvalStatus === 'Approved' ||
          approvalStatus === 'Rejected'
          ? approvalStatus
          : 'pending'
      ),
      reason: reasonStatus,
      date,
    };
  };

  return (
    <>
      {' '}
      <Drawer
        sx={drawerPaperW40}
        anchor="right"
        open={viewDrawer.get('status', false)}
        onClose={() => {}}
      >
        <div className="drawer-close-title-color ">
          <div className="d-flex">
            <div>
              <CloseIcon
                className="lms-apply-closeIcon"
                onClick={() => handleDrawerClose()}
              />
            </div>
            <div className="m-3 f-22 mt-1">Schedule List</div>
          </div>
        </div>
        <div className="digi-scheduled-sessionWise mx-2 mt-3">
          <div className="Sch-table-body col-md-12 col-lg-12 mt-1 p-0">
            <Table hover className="dashboard-table">
              <thead style={{ backgroundColor: '#f0f0f0' }}>
                <tr className="digi-scheduled-tbl digi-scheduled-tbl-apply-leave text-center bold">
                  <th className="pb-3 text-nowrap pt-3">S No.</th>
                  <th className="pb-3 pt-3 ">Course</th>
                  <th className="pb-3 pt-3">
                    <span>
                      Delivery Type
                      <br />& Topic
                    </span>
                  </th>
                  {/* <th className="pb-3 pt-3">Subject</th> */}
                  <th className="pb-3 pt-3">Staff Name</th>
                  <th className="pb-3 pt-3">Scheduled Info</th>
                  <th className="pb-3 pt-3">Approval Status</th>
                  <th className="pb-3 pt-3">Cancel</th>
                </tr>
              </thead>
              {viewScheduleList.size ? (
                <tbody>
                  {viewScheduleList.entrySeq().map(([key, schedule]) => {
                    return (
                      <>
                        <tr className="bold">
                          <td colSpan={12}>
                            <div className="pl-2 pt-2 dateFilterCss f-17">
                              {moment(key).format('DD MMM YYYY')}
                            </div>
                          </td>
                        </tr>
                        {schedule.map((student, sessionIndex) => {
                          const courseDetails = `${student.getIn(
                            ['schedules', 'course_name'],
                            ''
                          )} - ${student.getIn(
                            ['schedules', 'course_code'],
                            ''
                          )}`;
                          const leaveStatus = getLeaveApplicationStatus(
                            student.get('scheduleId', '')
                          );
                          return (
                            <tr
                              className="table_border digi-border-top digi-border-bottom text-center"
                              id="set-width-span"
                              key={sessionIndex}
                            >
                              <td className="pl-3">{sessionIndex + 1}</td>
                              <td className="p-1">
                                {student.getIn(
                                  ['schedules', 'schedules'],
                                  List()
                                ).size ? (
                                  <>
                                    <span className="text-wrap">
                                      {courseDetails}
                                      {getVersionName(schedule)}
                                    </span>

                                    <div className="d-flex align-items-center justify-content-center">
                                      {student.getIn(
                                        ['schedules', 'warning'],
                                        ''
                                      ) && (
                                        <>
                                          <WarningAmberTwoToneIcon
                                            style={{
                                              fill: student.getIn(
                                                ['schedules', 'colorCode'],
                                                ''
                                              ),
                                              verticalAlign: 'middle',
                                            }}
                                          />{' '}
                                          <div
                                            style={{
                                              color: student.getIn(
                                                ['schedules', 'colorCode'],
                                                ''
                                              ),
                                              fontSize: '17px',
                                            }}
                                            className="pl-2"
                                          >
                                            {jsUcFirstAll(
                                              student.getIn(
                                                ['schedules', 'warning'],
                                                ''
                                              )
                                            )}{' '}
                                            (Current Warning)
                                          </div>
                                        </>
                                      )}
                                    </div>
                                  </>
                                ) : (
                                  ''
                                )}
                              </td>
                              <DocumentPopOverDelivery schedule={student} />
                              <td className="pl-3 text-nowrap">
                                {student
                                  .get('staff_name', List())
                                  .map((staff, index) => (
                                    <span key={index}>
                                      <span>
                                        {staffFullNameLms(staff, true)}
                                      </span>{' '}
                                      <br />
                                    </span>
                                  ))}
                              </td>
                              <td className="text-nowrap">
                                {moment(
                                  student.get('scheduleStartDateAndTime', '')
                                ).format('hh:mm A')}{' '}
                                -{' '}
                                {moment(
                                  student.get('scheduleEndDateAndTime', '')
                                ).format('hh:mm A')}
                              </td>
                              <td
                                className={`${
                                  keysForProfileStatus[
                                    getLeaveApplicationStatus(
                                      student.get('scheduleId', '')
                                    )?.status
                                  ]?.color
                                } `}
                              >
                                <div className="d-flex align-items-center justify-content-center">
                                  <div>{leaveStatus?.status}</div>

                                  {leaveStatus?.reason &&
                                    leaveStatus?.status === 'Rejected' && (
                                      <div className="ml-1 cursor-pointer">
                                        <ApprovalStatus
                                          leaveStatus={leaveStatus}
                                          StuDetails={student}
                                        />
                                      </div>
                                    )}
                                </div>
                              </td>
                              {getLeaveApplicationStatus(
                                student.get('scheduleId', '')
                              )?.status === 'Pending' && (
                                <td>
                                  <MButton
                                    clicked={() => handlePopupOpen(student)}
                                    variant={'contained'}
                                    color={'red'}
                                  >
                                    Cancel Request
                                  </MButton>
                                </td>
                              )}
                            </tr>
                          );
                        })}
                      </>
                    );
                  })}
                </tbody>
              ) : (
                <tbody>
                  <tr>
                    <td colSpan={4}>No Sessions Found...</td>
                  </tr>
                </tbody>
              )}
            </Table>
          </div>
        </div>
      </Drawer>
      <DialogModal
        maxWidth="md"
        fullWidth="true"
        aria-labelledby="customized-dialog-title"
        show={popupClick.get('status', false)}
        onClose={() => {}}
      >
        <Box>
          <Typography variant="subtitle2" className=" digi-text-upper">
            <b>
              <div className="p-3"> Leave Cancellation Confirmation</div>
            </b>
            <Divider flexItem />
          </Typography>
          <div>
            <Grid container spacing={2} sx={{ py: 1 }}>
              <Grid item xs={3}>
                <Typography
                  className="text-body"
                  variant="subtitle1"
                  sx={{ ml: 4 }}
                >
                  Date
                </Typography>
              </Grid>
              <Grid item xs={7}>
                <Typography variant="subtitle1">
                  <b> {startEndDate}</b>
                </Typography>
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ py: 1 }}>
              <Grid item xs={3}>
                <Typography
                  className="text-body"
                  variant="subtitle1"
                  sx={{ ml: 4 }}
                >
                  Leave Type
                </Typography>
              </Grid>
              <Grid item xs={7}>
                <Typography variant="subtitle1">
                  <b> {viewDrawer.get('selectedKeys', '').split('/')[1]} </b>
                </Typography>
              </Grid>
            </Grid>
            {/* <Grid container spacing={2} sx={{ py: 1 }}>
                  <Grid item xs={3}>
                    <Typography
                      className="text-body"
                      variant="subtitle1"
                      sx={{ ml: 4 }}
                    >
                      Approval Status
                    </Typography>
                  </Grid>
                  <Grid item xs={7}>
                    <Typography variant="subtitle1">
                      <b> Pending</b>
                    </Typography>
                  </Grid>
                </Grid> */}
          </div>
          <Divider variant="middle" />
          <>
            <Typography className="text-body mb-2 bold" sx={{ pt: 2, pl: 2 }}>
              Reason for Cancelling the Leave
            </Typography>
            <Box sx={{ mx: 2, mb: 2 }} className="mb-4">
              <Input
                elementType={'textarea'}
                elementConfig={{
                  type: 'text',
                }}
                changed={(e) => setReason(e.target.value)}
                placeholder="Reason"
                className={'mt--22 textArea digi-lms-place'}
                value={reason}
              />
            </Box>
            <Divider flexItem className="p-2" />
          </>
          <Stack sx={{ mt: 0, p: 2 }} direction="row" justifyContent="flex-end">
            <MButton
              variant={'contained'}
              color={'PopupCancelButton'}
              className="digi-dontcancel-button"
              clicked={handleClosePopup}
            >
              {`Don't Cancel`}
            </MButton>

            <MButton
              variant={'contained'}
              color={'dutyButton'}
              className="font-weight-normal"
              disabled={!reason.trim().length}
              clicked={handleConfirmPopup}
            >
              Cancel the request
            </MButton>
          </Stack>
        </Box>
      </DialogModal>
    </>
  );
};

export default DrawerModal;
DrawerModal.propTypes = {
  viewDrawer: PropTypes.bool,
  setViewDrawer: PropTypes.func,
  viewScheduleList: PropTypes.instanceOf(Map),
  tab: PropTypes.number,
  currentGroupedDate: PropTypes.instanceOf(List),
  sessionWiseData: PropTypes.instanceOf(Map),
  type: PropTypes.string,
};
export const ApprovalStatus = ({ leaveStatus, StuDetails }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const opened = Boolean(anchorEl);
  const id = opened ? 'simple-popover' : undefined;
  return (
    <>
      <div>
        <div className=" cursor-pointer">
          <InfoIcon fontSize="small" onClick={handleClick} />
        </div>
        <Popover
          id={id}
          open={opened}
          anchorEl={anchorEl}
          onClose={handleClose}
          elevation={1}
        >
          <div className="p-3" style={{ width: '500px', overflowY: 'auto' }}>
            <div className="d-flex justify-content-between">
              <div className="bold">Comments</div>
              <CloseIcon
                className="lms-apply-closeIcon-minSize"
                onClick={() => handleClose()}
              />
            </div>
            <Divider className="my-2" />
            <div className="mt-1 d-flex">
              <Avatar sx={{ bgcolor: '#EFF9FB', width: 48, height: 51 }}>
                <div style={{ color: '#147AFC' }} className="f-14">
                  {Capitalize(
                    StuDetails.getIn(['staff_name', 0, 'first'], '').charAt(0)
                  )}
                </div>
              </Avatar>
              <div className="w-100 ml-2">
                <div className="d-flex justify-content-between">
                  <div className="bold">Faculty Feedback</div>
                  <div>
                    {moment(leaveStatus.date).format('DD/MM/YYYY | hh:mm A')}
                  </div>
                </div>
                <div>
                  {Capitalize(StuDetails.getIn(['staff_name', 0, 'first'], ''))}{' '}
                  {Capitalize(StuDetails.getIn(['staff_name', 0, 'last'], ''))}
                </div>
                <div className="mt-2">{leaveStatus.reason}</div>
              </div>
            </div>
          </div>
        </Popover>
      </div>
    </>
  );
};
ApprovalStatus.propTypes = {
  leaveStatus: PropTypes.object,
  StuDetails: PropTypes.instanceOf(Map),
};
