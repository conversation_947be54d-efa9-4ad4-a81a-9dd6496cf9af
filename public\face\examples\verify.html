<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Face Photo Capture with MediaPipe</title>
		<link
			rel="stylesheet"
			href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
		/>

		<style>
			#camera {
				width: 100%;
				max-width: 500px;
				margin: auto;
			}
			.preview-box {
				width: 100px;
				height: 100px;
				margin: 5px;
				overflow: hidden;
				border: 2px solid #ddd;
			}
			.preview-box img {
				width: 100%;
				/* height: auto; */
				height: 100px;
			}
			.oval-image {
				position: absolute;
			}
		</style>
		<script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
		<script src="./js/face-api.js"></script>
	</head>
	<body>
		<div class="container text-center mt-5">
			<h2>Face Verification Tool</h2>

			<div id="camera-container" class="mb-3">
				<img src="./images/oval.png" alt="Oval shape" class="oval-image" />
				<video id="camera" autoplay></video>
			</div>

			<button class="btn btn-primary" onclick="captureImage()">Capture</button>
			<p id="resultMessage" class="mt-2"></p>
		</div>

		<script type="module">
			import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
			const { FaceLandmarker, FilesetResolver, DrawingUtils } = vision;
			let uploadedDescriptors = [];
			let faceLandmarker;
			let video = document.getElementById('camera');

			// Initialize MediaPipe FaceLandmarker
			async function initializeFaceLandmarker() {
				const filesetResolver = await FilesetResolver.forVisionTasks(
					'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
				);
				faceLandmarker = await FaceLandmarker.createFromOptions(
					filesetResolver,
					{
						baseOptions: {
							modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
						},
						runningMode: 'video',
						numFaces: 1,
					}
				);
			}

			// Start video stream
			async function startVideo() {
				const stream = await navigator.mediaDevices.getUserMedia({
					video: true,
				});
				video.srcObject = stream;
			}
			// Capture and process the image

			async function capturePosition() {
				const canvas = document.createElement('canvas');
				canvas.width = video.videoWidth;
				canvas.height = video.videoHeight;
				const context = canvas.getContext('2d');
				context.filter = 'brightness(1.2) contrast(1.2)';
				context.drawImage(video, 0, 0, canvas.width, canvas.height);

				// Run face landmark detection
				const detections = await faceLandmarker.detectForVideo(
					video,
					performance.now()
				);
				if (detections && detections.faceLandmarks.length > 0) {
					const landmarks = detections.faceLandmarks[0]; // Get landmarks for the first detected face

					// Calculate bounding box from landmarks
					const xs = landmarks.map((point) => point.x * canvas.width);
					const ys = landmarks.map((point) => point.y * canvas.height);
					let minX = Math.min(...xs);
					let minY = Math.min(...ys);
					let maxX = Math.max(...xs);
					let maxY = Math.max(...ys);
					let faceWidth = maxX - minX;
					let faceHeight = maxY - minY;

					// Define padding as a percentage of face dimensions
					const padding = 0.4; // 20% padding around the face
					minX -= faceWidth * padding;
					minY -= faceHeight * padding;
					faceWidth += faceWidth * padding * 2;
					faceHeight += faceHeight * padding * 2;

					// Ensure boundaries are within the image
					minX = Math.max(0, minX);
					minY = Math.max(0, minY);
					faceWidth = Math.min(canvas.width - minX, faceWidth);
					faceHeight = Math.min(canvas.height - minY, faceHeight);

					// Create a cropped canvas for the face
					const croppedCanvas = document.createElement('canvas');
					croppedCanvas.width = faceWidth;
					croppedCanvas.height = faceHeight;
					croppedCanvas
						.getContext('2d')
						.drawImage(
							canvas,
							minX,
							minY,
							faceWidth,
							faceHeight,
							0,
							0,
							faceWidth,
							faceHeight
						);

					// Convert cropped canvas to an image for detection
					const croppedImage = new Image();
					croppedImage.src = croppedCanvas.toDataURL();
					await croppedImage.decode(); // Ensure the image is fully loaded

					// Detect face descriptor from the cropped image
					const detectionsFace = await faceapi
						.detectSingleFace(
							croppedImage,
							new faceapi.SsdMobilenetv1Options({ minConfidence: 0.5 })
							// new faceapi.TinyFaceDetectorOptions()
						)
						.withFaceLandmarks()
						.withFaceDescriptor();
					console.log(detectionsFace);
					if (detectionsFace) {
						console.log('Face descriptor:', detectionsFace.descriptor);
						const capturedDescriptor = detectionsFace.descriptor;
						const matchFound = compareDescriptors(capturedDescriptor);
						console.log(matchFound);

						if (matchFound) {
							document.getElementById('resultMessage').textContent =
								'Success! Match found.';
						} else {
							document.getElementById('resultMessage').textContent =
								'No match found. Try again.';
							await countdownAndCapture();
						}
					} else {
						document.getElementById('resultMessage').textContent =
							'No face detected in cropped image.';
						await countdownAndCapture();
					}
				} else {
					document.getElementById('resultMessage').textContent =
						'Face not detected, please adjust your position!';
					await countdownAndCapture();
				}
			}

			async function countdownAndCapture() {
				let countdown = 3;
				const countdownInterval = setInterval(() => {
					if (countdown > 0) {
						document.getElementById(
							'resultMessage'
						).innerText = `Capturing in ${countdown}...`;
						countdown--;
					} else {
						clearInterval(countdownInterval);
						capturePosition();
					}
				}, 1000);
			}

			async function captureImage() {
				await countdownAndCapture();
			}

			function compareDescriptors(capturedDescriptor) {
				let threshold = 0.47; // Define a threshold for matching
				let matchCount = 0;
				console.log({ uploadedDescriptors });
				uploadedDescriptors.forEach((descriptor) => {
					const distance = faceapi.euclideanDistance(
						capturedDescriptor,
						descriptor
					);
					if (distance < threshold) {
						matchCount++;
					}
				});

				const averageDistance = matchCount / uploadedDescriptors.length;
				console.log({ averageDistance, matchCount });
				return averageDistance >= 1 || matchCount >= 2;
			}

			// Initialize everything when the page loads
			window.onload = async () => {
				await initializeFaceLandmarker();
				await startVideo();
				console.log('FaceLandmarker initialized and video started');
			};
			window.captureImage = captureImage;

			const daAuthAppUrl =
				'https://auth-staging.gcp.digivalitsolutions.com/api/v0/auth/facial-labeled-descriptors-v2?employeeOrAcademicId=';

			function loadSingleDescfromDb(acadamicId) {
				console.log('load from db called');
				$.ajax({
					url: daAuthAppUrl + acadamicId,
					type: 'GET',
					beforeSend: function (request) {
						request.setRequestHeader('api_key', 'apikey123');
					},
					dataType: 'json', // added data type
					success: function (res) {
						uploadedDescriptors = res.data.flatMap((item) => item.descriptors);
					},
					error: function (err) {
						console.log('error occured load from db', err);
					},
				});
			}
			console.log({ uploadedDescriptors });
			async function loadLibraries() {
				const MODEL_URL = 'models';
				await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
				await faceapi.loadFaceLandmarkModel(MODEL_URL);
				await faceapi.loadFaceRecognitionModel(MODEL_URL);
				// await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
				console.log('load facial Libraries done');
			}

			$(document).ready(async function () {
				console.log('webcam is ready');
				await loadLibraries();
				loadSingleDescfromDb('D0033A');
				// trainAndVerify();
			});
		</script>
	</body>
</html>
