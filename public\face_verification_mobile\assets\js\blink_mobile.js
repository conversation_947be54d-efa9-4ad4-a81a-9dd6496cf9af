/* sample iframe widget load*/
//http://localhost/oracle_facial_offline_poc/liveness_mobile/mobile.html?appname=DC&type=student&employeeOrAcademicId=10000002&platform=android&debug=false&faceurl=https://ecs-auth-staging.digivalitsolutions.com/api/v0/auth/facial-labeled-descriptors?employeeOrAcademicId=

const urlSearchParams = new URLSearchParams(window.location.search);
const params = Object.fromEntries(urlSearchParams.entries());
var isWidgetLoaded = false;
var shouldLoadFromDb = false;
console.log('params', params);
var digiapikey = params.digiapikey;
if (Object.keys(params).length < 4) {
  alert('params not supplied');
}
platform = params.platform;
let bodyClassName = 'digi-ios-font';
platform == 'android' ? (bodyClassName = 'digi-android-font') : null;

const daAuthAppUrl = params.faceurl;

$('.cam1').hide();

$('body').addClass(bodyClassName);

if (params.debug == 'true') {
  $('.debug').show();
}

$('.iclose2,.cancel-track').on('click', function () {
  takeToinitialState();
  sendMesssageToPlatform('close1clicked');
});

$('.iclose').on('click', function () {
  $('.digi-tooltip-container').removeClass('digi-active');
});

$('.info-tooltip').on('click', function () {
  $('.digi-tooltip-container').toggleClass('digi-active');
});

$('.digi-fail-button').on('click', function () {
  if (platform == 'android' || platform == 'ios') {
    sendMesssageToPlatform('tryagain');
  } else {
    $('.cam1').show();
    $('.cam2').hide();
  }
  takeToinitialState();
});

$('.cam2').on('click', function () {
  $(this).hide();
  $('.cam1').show();
});

$('.digi-info-icon').on('click', function (event) {
  event.stopPropagation();
  $('.digi-tooltip-container,.digi-close-icon-container').toggleClass(
    'digi-active'
  );
});

async function setSourceAndHandle(picToVerify) {
  $('#snapshotplaceholder').attr('src', picToVerify);
  $('.digi-profile-picture').attr('src', picToVerify);
  //   console.log('result', result);
  let verifyresult = await trainAndVerify(true);
  if (verifyresult) {
    handleAuthenticationSuccess();
  } else {
    handleAuthenticationFailiure();
  }
  stopcamera();
}

$('.digi-home-icon').on('click', async function () {
  if ($(this).hasClass('disabled')) {
    return false;
  }
  $(this).addClass('disabled');
  var picToVerify = capture();
  setTimeout(async function () {
    setSourceAndHandle(picToVerify);
  }, 800);
});

function sendMesssageToPlatform(message) {
  console.log('message to platform', message);
  $('.digi-home-icon').removeClass('disabled');
  $('.loader').hide();
  if (params.debug == 'true') {
    alert(platform + message);
    console.log(platform, 'message sent', message);
  }
  //platform = params.platform;
  if (platform == 'ios')
    window.webkit.messageHandlers.Mobile.postMessage(message);
  if (platform == 'android') {
    Mobile.showMessageFromWeb(message);
  }
  if (platform == 'web') window.parent.postMessage(message, '*');
}

function handleAuthenticationSuccess() {
  shouldLoadFromDb = false;
  $('.digi-camera-page').removeClass('digi-active');
  $('.digi-verification-success-page').addClass('digi-active');
  console.log('handle succcses sent');
  setTimeout(function () {
    $('.loader').hide();
    sendMesssageToPlatform('facesuccess');
  }, 1200);
  setTimeout(function () {
    takeToinitialState();
  }, 5000);
}

function handleAuthenticationFailiure() {
  shouldLoadFromDb = true;
  sendMesssageToPlatform('facefailed');
  $('.steps').removeClass('digi-active');
  $('.step5').addClass('digi-active');
}

function dString(name) {
  if (name !== '') {
    return window.atob(name);
  }
  return '';
}

function dataURLtoFile(dataurl, filename) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = dString(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

async function trainAndVerify(isRealimage) {
  /*to load desciptior from db once failed*/
  if (shouldLoadFromDb) {
    loadSingleDescfromDb(params.employeeOrAcademicId);
  }

  $('.loader').show();
  const useTinyModel = true;
  const imgUrl2 = $('#snapshotplaceholder').attr('src');
  const queryImage1 = await faceapi.fetchImage(imgUrl2);
  //console.log('query image', queryImage1);
  const singleResult = await faceapi
    .detectSingleFace(
      queryImage1,
      new faceapi.TinyFaceDetectorOptions({ inputSize: 320 })
    )
    .withFaceLandmarks(useTinyModel)
    .withFaceDescriptor();

  $('.init-hide').removeClass('init-hide');
  if (typeof totaldescriptions === 'undefined') {
    console.log('total description undefined');
    //takeToinitialState();
    shouldLoadFromDb = true;
    return false;
  }
  if (isRealimage) {
    console.log('checking real image');
    var labeledFaceDescriptors = totaldescriptions.descriptors.map((x) =>
      faceapi.LabeledFaceDescriptors.fromJSON(totaldescriptions)
    );

    if (singleResult) {
      const faceMatcher2 = new faceapi.FaceMatcher(labeledFaceDescriptors);
      const bestMatch = faceMatcher2.findBestMatch(singleResult.descriptor);
      console.log(
        'best match ',
        bestMatch.distance,
        'bestmatch',
        bestMatch.distance < 0.463
      );
      //  $('.window-message').append('--- face match ratio ' + bestMatch.distance);

      return bestMatch.distance < 0.463;
    }
  } else {
    console.log('dummy image trained ');
  }
}

$(document).ready(async function () {
  console.log('webcam is ready');

  await loadLibraries();
  loadSingleDescfromDb(params.employeeOrAcademicId);
  // trainAndVerify();
  $('.loader').hide();
  $('.init-hide').removeClass('init-hide');

  if (
    typeof navigator === 'undefined' ||
    typeof navigator.userAgent === 'undefined'
  ) {
    // Handle the exception here
    alert('unable to detect navigation');
  } else {
    $('.window-message').append(navigator.userAgent);
  }

  /*todo add asymetric authentication before prod to fetch*/
});

function loadSingleDescfromDb(acadamicId) {
  console.log('load from db called', acadamicId);
  $.ajax({
    url: daAuthAppUrl + acadamicId,
    type: 'GET',
    dataType: 'json', // added data type
    headers: {
      Digiapikey: digiapikey,
    },
    success: function (res) {
      totaldescriptions = res.data;
      console.log('db data added to total descriptor success');
      shouldLoadFromDb = false;
    },
    error: function (err) {
      //alert(JSON.stringify(err));
      shouldLoadFromDb = true;
      console.log('error occured load from db', err);
      $('.loader').hide();
      takeToinitialState();
    },
  });

  isWidgetLoaded = true;
  sendMesssageToPlatform('widget-loaded');
}

async function loadLibraries() {
  const MODEL_URL = 'assets/weights';
  await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
  await faceapi.loadFaceLandmarkTinyModel(MODEL_URL);
  await faceapi.loadFaceRecognitionModel(MODEL_URL);
  console.log('load facial Libraries done');
}
function onBackPressed() {
  console.log('on back pressed');
  //close camera functionality
  takeToinitialState();
}

function takeToinitialState() {
  stopcamera();
  $('#snapshotplaceholder').attr('src', '');
  $('.digi-profile-picture').attr('src', '');
  // $('.steps,.digi-verification-success-page').removeClass('digi-active');
  // $('.step1').addClass('digi-active');
  $('.steps').removeClass('digi-active');
  $('.digi-camera-page').addClass('digi-active');
  $('.step1').addClass('digi-active');
  $('.cam1').hide();
  $('.cam2').show();
}

/*
 * common widget - in mobile verify only base 64 image send from parent of iframe
 */
//('use strict');

// Put variables in global scope to make them available to the browser console.
const constraints = (window.constraints = {
  audio: false,
  video: true,
});
const video = document.querySelector('video');
function handleSuccess(stream) {
  const videoTracks = stream.getVideoTracks();
  //console.log('Got stream with constraints:', constraints);
  //console.log(`Using video device: ${videoTracks[0].label}`);
  //window.stream = stream; // make variable available to browser console
  video.srcObject = stream;
}

function handleError(error) {
  if (error.name === 'OverconstrainedError') {
    const v = constraints.video;
    errorMsg(
      `The resolution ${v.width.exact}x${v.height.exact} px is not supported by your device.`
    );
  } else if (error.name === 'NotAllowedError') {
    errorMsg(
      'Permissions have not been granted to use your camera and ' +
        'microphone, you need to allow the page access to your devices in ' +
        'order for the demo to work.'
    );
  }
  errorMsg(`getUserMedia error: ${error.name}`, error);
}

function errorMsg(msg, error) {
  const errorElement = document.querySelector('#errorMsg');
  errorElement.innerHTML += `<p>${msg}</p>`;
  if (typeof error !== 'undefined') {
    console.error(error);
  }
}

async function init(e) {
  console.log('initcalled');
  try {
    stream = await navigator.mediaDevices.getUserMedia(constraints);
    handleSuccess(stream);
    // e.target.disabled = true;
  } catch (e) {
    handleError(e);
  }
}

document.querySelector('#cam2').addEventListener('click', (e) => init(e));

var scaleFactor = 0.25;
function capture(scaleFactor) {
  if (scaleFactor == null) {
    scaleFactor = 1;
  }
  var video = document.getElementById('webcam');
  var w = video.videoWidth * scaleFactor;
  var h = video.videoHeight * scaleFactor;
  var canvas = document.createElement('canvas');
  canvas.width = w;
  canvas.height = h;
  var ctx = canvas.getContext('2d');
  ctx.drawImage(video, 0, 0, w, h);
  let canvasData = canvas.toDataURL();
  return canvasData;
}

//stop video after snap
function stopcamera() {
  if (typeof stream !== 'undefined') {
    stream.getTracks().forEach(function (track) {
      if (track.kind === 'video') {
        track.stop();
      }
    });
  }
}

function validateByString(imgData) {
  if (!isWidgetLoaded) {
    sendMesssageToPlatform('facefailed');
    return false;
  }

  if (typeof imgData === 'undefined') {
    alert('no image passed');
    return false;
  }
  setSourceAndHandle(imgData);
}
