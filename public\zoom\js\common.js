function _0x452a(_0x1a1c01, _0x3cee59) {
  const _0x13ecda = _0x13ec();
  return (
    (_0x452a = function (_0x452af5, _0x1f32a6) {
      _0x452af5 = _0x452af5 - 0xe9;
      let _0x31c3c0 = _0x13ecda[_0x452af5];
      return _0x31c3c0;
    }),
    _0x452a(_0x1a1c01, _0x3cee59)
  );
}
function _0x13ec() {
  const _0x3d9808 = [
    '48gbLSDM',
    '11WCZjqv',
    'location',
    '244191cooRzu',
    '22115cOLJfc',
    'href',
    'getItem',
    '_lib',
    '/zoom/leave.html',
    'addEventListener',
    '16QALSIH',
    '7655DUiqtx',
    '1211733jzSuvz',
    '75425ZPIyfs',
    '3367450jtCwSS',
    'user_Id',
    '180ziKooT',
    '561894BQECdi',
    'parseQuery',
    'DOMContentLoaded',
    'contextmenu',
  ];
  _0x13ec = function () {
    return _0x3d9808;
  };
  return _0x13ec();
}
const _0x9fef64 = _0x452a;
(function (_0x4766ac, _0x5a8bf8) {
  const _0x1f5960 = _0x452a,
    _0x7c6e4c = _0x4766ac();
  while (!![]) {
    try {
      const _0x2e4526 =
        (-parseInt(_0x1f5960(0xec)) / 0x1) * (parseInt(_0x1f5960(0xf2)) / 0x2) +
        -parseInt(_0x1f5960(0xeb)) / 0x3 +
        (parseInt(_0x1f5960(0xf8)) / 0x4) * (-parseInt(_0x1f5960(0xf3)) / 0x5) +
        -parseInt(_0x1f5960(0xf9)) / 0x6 +
        (parseInt(_0x1f5960(0xf5)) / 0x7) * (parseInt(_0x1f5960(0xfd)) / 0x8) +
        parseInt(_0x1f5960(0xf4)) / 0x9 +
        (-parseInt(_0x1f5960(0xf6)) / 0xa) * (-parseInt(_0x1f5960(0xe9)) / 0xb);
      if (_0x2e4526 === _0x5a8bf8) break;
      else _0x7c6e4c['push'](_0x7c6e4c['shift']());
    } catch (_0x3df2d1) {
      _0x7c6e4c['push'](_0x7c6e4c['shift']());
    }
  }
})(_0x13ec, 0x1c1e3),
  window[_0x9fef64(0xf1)](_0x9fef64(0xfb), function (_0x48c94b) {
    const _0x30a0d6 = _0x9fef64;
    var _0x19f3c2 = window['testTool'],
      _0x1507c7 = _0x19f3c2[_0x30a0d6(0xfa)]();
    const _0x5f048e = atob(_0x1507c7?.['uid']),
      _0x572214 = localStorage['getItem'](_0x30a0d6(0xf7));
    _0x5f048e !== _0x572214 && (window[_0x30a0d6(0xea)]['href'] = '/'),
      checkHasSession();
  }),
  window[_0x9fef64(0xf1)](_0x9fef64(0xfc), (_0x22ef52) => {
    _0x22ef52['preventDefault']();
  });
function checkHasSession() {
  setInterval(function () {
    const _0x2fa67e = _0x452a,
      _0x3ed875 = localStorage[_0x2fa67e(0xee)](_0x2fa67e(0xef));
    _0x3ed875 === null &&
      (window[_0x2fa67e(0xea)][_0x2fa67e(0xed)] = _0x2fa67e(0xf0));
  }, 0x7530);
}
