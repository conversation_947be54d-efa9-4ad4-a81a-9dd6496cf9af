document.addEventListener("DOMContentLoaded", function () {
  var loaderPage = document.createElement("div");
  var spinContainer = document.createElement("div");
  var spinner = document.createElement("img");

  loaderPage.id = "loader";
  loaderPage.className = "loader";
  spinContainer.className = "load-spinner-container";
  spinner.className = "load-spinner";
  spinner.setAttribute("src", "./utils/images/ossai.gif");

  spinContainer.append(spinner);
  loaderPage.append(spinContainer);
  document.body.prepend(loaderPage);
});

function showLoader(show) {
  if (show) {
    $("#loader").show();
  } else {
    setTimeout(() => {
      $("#loader").hide();
    }, 800);
  }
}
