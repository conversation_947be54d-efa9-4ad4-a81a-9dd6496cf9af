function _0x4719() {
  var _0x3e527e = [
    'Already\x20you\x20have\x20an\x20opened\x20zoom\x20tab.',
    '_lib_1',
    'enc',
    'parse',
    '401544pLmYxe',
    '1252107JukWTn',
    '494KlxERV',
    '@#MSK0033#@',
    '2506823pXNkQw',
    'addEventListener',
    'result',
    'toString',
    'DOMContentLoaded',
    'stringify',
    'generateSignature',
    '344661BtNcFj',
    '12rQRdZq',
    'serialize',
    '7WdNaqF',
    'close',
    'testTool',
    '/zoom/meeting.html?',
    '6AKenuB',
    'preLoadWasm',
    '2425728hHPkpE',
    '1539470peoVLL',
    '113bUrBOt',
    'open',
    'AES',
    '10kUInVJ',
    'parseQuery',
    'encrypt',
    '8PaoqWM',
    '_self',
    'getItem',
    'isMobileDevice',
  ];
  _0x4719 = function () {
    return _0x3e527e;
  };
  return _0x4719();
}
var _0x3a815a = _0x2d00;
(function (_0x2f7219, _0x3bc17c) {
  var _0x3f551f = _0x2d00,
    _0x163c86 = _0x2f7219();
  while (!![]) {
    try {
      var _0x47d831 =
        (parseInt(_0x3f551f(0xca)) / 0x1) * (parseInt(_0x3f551f(0xb6)) / 0x2) +
        (-parseInt(_0x3f551f(0xbf)) / 0x3) * (parseInt(_0x3f551f(0xd0)) / 0x4) +
        (parseInt(_0x3f551f(0xc9)) / 0x5) * (parseInt(_0x3f551f(0xc6)) / 0x6) +
        (-parseInt(_0x3f551f(0xc2)) / 0x7) * (parseInt(_0x3f551f(0xc8)) / 0x8) +
        (-parseInt(_0x3f551f(0xb5)) / 0x9) *
          (-parseInt(_0x3f551f(0xcd)) / 0xa) +
        (-parseInt(_0x3f551f(0xb8)) / 0xb) *
          (-parseInt(_0x3f551f(0xc0)) / 0xc) +
        parseInt(_0x3f551f(0xb4)) / 0xd;
      if (_0x47d831 === _0x3bc17c) break;
      else _0x163c86['push'](_0x163c86['shift']());
    } catch (_0x472502) {
      _0x163c86['push'](_0x163c86['shift']());
    }
  }
})(_0x4719, 0x3100f),
  window[_0x3a815a(0xb9)](_0x3a815a(0xbc), function (_0x33f2e7) {
    var _0x1040fc = _0x3a815a;
    const _0x23eacd = window['localStorage'][_0x1040fc(0xae)]('loggedIn');
    _0x23eacd === null
      ? webSDKReady()
      : (alert(_0x1040fc(0xb0)), window[_0x1040fc(0xc3)]());
  });
function _0x2d00(_0x31191b, _0x99179b) {
  var _0x4719dd = _0x4719();
  return (
    (_0x2d00 = function (_0x2d0026, _0x229746) {
      _0x2d0026 = _0x2d0026 - 0xae;
      var _0xcde7f = _0x4719dd[_0x2d0026];
      return _0xcde7f;
    }),
    _0x2d00(_0x31191b, _0x99179b)
  );
}
function webSDKReady() {
  var _0x334bbf = _0x3a815a,
    _0x49c9cb = window[_0x334bbf(0xc4)];
  _0x49c9cb[_0x334bbf(0xaf)]() && (vConsole = new VConsole());
  ZoomMtg[_0x334bbf(0xc7)]();
  const _0x562678 = localStorage[_0x334bbf(0xae)]('_lib');
  if (_0x562678 !== null) {
    var _0x598ca6 = CryptoJS[_0x334bbf(0xcc)]['decrypt'](
        _0x562678['toString'](),
        _0x334bbf(0xb7)
      ),
      _0x15eb94 = JSON[_0x334bbf(0xb3)](
        _0x598ca6[_0x334bbf(0xbb)](CryptoJS[_0x334bbf(0xb2)]['Utf8'])
      ),
      _0xadc8b8 = _0x49c9cb[_0x334bbf(0xce)](),
      _0x46302e = _0x15eb94[0x3]['d'],
      _0x241cd2 = _0x15eb94[0x4]['e'];
    ZoomMtg[_0x334bbf(0xbe)]({
      meetingNumber: _0x15eb94[0x5]['f'],
      apiKey: _0x46302e,
      apiSecret: _0x241cd2,
      role: 0x0,
      success: function (_0x3f0341) {
        var _0x1d368b = _0x334bbf,
          _0x3b2775 = [{ s: _0x3f0341[_0x1d368b(0xba)] }],
          _0x108388 = CryptoJS[_0x1d368b(0xcc)][_0x1d368b(0xcf)](
            JSON[_0x1d368b(0xbd)](_0x3b2775),
            _0x1d368b(0xb7)
          );
        localStorage['setItem'](_0x1d368b(0xb1), _0x108388);
        var _0x7e3e27 = _0x1d368b(0xc5) + _0x49c9cb[_0x1d368b(0xc1)](_0xadc8b8);
        window[_0x1d368b(0xcb)](_0x7e3e27, _0x1d368b(0xd1));
      },
    });
  }
}
