import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { getClassName } from '../../Utils/utils';

const SessionStatusCell = ({ schedule, onSessionClick }) => {
  const { t } = useTranslation();

  if (schedule.get('isActive', '') !== true) {
    return (
      <div
        className={getClassName(
          'pr-3-arabic',
          'pr-3',
          'f-16 text-danger text-end bold icon'
        )}
        onClick={() => onSessionClick(schedule)}
      >
        {t('session_canceled')}
      </div>
    );
  }

  return (
    <div className="pt-2">
      {schedule.get('status', '') === 'completed' && (
        <div className="f-14">
          <span className="f-14 mb-0 text-success bold">{t('complete')}</span>
        </div>
      )}
      {schedule.get('status', '') === 'ongoing' && (
        <div
          className="f-16 text-primary text-end bold icon"
          onClick={() => onSessionClick(schedule)}
        >
          {t('progress')}
        </div>
      )}
    </div>
  );
};

SessionStatusCell.propTypes = {
  schedule: PropTypes.object.isRequired,
  onSessionClick: PropTypes.func.isRequired,
};

export default SessionStatusCell;
