import React from 'react';
import { fromJS, List, Map } from 'immutable';
import * as actions from './action';
import { Trans } from 'react-i18next';

const initialState = fromJS({
  message: '',
  isLoading: false,
  facialType: Map(),
  getFacialType: Map(),
  courseBasedStaffDetails: Map(),
  deliveryBasedStaff: List(),
});
export default function CourseSettings(state = initialState, action) {
  switch (action.type) {
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.SET_BREADCRUMB_SUCCESS: {
      return state.set('breadcrumbs', fromJS(action.breadcrumbs));
    }
    case actions.RESET_BREADCRUMB_SUCCESS: {
      return state.set('breadcrumbs', fromJS(action.breadcrumbs));
    }

    case actions.UPDATE_COURSE_BASED_FACIAL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_COURSE_BASED_FACIAL_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('facialType', fromJS(action.data));
    }
    case actions.UPDATE_COURSE_BASED_FACIAL_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_COURSE_BASED_FACIAL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_COURSE_BASED_FACIAL_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('getFacialType', fromJS(action.data));
    }
    case actions.GET_COURSE_BASED_FACIAL_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_COURSE_BASED_STAFF_DETAILS_REQUEST: {
      return state.set('courseBasedStaffDetails', Map()).set('isLoading', true);
    }
    case actions.GET_COURSE_BASED_STAFF_DETAILS_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('courseBasedStaffDetails', fromJS(action.data));
    }
    case actions.GET_COURSE_BASED_STAFF_DETAILS_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_DELIVERY_BASED_STAFF_REQUEST: {
      return state.set('deliveryBasedStaff', List()).set('isLoading', true);
    }
    case actions.GET_DELIVERY_BASED_STAFF_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('deliveryBasedStaff', fromJS(action.data));
    }
    case actions.GET_DELIVERY_BASED_STAFF_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    default:
      return state;
  }
}
function prepareErrorMessage(action, state) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  const errorMessage =
    message && typeof message === 'string' ? (
      message.toLowerCase()
    ) : (
      <Trans i18nKey={'an_error_occured_try_again'} />
    );
  return state.set('isLoading', false).set('message', errorMessage);
}
