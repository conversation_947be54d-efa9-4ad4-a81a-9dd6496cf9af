import React, { useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import PropTypes from 'prop-types';

import './style.scss';
import LocalStorageService from 'LocalStorageService';

export default function NewWebCam({
  handleFaceClose,
  userId,
  successCallBack,
  errorCallBack,
}) {
  NewWebCam.propTypes = {
    userId: PropTypes.string,
    handleFaceClose: PropTypes.func,
    errorCallBack: PropTypes.func,
    successCallBack: PropTypes.func,
  };

  useEffect(() => {
    window.addEventListener('message', handlePostCameraAuthentication);
    return () => {
      window.removeEventListener('message', handlePostCameraAuthentication);
    };
  }, []); //eslint-disable-line

  const handlePostCameraAuthentication = (e) => {
    const { type, payload } = e.data;
    if (type === 'success') {
      successCallBack(payload);
    } else if (type === 'error') {
      errorCallBack();
    }
  };

  function removeTrailingSlash(url) {
    if (url.endsWith('/')) {
      return url.slice(0, -1);
    }
    return url;
  }

  const token = LocalStorageService.getCustomToken('token');
  const iframeURL = `${
    process.env.REACT_APP_BASE_PATH || ''
  }/face/index1.html?userId=${userId}&_user_id=${userId}&token=${token}&url=${removeTrailingSlash(
    process.env.REACT_APP_API_URL
  )}/user/userBiometricRegister`;

  return (
    <React.Fragment>
      <Modal show={true} dialogClassName="model-1200" centered>
        <div className="container" style={{ padding: 0 }}>
          <div className="d-flex justify-content-between">
            <div className="pt-3 pl-3">
              <p className="f-20 mb-2 ">
                Capture Face (Follow instructions to capture the required face
                positions.)
              </p>
            </div>
            <div className="pt-3">
              <b className="pr-3">
                <Button
                  variant="outline-primary"
                  className="border-radious-8"
                  onClick={() => {
                    handleFaceClose();
                  }}
                >
                  {' '}
                  CANCEL
                </Button>{' '}
              </b>
            </div>
          </div>
        </div>
        <Modal.Body>
          <div style={{ width: '100%', position: 'relative', height: '75vh' }}>
            <iframe
              src={iframeURL}
              title="Face capture"
              style={{ width: '100%', border: 'none', height: '100%' }}
              allow="camera; microphone; fullscreen; autoplay;"
            ></iframe>
          </div>
        </Modal.Body>
      </Modal>
    </React.Fragment>
  );
}
