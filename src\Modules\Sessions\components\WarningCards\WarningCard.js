import React, { useEffect, useState } from 'react';
import MButton from 'Widgets/FormElements/material/Button';
import CreateModal from './CreateModal';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import Checkbox from '@mui/material/Checkbox';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import { Box, Card, CardContent, Chip, Divider } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import { getLmsWarningConfig, getRolesList } from 'ReduxApi/Sessions/action';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import MenuItemDropdown from './MenuItemDropdown';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import { selectRolesList } from 'ReduxApi/Sessions/selectors';
import CourseWiseDeliveryTypes from './CourseWiseDeliveryTypes';

const initialWarnings = fromJS([
  {
    typeName: 'First Warning',
    labelName: 'First Warning',
    percentage: '10',
    colorCode: '#16A34A',
    isActive: false,
    isChecked: false,
    typeIndex: 1,
    isOpen: false,
  },
  {
    typeName: 'Denial',
    labelName: 'Denial',
    percentage: '50',
    colorCode: '#16A34A',
    isActive: false,
    isChecked: false,
    typeIndex: 2,
    isOpen: false,
  },
]);

// Styled Components for Assign Incharge Section
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
  border: '1px solid #f0f0f0',
  transition: 'all 0.3s ease',
  marginTop: theme.spacing(3),
  '&:hover': {
    boxShadow: '0 4px 20px rgba(0,0,0,0.12)',
  },
}));

const inactiveStyle = {
  pointerEvents: 'none',
  opacity: 0.9,
};
const WarningCard = ({
  programId,
  courseId,
  term,
  year,
  level,
  rotation,
  rotationCount,
  institutionCalenderId,
}) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(0);
  const [isEditFow, setIsEditFlow] = useState(false);
  const [activeStep, setActiveStep] = useState(-1);
  const [skipped, setSkipped] = useState(new Set());
  const [warnings, setWarnings] = useState(fromJS(initialWarnings));
  const [warningLists, setWarningLists] = useState(List());
  const overAllRoleList = useSelector(selectRolesList);

  const handleClickOpen = (type, index) => {
    if (type === 'updateNew') {
      setEditIndex(0);
      setIsEditFlow(false);
      setActiveStep(-1);
    }
    if (type === 'edit') {
      setIsEditFlow(true);
      setActiveStep(index);
    }
    const callBack = (resData) => {
      const immResData = fromJS(resData);
      setWarnings(
        immResData.get('warningConfigLevels', List()).size
          ? immResData.get('warningConfigLevels', List())
          : initialWarnings
      );
    };
    const params = {
      programId,
      courseId,
      warningConfigBasedOn: 'course',
    };
    setOpen(true);
    dispatch(getLmsWarningConfig(params, callBack));
    dispatch(getRolesList());
  };
  useEffect(() => {
    const callBack = (resData) => {
      const immResData = fromJS(resData);
      setWarnings(
        immResData.get('warningConfigLevels', List()).size
          ? immResData.get('warningConfigLevels', List())
          : initialWarnings
      );
      setWarningLists(
        immResData.get('warningConfigLevels', List()).size
          ? immResData.get('warningConfigLevels', List())
          : initialWarnings
      );
    };
    const params = {
      programId,
      courseId,
      warningConfigBasedOn: 'course',
    };
    dispatch(getLmsWarningConfig(params, callBack));
    dispatch(getRolesList());
  }, []); //eslint-disable-line

  const toggleActive = (i) => {
    setWarningLists((prev) =>
      prev.setIn([i, 'isActive'], !prev.getIn([i, 'isActive'], false))
    );
    setWarnings((prev) =>
      prev.setIn([i, 'isActive'], !prev.getIn([i, 'isActive'], false))
    );
  };

  const toggleOpen = (index) => {
    setWarningLists((prev) => {
      const updated = prev.map((warning) => {
        return warning.set(
          'isOpen',
          warning.get('typeIndex') === index
            ? !warning.get('isOpen', false)
            : false
        );
      });
      return updated;
    });

    setWarnings((prev) => {
      const updated = prev.map((warning) => {
        return warning.set(
          'isOpen',
          warning.get('typeIndex') === index
            ? !warning.get('isOpen', false)
            : false
        );
      });
      return updated;
    });
  };

  // Assign Incharge state using Immutable.js
  const [assignInchargeData, setAssignInchargeData] = useState(() => {
    // Get persisted groupType from localStorage
    const savedGroupType =
      localStorage.getItem('warningCard_groupType') || 'male';

    return fromJS({
      deliveryType: '',
      groupType: savedGroupType,
      selectedStaffList: Map(),
      deliveryGroups: List(),
      groupTypes: List(),
    });
  });

  // Optional: Clean up localStorage on component unmount
  // useEffect(() => {
  //   return () => {
  //     localStorage.removeItem('warningCard_groupType');
  //   };
  // }, []);

  return (
    <div style={{ paddingTop: '4em' }} className="container-fluid">
      <StyledCard>
        <CardContent sx={{ p: 3 }}>
          <div className="bg-white border_radius_4">
            <div className="d-flex">
              <h4 className="bold">Warning and Denial Configuration</h4>
              {/* {lmsSettings.get('warningConfig', List()).size > 0 && ( */}
              <div className="ml-auto">
                <MButton
                  className="mr-2"
                  variant="contained"
                  color={'primary'}
                  startIcon={<AddCircleIcon />}
                  clicked={() => handleClickOpen('updateNew')}
                  sx={{ textTransform: 'none' }}
                >
                  Add New Types
                </MButton>
              </div>
              {/* )} */}
            </div>
            <div className="w-100">
              {warningLists.size !== 0 ? (
                warningLists.map((warningData, i) => {
                  return (
                    <div
                      key={warningData.get('typeIndex') || `warning-${i}`}
                      className="p-4 mb-4 cursor-pointer"
                    >
                      <div className="w-100">
                        <div className="d-flex align-items-center justify-content-between">
                          <div
                            className="d-flex"
                            onClick={() => toggleOpen(i + 1)}
                          >
                            {warningData.get('isOpen', false) ? (
                              <ExpandLessIcon className="mr-2" />
                            ) : (
                              <ExpandMoreIcon className="mr-2" />
                            )}
                            <div className="blackText-color bold">
                              {warningData.get('labelName', '')}
                            </div>
                          </div>
                          <Divider className="flex-grow-1 mr-3 ml-3" />

                          <div className="d-flex border justify-content-between">
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleActive(i);
                              }}
                              className={`px-4 py-1 ${
                                warningData.get('isActive', '')
                                  ? 'bg-success text-black'
                                  : ''
                              }`}
                            >
                              Active
                            </div>
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleActive(i);
                              }}
                              className={`px-4 py-1 ${
                                !warningData.get('isActive', '')
                                  ? 'bg-danger text-white'
                                  : ''
                              }`}
                            >
                              Inactive
                            </div>
                          </div>
                          <MenuItemDropdown
                            warningIndex={i}
                            setEditIndex={setEditIndex}
                            handleClickOpen={handleClickOpen}
                          />
                        </div>
                        <div>
                          {warningData.get('isOpen', false) && (
                            <div className="pl-3 py-5" style={inactiveStyle}>
                              <div className="d-flex align-items-center">
                                <div className="ml-3 mr-5">
                                  <div className="lightGray-color">Label:</div>
                                  <div className="blackText-color">
                                    {warningData.get('labelName', '')}
                                  </div>
                                </div>
                                <div className="mx-5">
                                  <div className="lightGray-color">
                                    Warning Message:
                                  </div>
                                  <div className="blackText-color">
                                    {warningData.get('typeName', '')}
                                  </div>
                                </div>
                                <div className="mx-5">
                                  <div className="lightGray-color">
                                    Set Percentage:
                                  </div>
                                  <div className="blackText-color">
                                    {warningData.get('percentage', '')}%
                                  </div>
                                </div>
                                <div className="mx-5">
                                  <div className="lightGray-color">
                                    Warning Color:
                                  </div>
                                  <div>
                                    <Box
                                      className="ml-4"
                                      style={
                                        i + 1 !== editIndex && isEditFow
                                          ? inactiveStyle
                                          : {}
                                      }
                                      sx={{
                                        width: 45,
                                        height: 27,
                                        backgroundColor: warningData.get(
                                          'colorCode',
                                          ''
                                        ),
                                        borderRadius: 2,
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                              <div className="my-2 blackText-color">
                                <Checkbox
                                  checked={warnings.getIn(
                                    [i, 'notificationToStaff'],
                                    false
                                  )}
                                />
                                Send Alert Notifications To Course Staffs
                              </div>
                              <div className="mb-2 blackText-color">
                                <Checkbox
                                  checked={warnings.getIn(
                                    [i, 'isAdditionStaffNotify'],
                                    false
                                  )}
                                />
                                Which Additional Staff Gets Alert Notification
                                About the Warning:
                              </div>
                              <Box
                                sx={{
                                  marginLeft: 2,
                                  display: 'flex',
                                  flexWrap: 'wrap',
                                  gap: 1,
                                }}
                              >
                                {overAllRoleList
                                  .filter((d) =>
                                    warningData
                                      .get('notificationRoleIds', List())
                                      .includes(d.get('_id', ''))
                                  )
                                  .map((s, index) => (
                                    <Chip
                                      key={s.get('_id') || `chip-${index}`}
                                      label={s.get('name', '')}
                                    />
                                  ))}
                              </Box>
                              <div className="mb-2 blackText-color">
                                <Checkbox
                                  checked={warnings.getIn(
                                    [i, 'notificationToStudent', 'isActive'],
                                    false
                                  )}
                                />
                                Send Alert Notifications To Students
                              </div>
                              <div className="d-flex align-items-center ml-2 blackText-color">
                                <div className="mb-2 mr-3">Type:</div>
                                <RadioGroup
                                  row
                                  aria-labelledby="demo-row-radio-buttons-group-label"
                                  name="row-radio-buttons-group"
                                  value={warnings.getIn(
                                    [i, 'notificationToStudent', 'setType'],
                                    ''
                                  )}
                                >
                                  <FormControlLabel
                                    value="automatic"
                                    control={<Radio size="small" />}
                                    label="Automatic"
                                  />
                                  <FormControlLabel
                                    value="manual"
                                    control={<Radio size="small" />}
                                    label="Manual"
                                  />
                                </RadioGroup>
                              </div>

                              <div className="mb-2 blackText-color">
                                <Checkbox
                                  checked={warnings.getIn(
                                    [i, 'acknowledgeToStudent'],
                                    false
                                  )}
                                />
                                Acknowledge Warning To Students In Warning
                                Alerts
                              </div>
                              <div className="mb-2 blackText-color">
                                <Checkbox defaultChecked />
                                Mark it Mandatory
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="bold text-center mt-5">
                  No Warning and Denial Found
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </StyledCard>
      {/* Assign Incharge Component */}
      <CourseWiseDeliveryTypes
        assignInchargeData={assignInchargeData}
        setAssignInchargeData={setAssignInchargeData}
        programId={programId}
        courseId={courseId}
        term={term}
        year={year}
        level={level}
        rotation={rotation}
        rotationCount={rotationCount}
        institutionCalenderId={institutionCalenderId}
      />
      {open && (
        <CreateModal
          drawerOpen={open}
          setDrawerClose={setOpen}
          programId={programId}
          courseId={courseId}
          warnings={warnings}
          setWarnings={setWarnings}
          editIndex={editIndex}
          isEditFow={isEditFow}
          activeStep={activeStep}
          setActiveStep={setActiveStep}
          skipped={skipped}
          setSkipped={setSkipped}
          warningLists={warningLists}
          setWarningLists={setWarningLists}
        />
      )}
    </div>
  );
};

export default WarningCard;
WarningCard.propTypes = {
  programId: PropTypes.string,
  courseId: PropTypes.string,
  term: PropTypes.string,
  year: PropTypes.string,
  level: PropTypes.string,
  rotation: PropTypes.string,
  rotationCount: PropTypes.string,
  institutionCalenderId: PropTypes.string,
};
