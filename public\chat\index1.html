<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>Chat Page</title>
  <link rel="stylesheet" href="css/style.css" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
  <script src="./js/data/institute_policy_data.js"></script>
  <script src="./js/data/program_policy_data.js"></script>
  <script src="./js/data/schedules_data.js"></script>
  <script src="./js/data/curricualm_monitor_data.js"></script>
  <script src="./js/data/overall_grade_data.js"></script>
  <script src="./js/data/360_data.js"></script>
  <script src="./js/data/leave_data.js"></script>

  <script src="./js/data/plo_data.js"></script>

  <script src="./js/scroll_header.js"></script>



  <script src="./js/heba_variables.js"></script>
  <script src="./js/heba-flow.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>

  <link rel="stylesheet" href="css/q360.css" />
  <script src="./js/q360.js"></script>

  <script src="//cdnjs.cloudflare.com/ajax/libs/annyang/2.6.0/annyang.min.js"></script>
  <!-- <script src="./js/speech.js"></script> -->

</head>
<style>
  #content {
    transition: opacity 0.3s ease;
  }

  /* #scrollButton {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  } */
</style>

<body>
  <input type="checkbox" id="check" />
  <label for="check">
    <div id="btn">
      <img src="./images/sidebar-open-img1.svg" alt="open" class="sidebar-open-img1" />
      <img src="./images/arrow.gif" width="60px" class="rotate-image-open" />
    </div>
    <img src="./images/arrow.gif" width="60px" alt="Close" class="rotate-image-close" id="cancel" />
  </label>


  <div class="sidebar heba-sidebar">
    <div style="text-align: center">
      <img src="./images/heba-logo.gif" class="pt-25" width="200px" />
      <h1 style="text-align: center; margin: 0" class="ask-heba-text">
        HEBA
      </h1>
    </div>
  </div>

  <section class="overall_section">
    <div id="content" class="content">
      <div class="header fix-header">
        <div class="flt-left selected-title">
          <h3>Grades and Outcomes</h3>
        </div>
        <div id="selected-form" onclick="viewFormModal();" style="display:none;"><a>View Form</a></div>
        <div class="flt-right">
          <div class="search-container">
            <span class="search-icon"><img src="./images/search.svg" /></span>
            <input type="text" class="hidden search-input" placeholder="Search..." />
          </div>
        </div>
        <div class="clr"></div>
      </div>

      <div class="chat-text-center search-loader chat-loader">
        <img src="./images/loader-main.gif" width="320px" class="pt-10" />
        <h1 class="ask-heba" style="padding: 15px 0 20px 0">
          <!-- Hi <span class="heba-user-name"></span>  <br />
            ask me anything or pick a suggestion to get started! -->
          Crafting prompts with care, sprinkling love for
          meaningful responses...
        </h1>
      </div>

      <div class="chat-text-center chat-bg page-banner">
        <img class="main_pic" src="./images/learning-big.svg" width="110px" />
        <h1 class="ask-heba" style="padding: 12px 10px 20px 10px">
          Hi <span class="heba-user-name"></span><br />
          Ask relevant questions or suggest!
        </h1>

        <div class="div-center-course common-selector policy">
          <div class="">
            <div class="text-left">
              <label class="chat-label pl-15">Policy Selection:</label>
            </div>
            <div>
              <div class="common-chip policy-chip bg-white">Institution</div>
              <div class="common-chip policy-chip bg-white">Program</div>
            </div>
            <div style="clear: both"></div>
          </div>
        </div>

        <div class=" common-selector grades_and_outcomes_staff">
          <div class="div-center-course">
            <div class="text-left">
              <label class="chat-label pl-15">Grades and Outcomes:</label>
            </div>
            <div>
              <div class="common-chip grades_outcomes_staff_chip bg-white">Overall</div>
              <div class="common-chip grades_outcomes_staff_chip   bg-white">Individual</div>
            </div>
            <div style="clear: both"></div>
          </div>
        </div>


        <div class="common-selector q360">
          <div class="div-center-course">
            <div class="text-left">
              <label class="chat-label pl-15">Level</label>
            </div>
            <div>
              <div class="common-chip q360_level_chip bg-white common-chip-q360">Institution</div>
              <div class="common-chip q360_level_chip bg-white common-chip-q360">Course</div>
              <div class="common-chip q360_level_chip bg-white common-chip-q360">Program</div>

            </div>
            <div style="clear: both"></div>
          </div>

          <div class="div-center-course pt-25" style="height:100px">
            <div style="float: left;width: 48%;text-align:left;">
          
              <label class="chat-label pl-15">Academic Year:</label>
        
            <div class="box1">
              <select class="academic-years-append">
                <option value="">Select Academic Year</option>
              </select>
            </div>
          </div>

          <div style="float: left;width: 50%;text-align:left;">
        
              <label class="chat-label pl-15">Forms:</label>
       
            <div class="box1">
              <select class="forms-append">
                <option value="">Select Forms</option>
              </select>
            </div>
        </div>

          </div>


          <div class="div-center-course pt-25">
            <div class="text-left">
              <label class="chat-label pl-15" id="q360-data-label">Select Institution:</label>
            </div>
            <div class="text-right" id="preview-form" style="display:none;margin-top:-18px">
               <a style=""onclick="viewFormModal();">Preview Form</a>
            </div>
            <div class="box1 text-left">
              <select class="q360-data-append">
                <option value="">Select Institution</option>
              </select>
            </div>
          </div>
   
        </div>




        <div class="div-center-course pt-25 common-selector grades_and_outcomes_staff student-selector">
          <div class="text-left">
            <label class="chat-label pl-15">student list:</label>
          </div>
          <div class="box">
            <select class="students-appender">
              <option>select student</option>
              <option id="1625887">1625887</option>
              <option id="2010422">2010422</option>
              <option id="2110361">2110361</option>
              <option id="2110421">2110421</option>
              <option id="2110484">2110484</option>
              <option id="2110490">2110490</option>
              <option id="2110497">2110497</option>
              <option id="2110498">2110498</option>
              <option id="2110499">2110499</option>
              <option id="2110503">2110503</option>


            </select>
          </div>
          <div style="clear: both"></div>
        </div>


        <div class="common-selector learning_resources course-selector">
          <div class="div-center-course">
            <div class="text-left">
              <label class="chat-label pl-15">Courses:</label>
            </div>
            <div class="courseAppender">
              <!-- <div class="digi-courses bg-white">Fundamental of Human Body</div>
                      <div class="digi-courses bg-white">Fundamental of Human Body</div>
                      <div class="digi-courses bg-white">Fundamental of Human Body</div> -->
            </div>
            <div style="clear: both"></div>
          </div>
        </div>

        <div class="div-center-course pt-25 common-selector learning_resources topic-selector">
          <div class="text-left">
            <label class="chat-label pl-15">Topics:</label>
          </div>
          <div class="box">
            <select class="topics-appender">

            </select>
          </div>
        </div>



        <div class="div-center"><img src="./images/line.svg" /></div>

        <div class="div-center-suggestion">
          <div>
            <div class="digi-question bg-white digi-height">
              Learning Resources Summary
            </div>
            <div class="digi-question bg-white digi-height">
              Key Points on Learning Outcomes
            </div>
            <div style="clear: both"></div>
          </div>
        </div>
        <div class="div-center-suggestion pb-mobile">
          <div>
            <div class="digi-question bg-white digi-height">
              Learning Resources Summary
            </div>
            <div class="digi-question bg-white digi-height">
              Key Points on Learning Outcomes
            </div>
            <div style="clear: both"></div>
          </div>
        </div>
      </div>

      <!--chat page start-->
      <div class="chat-text-center chat-bg" style="padding: 12px 0 0 0;">
        <div class="chat-appender"></div>

        <div class="clr"></div>
      </div>

      <div id="snackbar">Text copied to Clip Boared!</div>

      <div class="chat-text-center chat-bg desktop-send" style="padding: 0 0px 90px 0">
        <div class="display-arrow" style="display: none;">
          <img src="./images/down-arrow.svg" onclick="scrollToBottom()" class="scroll-button scroll-to-bottom"
            id="scrollToBottomBtn" title="Tap to down" />
        </div>
        <div>
          <div class="go-up" style="padding: 20px 0 0 0;">
            <div></div>
            <!-- <button class="button-response cursor-pointer">
              <img src="./images/regen_icon.svg" class="icon-vertical-align" /> Regenerate response
            </button> -->
            <div class="up-arrow cursor-pointer">
              <img src="./images/up-arrow.svg" onclick="scrollToTop()" class="scroll-button scroll-to-top"
                id="scrollToTopBtn" title="Tap to top" />
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="digi-d-flex scroll-button-container mobile-send">

            <div>
              <img
              src="./images/down-arrow.svg"
              onclick="scrollToBottom()"
              class="scroll-to-bottom"
              id="scrollToBottomBtn"
              title="Tap to down"
            />
            </div>

            <div>
              <img
              src="./images/up-arrow.svg"
              onclick="scrollToTop()"
              class="scroll-to-top"
              id="scrollToTopBtn"
              title="Tap to top"
            />
            </div>

          </div> -->


      <div>
        <div class="chat-input-box">
          <!-- <div class="img-attach">
                <img src="./images/attach.svg" />
              </div> -->
          <div class="hidden img-voice cursor-pointer close_icon tooltip" style="height: 25px;">
            <img src="./images/keyboard_voice.svg" /><span class="tooltiptextright">Search by voice</span>
          </div>
          <div class="chat-input chat-text-center">
            <div class="chat-align">
              <!-- <input
                    type="text"
                    class="rounded-input"
                    placeholder="Ask me anything..."
                    
                  /> -->

              <!-- <textarea class="rounded-input"
                    placeholder="Ask me anything..."
                  ></textarea> -->

              <textarea class="rounded-input" id="inputTextArea" placeholder="Ask me anything..."></textarea>


              <div class="pr-30 desktop-send">
                <img src="./images/book.gif" style="vertical-align: middle"
                  class="loader-book chat-loader cursor-pointer" />
                <button type="button" class="loader-gen send cursor-pointer">
                  <img src="./images/loader.gif" class="loader-gen-image" />
                  Generate
                </button>
              </div>

              <div class="send-icon mobile-send">
                <img src="./images/mobile-send.svg" class="send" width="45px" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--chat page end-->
    </div>
  </section>
  <div id="loading-backdrop">
    <img id="loading-image" src="./images/Heba-loading.gif" alt="Loading...">
    <br/>
    <div>Processing...</div>
  </div>
  <script>
    // function openNav() {
    //   document.getElementById('mySidenav').style.width = '250px';
    //   document.getElementById('main').style.marginLeft = '250px';
    // }

    // function closeNav() {
    //   document.getElementById('mySidenav').style.width = '0';
    //   document.getElementById('main').style.marginLeft = '0';
    // }

    // function toggleNav() {
    //   var sidenavWidth = document.getElementById('mySidenav').style.width;
    //   if (sidenavWidth === '250px') {
    //     closeNav();
    //   } else {
    //     openNav();
    //   }
    // }

    document.addEventListener('DOMContentLoaded', function () {
      const digiCourses = document.querySelectorAll('.digi-courses');

      digiCourses.forEach(function (course) {
        course.addEventListener('click', function () {
          this.classList.toggle('active');
        });
      });

      $('.academic-years-append,.q360-data-append,.forms-append').select2({
        width: 'resolve',
     });
    });

    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }

    function scrollToBottom() {
      window.scrollTo({
        top: document.body.scrollHeight - 300,
        behavior: 'smooth',
      });
    }

    window.onscroll = function () {
      scrollFunction();
    };

    function scrollFunction() {
      var scrollToTopBtn = document.getElementById('scrollToTopBtn');
      var scrollToBottomBtn = document.getElementById('scrollToBottomBtn');
      if (
        document.body.scrollTop > 20 ||
        document.documentElement.scrollTop > 20
      ) {
        scrollToTopBtn.style.display = 'block';
      } else {
        scrollToTopBtn.style.display = 'none';
      }

      const scrollToBottomScroll = window.innerHeight + window.scrollY >= document.body.scrollHeight ?
        window.innerHeight + window.scrollY >= document.body.scrollHeight : document.documentElement.scrollTop < 600



      if (
        scrollToBottomScroll


      ) {
        scrollToBottomBtn.style.display = 'none';
      } else {
        scrollToBottomBtn.style.display = 'block';
      }
    }

    function showcopy() {
      var x = document.getElementById('snackbar');
      x.className = 'show';
      setTimeout(function () {
        x.className = x.className.replace('show', '');
      }, 3000);
    }

    function myFunction() {
      var x = document.getElementById("snackbar");
      x.className = "show";
      setTimeout(function () { x.className = x.className.replace("show", ""); }, 3000);
    }

    //   function adjustOpacity() {
    //   var content = document.getElementById('content');
    //   if (document.getElementById('check').checked) {

    //     content.style.opacity = '0.1';
    //   } else {

    //     content.style.opacity = '1';
    //   }
    // }

    adjustOpacity();

    document.getElementById('check').addEventListener('change', function () {
      adjustOpacity();
    });

    const inputTextArea = document.getElementById('inputTextArea');

    inputTextArea.addEventListener('input', function () {
      this.style.height = 'auto';
      this.style.height = this.scrollHeight + 'px';
    });


    var scrollButton = document.getElementById('scrollButton');

    // Function to check scroll position and show/hide button
    function checkScrollPosition() {
      // Calculate the scroll percentage
      var scrollPercentage = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;

      // Check if scroll percentage is greater than or equal to 25%
      if (scrollPercentage >= 25) {
        // Show the button
        scrollButton.style.display = 'block';
      } else {
        // Hide the button
        scrollButton.style.display = 'none';
      }
    }

    // Add event listener for scroll event
    window.addEventListener('scroll', checkScrollPosition);
  </script>

  <div id="myModal" class="modal">
    <div class="modal-content">
      <span class="modal-close">&times;</span>
      <div id="preview-content"></div>
    </div>
  </div>
  <style>
    .scrollable-table {
      width: 95%;
      overflow-x: auto;
      scrollbar-width: thin;
      scrollbar-color: #888 #f0f0f0;
    }
    .scrollable-table::-webkit-scrollbar {
        height: 8px;
        background-color: #f0f0f0;
    }
    .scrollable-table::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 4px;
    }
  </style>
</body>

</html>