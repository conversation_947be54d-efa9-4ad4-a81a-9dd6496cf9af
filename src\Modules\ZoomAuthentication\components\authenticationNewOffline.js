import React, { Component, Suspense } from 'react';
import PropTypes from 'prop-types';
import SnackBars from '../../Utils/Snackbars';
import { Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import { Container } from 'react-bootstrap';
import * as actions from '../../../ReduxApi/Sessions/action';
import { compose } from 'redux';
import { connect } from 'react-redux';
import {
  selectMessage,
  selectIsLoading,
  selectActiveSessionSchedule,
  selectStaffFail,
} from '../../../ReduxApi/Sessions/selectors';
import Loader from '../../../Widgets/Loader/Loader';
import { eString, getURLParams } from '../../../ReduxApi/util';
import { selectAuthData } from '../../../ReduxApi/User/selectors';
// import './authentication.css';
import './authentication.scss';
import { ATTENDANCE_PERCENTAGE } from '../../../constants';
import { getSiteUrl, showArabic } from '../../../utils';

const BgCaptcha = React.lazy(() => import('./BgCaptcha'));

const INIT_STATE = {
  imageURL: null,
  loading: false,
};
class Authentication extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      data: null,
      ...INIT_STATE,
      scheduleId: getURLParams('_schedule_id', true),
      scheduleMode: getURLParams('mode', true),
      firstImageDesc: '',
    };
  }

  componentDidMount() {
    this.resetState();
    window.addEventListener('message', this.handlePostCameraAuthentication);
  }

  componentWillUnmount() {
    window.removeEventListener('message', this.handlePostCameraAuthentication);
  }

  resetState = () => {
    this.setState({ ...INIT_STATE });
  };

  sessionStarts = (mode) => {
    const { sessionStart, authData, history, activeSessionSchedule } =
      this.props;
    const search = window.location.search;
    const scheduleMode = activeSessionSchedule.get('mode', '');
    if (scheduleMode === 'onsite' && mode === 'start') {
      history.replace({
        pathname: `/sessions/onsite/${eString('primary')}/${eString('new')}`,
        search: search,
        state: { load: false },
      });
      return;
    }

    sessionStart(
      {
        _id: this.state.scheduleId,
        _staff_id: authData.get('_id', ''),
        mode: mode,
      },
      () => {
        history.push({
          pathname: '/sessions/dashboard',
          search: search,
          state: { load: false },
        });
      }
    );
  };

  studentSessionJoin = () => {
    const { studentSessionJoining, authData, history, activeSessionSchedule } =
      this.props;
    const search = window.location.search;

    const data = {
      _id: this.state.scheduleId,
      _student_id: authData.get('_id', ''),
      uuid: activeSessionSchedule.get('uuid', ''),
    };
    studentSessionJoining(data, () => {
      history.push({
        pathname: '/sessions/dashboard',
        search: search,
        state: { load: false },
      });
    });
  };

  checkStarter = () => {
    const { activeSessionSchedule, authData } = this.props;
    return (
      activeSessionSchedule.getIn(['sessionDetail', 'startBy'], '') ===
        authData.get('_id', '') ||
      activeSessionSchedule.getIn(['sessionDetail', 'startBy'], '') === ''
    );
  };

  modalOpen = () => {
    this.props.setData(Map({ staffFail: true }));
  };

  modalClose = () => {
    this.props.setData(Map({ staffFail: false }));
  };

  staffStart = () => {
    this.sessionStarts(this.checkStarter() ? 'start' : 'join');
  };

  studentStart = () => {
    this.studentSessionJoin();
  };

  studentStart = () => {
    this.studentSessionJoin();
  };

  handlePostCameraAuthentication = (e) => {
    const { authData } = this.props;
    const userType = authData.get('user_type', 'staff');
    const userId = authData.get('user_id', '');

    const captchaShow = authData.getIn(
      [
        'scheduleAttendanceConfig',
        userType === 'staff' ? 'staffCaptcha' : 'studentCaptcha',
      ],
      true
    );

    const { type } = e.data;
    if (typeof type === 'string' && userId.includes('DCAuto')) {
      if (userType === 'staff')
        this.sessionStarts(this.checkStarter() ? 'start' : 'join');
      else this.studentSessionJoin();
      return;
    }

    if (type === 'success') {
      this.props.setData(Map({ message: 'Authentication Verifed Success.' }));
      if (userType === 'staff')
        this.sessionStarts(this.checkStarter() ? 'start' : 'join');
      else this.studentSessionJoin();
    } else if (type === 'failed') {
      if (captchaShow) {
        this.modalOpen();
      } else {
        this.props.setData(Map({ message: 'Face not recognized.' }));
      }
    }
  };

  removeTrailingSlash = (url) => {
    if (url.endsWith('/')) {
      return url.slice(0, -1);
    }
    return url;
  };

  render() {
    const { isLoading, message, authData, staffFail } = this.props;
    const { loading } = this.state;
    const isLive = false; //activeSessionSchedule.get('isLive', false);
    const siteURL = getSiteUrl();
    const iframeURL = `${this.removeTrailingSlash(
      siteURL.DC_URL
    )}/face/verify.html?url=${this.removeTrailingSlash(
      process.env.REACT_APP_AUTH_URL
    )}/auth/facial-labeled-descriptors-v2?employeeOrAcademicId=${authData.get(
      'user_id',
      ''
    )}&isLive=${isLive}`;
    return (
      <>
        <Loader isLoading={loading || isLoading} />
        {message !== '' && <SnackBars show={true} message={message} />}
        {staffFail && (
          <Suspense fallback="">
            <BgCaptcha
              modalClose={this.modalClose}
              staffStart={this.staffStart}
              studentStart={this.studentStart}
              authData={authData}
            />
          </Suspense>
        )}
        <Container
          fluid
          className="digi-bg-authentication"
          style={{ height: '100vh' }}
        >
          {authData.get('user_type', '') === 'student' && showArabic() && (
            <div
              dir="rtl"
              lang="ar"
              className="text-center text-white f-20 pt-2 pb-2"
            >
              {`ملحوظة:
سيتم تسجيل الحضور بنجاح بإستخدام بصمة الوجه و حضور${ATTENDANCE_PERCENTAGE}٪ من إجمالي مدة المحاضرة  مع العلم أن عدم بلوغ الحد الأدنى لمدة الحضور سيحتسب الطالب/ة غائبًا وفقًا للسياسة الموضوعة من قبل إدارة الكلية و للإستفادة القصوى من المحاضرات عن بُعد يوصى باستخدام متصفح Google Chrome أحدث إصدار (مع تمكين الكاميرا والميكروفون والصوت والإشعارات).برجاء التأكد أن الإضاءة المحيطة جيدة وأن وجهك ظاهر في الدائرة أدناه.`}
            </div>
          )}

          <div
            className="text-center text-white f-20 pt-2 pb-2"
            title={
              'Make sure you have good ambient light. Try to fit your face in circle'
            }
          >
            Make sure you have good ambient light. Try to fit your face in
            circle
          </div>

          <div
            className="position-relative"
            style={{
              width: '507px',
              padding: '3px 3px 0',
              borderRadius: 0,
              height: '62vh',
              overFlow: 'hidden',
              margin: '0 auto',
            }}
          >
            <iframe
              src={iframeURL}
              title="Face capture"
              style={{ width: '100%', border: 'none', height: '100%' }}
              allowFullScreen
              allow="camera; microphone; fullscreen; autoplay;"
            ></iframe>
          </div>
          {authData.get('user_type', '') === 'student' && (
            <div
              className="text-white f-18 pt-2"
              style={{
                width: '600px',
                margin: 'auto',
                paddingBottom: '2em',
              }}
            >
              <span>Note:</span>
              <p>
                • Attendance will be marked Present Successfully provided you
                are available in the Remote / Online Session for a Minimum of{' '}
                {ATTENDANCE_PERCENTAGE}% of the Total Duration. You are advised
                to actively participate in your Session for Quality Learning.
              </p>
              <p>
                • Please note, not achieving the Minimum Duration will be
                considered Absent as per the Institution Policy.
              </p>
              <p>
                • Recommend Browser - Google Chrome (Latest Version with Camera,
                Microphone, Sound &amp; Notifications Enabled)
              </p>
            </div>
          )}
        </Container>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    authData: selectAuthData(state),
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    activeSessionSchedule: selectActiveSessionSchedule(state),
    staffFail: selectStaffFail(state),
  };
};

Authentication.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  authData: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  activeSessionSchedule: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  sessionStart: PropTypes.func,
  studentSessionJoining: PropTypes.func,
  staffFail: PropTypes.bool,
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions)
)(Authentication);
