import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { List, Map } from 'immutable';
import merged from '../../../Assets/merged.png';
import { getMergeSessions, getClassName } from '../../Utils/utils';
import { CapitalizeAll } from '../../../ReduxApi/util';
import SessionTableRows from './SessionTableRows';

const SessionTable = ({
  sessions,
  authData,
  userType,
  programId,
  onSessionClick,
}) => {
  const { t } = useTranslation();
  if (!sessions || sessions.size === 0) {
    return (
      <div className="row mb-5 pt-3">
        <div className="col-lg-12 text-center mt-5">
          {t('no_session_found')} ...
        </div>
      </div>
    );
  }

  return (
    <div className="row mb-5 pt-3">
      {sessions
        .filter((item) => item.get('schedules', List()).size > 0)
        .map((data, i) => {
          const sessionType = data.getIn(['schedules', 0, 'type'], 'regular');
          const isMerged = data.get('merge_status', false);
          return (
            <div className="col-md-12 pb-3" key={i}>
              <div className="pt-2 border-radious-8">
                <div className="bg-white pl-3 pr-3 pt-2 pb-2 top_left_right_radious">
                  <div className="d-flex justify-content-between">
                    <div>
                      <p
                        className={getClassName(
                          'text-right pl-3',
                          '',
                          'mb-1 bold'
                        )}
                      >
                        {isMerged ? (
                          <>
                            <img
                              src={merged}
                              alt="Digiclass"
                              className="digi-add-icon"
                            />
                            {data.getIn(
                              ['schedules', 0, 'session', 'delivery_symbol'],
                              ''
                            ) +
                              data.getIn(
                                ['schedules', 0, 'session', 'delivery_no'],
                                ''
                              ) +
                              ', '}
                            {getMergeSessions(
                              data.getIn(['schedules', 0, 'merge_with'], List())
                            )}
                          </>
                        ) : (
                          <>
                            {sessionType === 'regular' ? (
                              <>
                                {data.get('delivery_symbol')}
                                {data.get('delivery_no')} -{' '}
                                {data.get('session_topic')}
                              </>
                            ) : (
                              <>
                                {CapitalizeAll(
                                  data.get('session_type').replace('_', ' ')
                                )}{' '}
                                - {data.get('session_topic')}
                              </>
                            )}
                          </>
                        )}
                      </p>
                    </div>
                    <div className="pt-1">
                      <div
                        className="d-flex justify-content-between"
                        onClick={() =>
                          onSessionClick(data.getIn(['schedules', 0], Map()))
                        }
                      >
                        <p className="mb-2 text-skyblue pt-1 bold icon">
                          {t('view')}
                        </p>
                        <i
                          className={getClassName(
                            'pl-2-arabic fa fa-chevron-left',
                            'pl-2 fa fa-chevron-right',
                            'text-skyblue pt-2 digi-pointer'
                          )}
                          aria-hidden="true"
                          style={{ fontSize: '15px' }}
                        ></i>
                      </div>
                    </div>
                  </div>
                </div>
                <SessionTableRows
                  schedules={data.get('schedules', List())}
                  authData={authData}
                  userType={userType}
                  programId={programId}
                  onSessionClick={onSessionClick}
                />
              </div>
            </div>
          );
        })}
    </div>
  );
};

SessionTable.propTypes = {
  sessions: PropTypes.instanceOf(List).isRequired,
  authData: PropTypes.object.isRequired,
  userType: PropTypes.string.isRequired,
  programId: PropTypes.string.isRequired,
  onSessionClick: PropTypes.func.isRequired,
};

export default SessionTable;
