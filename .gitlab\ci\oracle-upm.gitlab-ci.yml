.rules_oracle:
  rules:
    - if: $CI_COMMIT_BRANCH == "v2-master-oracle"
      when: manual

default:
  tags:
    - dc-web

.aws_image:
  image:
    name: amazon/aws-cli:2.9.21
    entrypoint: ['']

.build:
  extends: .aws_image
  services:
    - docker:23.0.0-dind
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
    # TODO: use credential helpers
    - aws ecr get-login-password | docker login --username AWS --password-stdin $AWS_CONTAINER_REGISTRY

.ecs_update_service:
  script:
    - echo "Updating the service..."
    - aws ecs update-service --service $AWS_ECS_SERVICE --task-definition $AWS_ECS_TASK_DEFINITION --cluster $AWS_ECS_CLUSTER --force-new-deployment

.ecs_register_task_definition:
  script:
    - echo "Fetching task definition detail..."
    - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$AWS_ECS_TASK_DEFINITION" --region "$AWS_DEFAULT_REGION")
    - NEW_CONTAINER_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$ECR:$CI_COMMIT_TAG" '.taskDefinition.containerDefinitions[0].image = $IMAGE | .taskDefinition.containerDefinitions[0]')
    - echo "Registering new container definition..."
    - aws ecs register-task-definition --region "$AWS_DEFAULT_REGION" --family "$AWS_ECS_TASK_DEFINITION" --container-definitions "$NEW_CONTAINER_DEFINITION"

build oracle-upm:
  stage: build
  environment: oracle
  extends:
    - .rules_oracle
    - .build
  variables:
    ECR_ORACLE: $ECR:upm
  script:
    - docker build --build-arg buildStage=master-oracle --tag $ECR_ORACLE .
    - docker push $ECR_ORACLE
    - docker tag $ECR_ORACLE $OCI_TAG
    - docker login -u $OCI_USER -p $OCI_AUTH_TOKEN $OCI_CONTAINER_REGISTRY
    - docker push $OCI_TAG