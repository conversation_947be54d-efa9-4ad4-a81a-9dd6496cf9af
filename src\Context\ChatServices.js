import React from 'react';
import { StreamChat } from 'stream-chat';

export const ChatClient = StreamChat.getInstance(
  process.env.REACT_APP_CHAT_KEY
);

export const ChatContext = React.createContext();

export const ChatUser = async (authData) => {
  if (!authData.isEmpty()) {
    return await ChatClient.connectUser(
      { id: authData.get('_id', '') },
      authData.get('ioToken', '')
    );
  } else {
    return null;
  }
};
