body, html {
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
  background-color: #f4f6f8;
}

#current-time {
  padding-left: 5px;
}

.header {
  background-color: #1877F2;
  color: white;
  text-align: center;
  padding: 3px;
}

.instruction-wrapper {
  margin-left: 5px;
}

#current-date-time {
  font-size: 18px;
  color: #ddd;
}

#current-date {
  padding-right: 5px;
}

.slider-container {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: auto; /* Adjust based on content */
}

.slider {
  display: flex;
  transition: transform 0.5s ease-in-out;
  width: 200%; /* Double the width for sliding effect */
}

.slide {
  width: 50%; /* Each slide takes up half of the slider */
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

 

.status-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  padding: 20px;
  text-align: left;
  cursor: pointer;
}

.status-card i {
  font-size: 24px;
  margin-bottom: 10px;
}

.status-card h3 {
  margin: 10px 0;
  font-size: 18px;
  color: #333;
}

.status-card p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.sidebar {
  position: fixed;
  right: -100%;
  top: 0;
  width: 100%;
  max-width: 400px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: -2px 0 5px rgba(0,0,0,0.2);
  transition: right 0.3s ease-in-out;
  padding: 20px;
  z-index: 1000;
  overflow-y: auto; /* Enable vertical scrolling */
}

.sidebar.open {
  right: 0;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sidebar h2 {
  margin-top: 0;
  color: #333;
}

.sidebar button.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  align-self: flex-end;
  cursor: pointer;
}

footer {
  position: fixed;
  bottom: 0;
  width: 100%;
}

#current-date-time {
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-icon {
  margin-left: 10px;
  font-size: 24px;
  cursor: pointer;
}

#settings-sidebar {
  position: fixed;
  right: -100%;
  top: 0;
  width: 100%;
  max-width: 400px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: -2px 0 5px rgba(0,0,0,0.2);
  transition: right 0.3s ease-in-out;
  padding: 20px;
  z-index: 1000;
  overflow-y: auto; /* Enable vertical scrolling */
}

#settings-sidebar.open {
  right: 0;
}

.filter-section {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.filter-panel {
  margin-top: 20px;
}

.filter-panel h3 {
  margin-bottom: 15px;
}

.filter-panel label {
  display: block;
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.filter-panel button {
  margin-top: 20px;
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background-color: #1877F2;
  color: white;
  font-size: 16px;
}

.students-container {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.students-list {
  width: 45%;
}

.students-list h4 {
  margin-bottom: 10px;
  color: #333;
}

.students-list ul {
  list-style: none;
  padding: 0;
  max-height: 200px; /* Limit the height */
  overflow-y: auto; /* Enable vertical scrolling */
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 8px;
}

.students-list li {
  margin-bottom: 5px;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.status-card.ok {
  border-left: 10px solid #48bb78; /* Green */
}

.status-card.warn {
  border-left: 10px solid #ed8936; /* Orange */
}

.status-card.failed {
  border-left: 10px solid #e53e3e; /* Red */
}

.status-card.ongoing {
  border-left: 10px solid #4299e1; /* Blue */
}

.status-card.upcoming {
  border-left: 10px solid #ecc94b; /* Yellow */
}

.status-card.late {
  border-left: 10px solid #e53e3e; /* Red */
}

.status-card.completed {
  border-left: 10px solid #48bb78; /* Green */
}
