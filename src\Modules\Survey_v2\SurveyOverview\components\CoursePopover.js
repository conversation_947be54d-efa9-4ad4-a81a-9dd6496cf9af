import * as React from 'react';
import PropTypes from 'prop-types';
import { useEffect } from 'react';
import { styled } from '@mui/material/styles';

import {
  Accordion,
  AccordionDetails,
  Button,
  DialogActions,
  FormControl,
  InputAdornment,
  OutlinedInput,
  Tab,
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import SearchIcon from '@mui/icons-material/Search';
import Checkbox from '@mui/material/Checkbox';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Backdrop from '@mui/material/Backdrop';
import DialogModal from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import { List, Map } from 'immutable';
import { useState } from 'react';
import Tabs, { tabsClasses } from '@mui/material/Tabs';
import {
  StepperPanel,
  StepProvider,
  useDebounce,
} from 'Modules/Survey_v2/Utils/jsUtils';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';

const dialogSX = {
  boxShadow: '0',
  borderRadius: '8px',
  width: '480px',
};
const inputStyles = {
  pr: '8px',
  '& .MuiInputBase-input::placeholder': {
    color: '#6c757d !important',
    fontSize: '14px',
  },
};
const style = {
  minWidth: 0,
  minHeight: 10,
  padding: '5px',
};
const tabListSX = {
  [`& .${tabsClasses.scrollButtons}`]: {
    '&.Mui-disabled': { opacity: 0.3 },
  },
  '& .MuiTabs-flexContainer': {
    gap: '10px',
  },
};
const AccordionSummary = styled(MuiAccordionSummary)(() => ({
  minHeight: '0px',
  borderBottom: '1px solid #D1D5DB',
  padding: 0,
  '&.Mui-expanded': {
    minHeight: '0px',
    backgroundColor: '#F3F4F6',
  },
  '& .MuiAccordionSummary-content.Mui-expanded': {
    margin: '0px',
  },
  '& .MuiAccordionSummary-content': {
    margin: 0,
  },
}));

export const getCheckedCourseNames = (immutableData, isAllProgram) => {
  let courseNames = Map({
    programId: List(),
    term: List(),
    year: List(),
    level: List(),
    courseId: List(),
    courseName: List(),
    courseParticipantDetails: List(),
  });

  immutableData.forEach((program, programIndex) => {
    const programId = program.get('programId', '');
    const programName = program.get('programName', '');
    // Update program, term, year, level, and courseId lists

    program.get('term').forEach((term, termIndex) => {
      const termName = term.get('term', '');

      term.get('yearLevel').forEach((yearLevel, yearLevelIndex) => {
        const year = yearLevel.get('year', '');
        const level = yearLevel.get('level', '');

        yearLevel.get('course').forEach((course, courseIndex) => {
          const courseChecked = course.get('isChecked', false);
          const courseName = `${course.get('courseCode', '')} - ${course.get(
            'courseName',
            ''
          )}`;
          const courseId = course.get('courseId', '');

          if (courseChecked) {
            // Concatenate indices for participantIndex
            const participantIndex = `${programIndex}/${termIndex}/${yearLevelIndex}/${courseIndex}`;
            const participantName = `${programName}/${termName}/${year}/${level}`;

            // Update courseParticipantDetails
            courseNames = courseNames.update(
              'courseParticipantDetails',
              (list) =>
                list.push(
                  Map({
                    name: courseName,
                    participantIndex: participantIndex,
                    participantName: participantName,
                  })
                )
            );

            // Set course names based on isAllProgram flag
            if (isAllProgram) {
              courseNames = courseNames.set(
                'courseName',
                List(['All Courses'])
              );
            } else {
              courseNames = courseNames.update('courseName', (list) =>
                list.push(courseName)
              );
            }
            courseNames = courseNames.update('programId', (list) =>
              list.push(programId)
            );
            courseNames = courseNames.update('term', (list) =>
              list.push(termName)
            );
            courseNames = courseNames.update('year', (list) => list.push(year));
            courseNames = courseNames.update('level', (list) =>
              list.push(level)
            );
            courseNames = courseNames.update('courseId', (list) =>
              list.push(courseId)
            );
          }
        });
      });
    });
  });

  return courseNames;
};

const CourseComponentTab = ({
  programIndex,
  termIndex,
  termDetail,
  checkedOption,
  setCheckedOption,
  searchQuery = '',
}) => {
  const [value, setValue] = React.useState('1');
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const yearLevelDetails = termDetail.get('yearLevel', List());

  const handleYearLevelChecked = (
    e,
    programIndex,
    termIndex,
    yearLevelIdex,
    yearLevelDetail
  ) => {
    const checkedValue = e.target.checked;
    e.stopPropagation();
    setCheckedOption((prev) =>
      prev.updateIn(
        [programIndex, 'term', termIndex, 'yearLevel', yearLevelIdex],
        (value) =>
          value
            .set('isChecked', checkedValue)
            .set('selectedCount', checkedValue ? value.get('totalCount', 0) : 0)
            .update('course', (courseValue) => {
              return courseValue.map((courseDetails) =>
                courseDetails.set('isChecked', checkedValue)
              );
            })
      )
    );
    updateYearTerm(e, programIndex, termIndex);
  };
  const updateYearTerm = React.useCallback(
    (e, programIndex, termIndex) => {
      setCheckedOption((pre) =>
        pre.updateIn([programIndex, 'term', termIndex], (value) =>
          value.update((v) =>
            v
              .set(
                'selectedCount',
                pre
                  .getIn([programIndex, 'term', termIndex, 'yearLevel'], List())
                  .reduce((count, item) => {
                    return count + (item.get('isChecked', false) ? 1 : 0);
                  }, 0)
              )
              .set('isChecked', e.target.checked)
          )
        )
      );
      updateYearProgram(programIndex);
    },
    [checkedOption] //eslint-disable-line
  );
  const updateYearProgram = React.useCallback(
    (programIndex) => {
      setCheckedOption((pre) => {
        const termIsChecked = pre
          .getIn([programIndex, 'term'], List())
          .every(
            (term) => term.get('selectedCount', 0) === term.get('totalCount', 0)
          );
        return pre
          .setIn([programIndex, 'isChecked'], termIsChecked)
          .setIn(
            [programIndex, 'selectedCount'],
            termIsChecked ? pre.getIn([programIndex, 'totalCount'], 0) : 0
          );
      });
    },
    [checkedOption] //eslint-disable-line
  );
  const handleCourseChecked = (
    e,
    programIndex,
    termIndex,
    yearLevelIdex,
    courseIndex
  ) => {
    const checkedValue = e.target.checked;
    e.stopPropagation();
    setCheckedOption((prev) => {
      return prev.updateIn(
        [
          programIndex,
          'term',
          termIndex,
          'yearLevel',
          yearLevelIdex,
          'course',
          courseIndex,
        ],
        (value) => {
          return value.set('isChecked', checkedValue);
        }
      );
    });
    updateCourseYear(programIndex, termIndex, yearLevelIdex);
  };
  const updateCourseYear = React.useCallback(
    (programIndex, termIndex, yearLevelIdex) => {
      setCheckedOption((pre) => {
        const courseValue = [
          programIndex,
          'term',
          termIndex,
          'yearLevel',
          yearLevelIdex,
          'course',
        ];
        const courseIsChecked = pre
          .getIn(courseValue, List())
          .every((course) => course.get('isChecked', false));
        const yearLevelIsChecked = [
          programIndex,
          'term',
          termIndex,
          'yearLevel',
          yearLevelIdex,
          'isChecked',
        ];
        const selectedCount = [
          programIndex,
          'term',
          termIndex,
          'yearLevel',
          yearLevelIdex,
          'selectedCount',
        ];

        return pre
          .setIn(yearLevelIsChecked, courseIsChecked)
          .setIn(
            selectedCount,
            courseIsChecked
              ? pre
                  .setIn(
                    selectedCount,
                    pre.getIn(
                      [
                        programIndex,
                        'term',
                        termIndex,
                        'yearLevel',
                        yearLevelIdex,
                        'totalCount',
                      ],
                      0
                    )
                  )
                  .getIn(selectedCount, 0)
              : 0
          );
      });
      updateCourseTerm(programIndex, termIndex);
    },
    [checkedOption] //eslint-disable-line
  );
  const updateCourseTerm = React.useCallback(
    (programIndex, termIndex) => {
      setCheckedOption((pre) => {
        const yearLevelIsChecked = pre
          .getIn([programIndex, 'term', termIndex, 'yearLevel'], List())
          .every(
            (yearLevel) =>
              yearLevel.get('selectedCount', 0) ===
              yearLevel.get('totalCount', 0)
          );
        return pre
          .setIn(
            [programIndex, 'term', termIndex, 'isChecked'],
            yearLevelIsChecked
          )
          .setIn(
            [programIndex, 'term', termIndex, 'selectedCount'],
            yearLevelIsChecked
              ? pre.getIn([programIndex, 'term', termIndex, 'totalCount'], 0)
              : 0
          );
      });
      updateCourseProgram(programIndex);
    },
    [checkedOption] //eslint-disable-line
  );
  const updateCourseProgram = React.useCallback(
    (programIndex) => {
      setCheckedOption((pre) => {
        const programIsChecked = pre
          .getIn([programIndex, 'term'], List())
          .every(
            (program) =>
              program.get('selectedCount', 0) === program.get('totalCount', 0)
          );
        return pre
          .setIn([programIndex, 'isChecked'], programIsChecked)
          .setIn(
            [programIndex, 'selectedCount'],
            programIsChecked ? pre.getIn([programIndex, 'totalCount'], 0) : 0
          );
      });
    },
    [checkedOption] //eslint-disable-line
  );

  return (
    <>
      <StepProvider activeStep={value}>
        <Tabs
          value={value}
          onChange={handleChange}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          style={style}
          sx={tabListSX}
          className="py-0 pl-2 pr-0"
        >
          {yearLevelDetails.map((yearLevelDetail, yearLevelIdex) => {
            const yearName = yearLevelDetail.get('year', '');
            const levelName = yearLevelDetail.get('level', '');
            const yearLevelSelectedCount = yearLevelDetail.get(
              'selectedCount',
              0
            );
            const yearLevelTotalCount = yearLevelDetail.get('totalCount', 0);
            const isShowedYearLevel = yearLevelDetail.get('isShow', true);
            if (!isShowedYearLevel) {
              return null;
            }
            return (
              <Tab
                key={yearLevelIdex}
                className="px-0"
                style={style}
                label={
                  <div className="py-1">
                    {!searchQuery && (
                      <Checkbox
                        disableRipple
                        className="p-0 mr-2"
                        size="small"
                        onClick={(e) =>
                          handleYearLevelChecked(
                            e,
                            programIndex,
                            termIndex,
                            yearLevelIdex,
                            yearLevelDetail
                          )
                        }
                        checked={yearLevelSelectedCount === yearLevelTotalCount}
                      />
                    )}
                    <span className="text-capitalize">
                      {yearName + ' / ' + levelName}
                    </span>
                  </div>
                }
                value={`${yearLevelIdex + 1}`}
              />
            );
          })}
        </Tabs>

        {yearLevelDetails.map((yearLevelDetail, yearLevelIdex) => {
          const courseDetails = yearLevelDetail.get('course', List());
          const yearLevelSelectedCount = yearLevelDetail.get(
            'selectedCount',
            0
          );
          const yearLevelTotalCount = yearLevelDetail.get('totalCount', 0);

          return (
            <StepperPanel
              key={yearLevelIdex}
              value={`${yearLevelIdex + 1}`}
              className="p-0 m-0"
            >
              {!searchQuery && (
                <div className="d-flex justify-content-end align-items-center">
                  <div>
                    <Checkbox
                      size="small"
                      disableRipple
                      className="text-capitalize"
                      onClick={(e) =>
                        handleYearLevelChecked(
                          e,
                          programIndex,
                          termIndex,
                          yearLevelIdex,
                          yearLevelDetail
                        )
                      }
                      checked={yearLevelSelectedCount === yearLevelTotalCount}
                    />
                  </div>
                  <div>All Course</div>
                </div>
              )}

              {courseDetails.map((courseDetail, courseIndex) => {
                const courseCode = courseDetail.get('courseCode', '');
                const courseName = courseDetail.get('courseName', '');
                const isCourseChecked = courseDetail.get('isChecked', false);
                const isShowedCourse = courseDetail.get('isShow', true);
                if (!isShowedCourse) {
                  return null;
                }
                return (
                  <div
                    key={courseIndex}
                    className="d-flex gap-2 align-items-center border-bottom py-2"
                  >
                    <SubdirectoryArrowRightIcon className="pl-2 ml-1 primary-color" />
                    <div>
                      <Checkbox
                        size="small"
                        className="text-capitalize p-0"
                        disableRipple
                        onClick={(e) =>
                          handleCourseChecked(
                            e,
                            programIndex,
                            termIndex,
                            yearLevelIdex,
                            courseIndex,
                            courseDetail
                          )
                        }
                        checked={isCourseChecked}
                      />
                    </div>
                    <div>{courseCode + ' - ' + courseName}</div>
                  </div>
                );
              })}
            </StepperPanel>
          );
        })}
      </StepProvider>
    </>
  );
};
CourseComponentTab.propTypes = {
  termDetail: PropTypes.instanceOf(List),
  programIndex: PropTypes.number,
  termIndex: PropTypes.number,
  setCheckedOption: PropTypes.number,
  checkedOption: PropTypes.instanceOf(List),
  searchQuery: PropTypes.string,
};
const TermComponent = ({
  programDetails,
  programIndex,
  checkedOption,
  setCheckedOption,
  searchQuery = '',
}) => {
  const [value, setValue] = React.useState('1');
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const termDetails = programDetails.get('term', List());

  const handleTermChecked = (e, programIndex, termIndex, termDetail) => {
    const checkedValue = e.target.checked;
    e.stopPropagation();
    const updateProgramOptions = (options) => {
      return options
        .setIn([programIndex, 'term', termIndex, 'isChecked'], checkedValue)
        .setIn(
          [programIndex, 'term', termIndex, 'selectedCount'],
          checkedValue
            ? options.getIn([programIndex, 'term', termIndex, 'totalCount'], 0)
            : 0
        )
        .updateIn([programIndex, 'term', termIndex], (termValue) => {
          return termValue
            .set('isChecked', checkedValue)
            .set(
              'selectedCount',
              checkedValue ? termDetail.get('totalCount', 0) : 0
            )
            .update('yearLevel', (yearLevelValue) => {
              return yearLevelValue.map((yearLevelDetails) =>
                yearLevelDetails
                  .set('isChecked', checkedValue)
                  .set(
                    'selectedCount',
                    checkedValue ? yearLevelDetails.get('totalCount', 0) : 0
                  )
                  .update('course', (courseValue) => {
                    return courseValue.map((courseDetails) =>
                      courseDetails.set('isChecked', checkedValue)
                    );
                  })
              );
            });
        });
    };
    const updatedOptions = updateProgramOptions(checkedOption);
    setCheckedOption(updatedOptions);

    updateTerm(e, programIndex, termIndex);
  };
  const updateTerm = React.useCallback(
    (e, programIndex, termIndex) => {
      setCheckedOption((pre) => {
        const termIsChecked = pre
          .getIn([programIndex, 'term'], List())
          .every((term) => {
            return term.get('selectedCount', 0) === term.get('totalCount', 0);
          });
        return pre
          .setIn([programIndex, 'isChecked'], termIsChecked)
          .setIn(
            [programIndex, 'selectedCount'],
            termIsChecked ? pre.getIn([programIndex, 'totalCount'], 0) : 0
          );
      });
    },
    [checkedOption] //eslint-disable-line
  );

  return (
    <>
      <StepProvider activeStep={value}>
        <Tabs
          value={value}
          onChange={handleChange}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          style={style}
          sx={tabListSX}
          className="py-0 pl-2 pr-0"
        >
          {termDetails.map((termDetail, termIndex) => {
            const termName = termDetail.get('term', '');
            const termSelectedCount = termDetail.get('selectedCount', 0);
            const termTotalCount = termDetail.get('totalCount', 0);
            const isShowedTerm = termDetail.get('isShow', true);
            if (!isShowedTerm) {
              return null;
            }
            return (
              <Tab
                key={termIndex}
                style={style}
                className="px-0"
                label={
                  <div className="py-1">
                    {!searchQuery && (
                      <Checkbox
                        size="small"
                        disableRipple
                        className="p-0 mr-2"
                        onClick={(e) =>
                          handleTermChecked(
                            e,
                            programIndex,
                            termIndex,
                            termDetail
                          )
                        }
                        checked={termSelectedCount === termTotalCount}
                      />
                    )}
                    <span className="text-capitalize">{termName}</span>
                  </div>
                }
                value={`${termIndex + 1}`}
              />
            );
          })}
        </Tabs>

        {termDetails.map((termDetail, termIndex) => {
          return (
            <StepperPanel
              key={termIndex}
              value={`${termIndex + 1}`}
              className="p-0 m-0"
            >
              <CourseComponentTab
                programIndex={programIndex}
                termIndex={termIndex}
                termDetail={termDetail}
                checkedOption={checkedOption}
                setCheckedOption={setCheckedOption}
                searchQuery={searchQuery}
              />
            </StepperPanel>
          );
        })}
      </StepProvider>
    </>
  );
};
TermComponent.propTypes = {
  programDetails: PropTypes.instanceOf(List),
  programIndex: PropTypes.number,
  setCheckedOption: PropTypes.number,
  checkedOption: PropTypes.instanceOf(List),
  searchQuery: PropTypes.string,
};

const CoursePopover = ({
  anchorEl,
  setAnchorEl,
  checkedOption,
  setCheckedOption,
  SelectValue,
  setSelectValue,
}) => {
  const courseNameString = SelectValue.getIn(
    ['surveyCourse', 'courseName'],
    List()
  )
    .toJS()
    .join(', ');

  const handleOpen = () => setAnchorEl(true);
  const handleClose = () => setAnchorEl(false);

  return (
    <>
      <div className="cursor-pointer">
        <FormControl
          onClick={handleOpen}
          size="small"
          fullWidth
          variant="outlined"
        >
          <OutlinedInput
            id="outlined-adornment-password"
            placeholder="All Courses"
            value={courseNameString}
            sx={inputStyles}
            endAdornment={
              <InputAdornment position="end">
                <ArrowDropDownIcon />
              </InputAdornment>
            }
          />
        </FormControl>
      </div>
      {anchorEl && (
        <ProgramSelectionModal
          anchorEl={anchorEl}
          handleClose={handleClose}
          checkedOption={checkedOption}
          setSelectValue={setSelectValue}
          setCheckedOption={setCheckedOption}
        />
      )}
    </>
  );
};

CoursePopover.propTypes = {
  anchorEl: PropTypes.object.isRequired,
  setAnchorEl: PropTypes.func.isRequired,
  checkedOption: PropTypes.instanceOf(List).isRequired,
  setCheckedOption: PropTypes.func.isRequired,
  SelectValue: PropTypes.instanceOf(Map).isRequired,
  setSelectValue: PropTypes.func.isRequired,
};

export default CoursePopover;

const ProgramSelectionModal = ({
  anchorEl,
  handleClose,
  checkedOption,
  setSelectValue,
  setCheckedOption,
}) => {
  const [checkedHierarchyOptions, setCheckedHierarchyOptions] =
    useState(checkedOption);
  const [searchKey, setSearchKey] = useState('');

  const debouncedSearchQuery = useDebounce(searchKey, 300);

  const isSearchActive = !!debouncedSearchQuery;
  const areAllOptionsHidden = checkedHierarchyOptions.every(
    (data) => !data.get('isShow', false)
  );

  const isNoDataVisible = isSearchActive && areAllOptionsHidden;

  const isAllProgram = checkedHierarchyOptions.every(
    (i) => i.get('selectedCount', 0) === i.get('totalCount', 0)
  );

  const handleOverallProgramChecked = (e) => {
    const checkedValue = e.target.checked;
    const updateOptions = (options) => {
      return options.map((option) => {
        return option
          .set('isChecked', checkedValue)
          .set('selectedCount', checkedValue ? option.get('totalCount', 0) : 0)
          .update('term', (termValue) => {
            return termValue.map((termDetails) =>
              termDetails
                .set('isChecked', checkedValue)
                .set(
                  'selectedCount',
                  checkedValue ? termDetails.get('totalCount', 0) : 0
                )
                .update('yearLevel', (yearLevelValue) => {
                  return yearLevelValue.map((yearLevelDetails) =>
                    yearLevelDetails
                      .set('isChecked', checkedValue)
                      .set(
                        'selectedCount',
                        checkedValue ? yearLevelDetails.get('totalCount', 0) : 0
                      )
                      .update('course', (courseValue) => {
                        return courseValue.map((courseDetails) =>
                          courseDetails.set('isChecked', checkedValue)
                        );
                      })
                  );
                })
            );
          });
      });
    };

    const updatedOptions = updateOptions(checkedHierarchyOptions);
    setCheckedHierarchyOptions(updatedOptions);
  };

  const handleProgramChecked = (e, programIndex, programDetails) => {
    const checkedValue = e.target.checked;
    e.stopPropagation();
    const updateProgramOptions = (options) => {
      return options
        .setIn([programIndex, 'isChecked'], checkedValue)
        .setIn(
          [programIndex, 'selectedCount'],
          checkedValue ? options.getIn([programIndex, 'totalCount'], 0) : 0
        )
        .updateIn([programIndex, 'term'], (termValue) => {
          return termValue.map((termDetails) =>
            termDetails
              .set('isChecked', checkedValue)
              .set(
                'selectedCount',
                checkedValue ? termDetails.get('totalCount', 0) : 0
              )
              .update('yearLevel', (yearLevelValue) => {
                return yearLevelValue.map((yearLevelDetails) =>
                  yearLevelDetails
                    .set('isChecked', checkedValue)
                    .set(
                      'selectedCount',
                      checkedValue ? yearLevelDetails.get('totalCount', 0) : 0
                    )
                    .update('course', (courseValue) => {
                      return courseValue.map((courseDetails) =>
                        courseDetails.set('isChecked', checkedValue)
                      );
                    })
                );
              })
          );
        });
    };
    const updatedOptions = updateProgramOptions(checkedHierarchyOptions);
    setCheckedHierarchyOptions(updatedOptions);
  };

  const handleCourseApply = () => {
    const checkedCourseNames = getCheckedCourseNames(
      checkedHierarchyOptions,
      isAllProgram
    );
    setCheckedOption(checkedHierarchyOptions);
    setSelectValue((prev) => prev.set('surveyCourse', checkedCourseNames));
    handleClose();
  };

  const searchCourses = (data, searchQuery) => {
    if (!searchQuery.trim()) {
      // If the search query is empty, set all isShow to false
      return data.map((programItem) =>
        programItem.set('isShow', true).set(
          'term',
          programItem.get('term').map((termItem) =>
            termItem.set('isShow', true).set(
              'yearLevel',
              termItem.get('yearLevel').map((yearLevelItem) =>
                yearLevelItem.set('isShow', true).set(
                  'course',
                  yearLevelItem
                    .get('course')
                    .map((courseItem) => courseItem.set('isShow', true))
                )
              )
            )
          )
        )
      );
    }

    // Process the data when search query is not empty
    return data.map((programItem) => {
      let isProgramVisible = false;

      const updatedTerms = programItem.get('term').map((termItem) => {
        let isTermVisible = false;

        const updatedYearLevels = termItem
          .get('yearLevel')
          .map((yearLevelItem) => {
            let isYearLevelVisible = false;

            const updatedCourses = yearLevelItem
              .get('course')
              .map((courseItem) => {
                // Combine courseCode and courseName for search
                const courseName = courseItem.get('courseName').toLowerCase();
                const courseCode = courseItem.get('courseCode').toLowerCase();
                const combinedCourseInfo = `${courseCode} - ${courseName}`;

                // Check if the combined string matches the search query
                if (combinedCourseInfo.includes(searchQuery.toLowerCase())) {
                  isYearLevelVisible = true;
                  isTermVisible = true;
                  isProgramVisible = true;

                  // Set isShow to true for matching course and add displayName
                  return courseItem.set('isShow', true);
                }

                // Set isShow to false for non-matching course
                return courseItem.set('isShow', false);
              });

            // Update yearLevel visibility based on course matches
            return isYearLevelVisible
              ? yearLevelItem.set('isShow', true).set('course', updatedCourses)
              : yearLevelItem
                  .set('isShow', false)
                  .set('course', updatedCourses);
          });

        // Update term visibility based on year level matches
        return isTermVisible
          ? termItem.set('isShow', true).set('yearLevel', updatedYearLevels)
          : termItem.set('isShow', false).set('yearLevel', updatedYearLevels);
      });

      // Update program visibility based on term matches
      return isProgramVisible
        ? programItem.set('isShow', true).set('term', updatedTerms)
        : programItem.set('isShow', false).set('term', updatedTerms);
    });
  };

  const handleSearchCourse = (event) => {
    const searchCourseName = event.target.value.trim();
    setSearchKey(searchCourseName);
  };

  useEffect(() => {
    const filteredData = searchCourses(
      checkedHierarchyOptions,
      debouncedSearchQuery
    );
    setCheckedHierarchyOptions(filteredData);
  }, [debouncedSearchQuery]); //eslint-disable-line

  return (
    <DialogModal
      open={anchorEl}
      onClose={handleClose}
      closeAfterTransition
      PaperProps={{ sx: dialogSX }}
      slots={{ backdrop: Backdrop }}
      slotProps={{
        backdrop: {
          timeout: 500,
        },
      }}
    >
      <DialogTitle className="px-4 pt-4 pb-0 d-flex flex-column gap-2">
        <div>
          <FormControl fullWidth size="small" variant="outlined">
            <OutlinedInput
              sx={{
                '&::placeholder': {
                  fontSize: '14px',
                },
              }}
              id="input-with-icon-adornment"
              placeholder="Search by course name..."
              endAdornment={
                <InputAdornment position="end">
                  <SearchIcon />
                </InputAdornment>
              }
              onChange={handleSearchCourse}
            />
          </FormControl>
        </div>

        {!searchKey && (
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex f-14 align-items-center ml-auto gap-2">
              <Checkbox
                size="small"
                disableRipple
                className="m-0 p-0"
                onClick={(e) => handleOverallProgramChecked(e)}
                checked={isAllProgram}
              />
              <div className="f-12">All Program</div>
            </div>
          </div>
        )}
      </DialogTitle>
      <DialogContent className="px-4 pb-0">
        {isNoDataVisible ? (
          <div className="py-4 text-center text-secondary">No data found.</div>
        ) : (
          <div>
            {checkedHierarchyOptions.map((programDetails, programIndex) => {
              const programName = programDetails.get('programName', '');
              const programCode = programDetails.get('programCode', '');
              const programIsShow = programDetails.get('isShow', true);
              const programSelectedCount = programDetails.get(
                'selectedCount',
                0
              );
              const programTotalCount = programDetails.get('totalCount', 0);

              // Skip rendering if the program is not visible
              if (!programIsShow) {
                return null;
              }

              return (
                <Accordion
                  key={programIndex}
                  className="my-2"
                  elevation={0}
                  sx={{
                    '&.MuiAccordionSummary-content': {
                      margin: '0px',
                    },
                    '& .MuiAccordionSummary-content.Mui-expanded': {
                      margin: '0px',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon className="f-18" />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                    elevation={0}
                    className="p-2"
                  >
                    <div className="d-flex f-16 align-items-center">
                      {!debouncedSearchQuery && (
                        <Checkbox
                          size="small"
                          className="p-0 mr-2"
                          disableRipple
                          onClick={(e) =>
                            handleProgramChecked(
                              e,
                              programIndex,
                              programDetails
                            )
                          }
                          checked={programTotalCount === programSelectedCount}
                        />
                      )}
                      <div>{programCode + ' - ' + programName}</div>
                    </div>
                  </AccordionSummary>
                  <AccordionDetails className="px-0 pt-2">
                    <TermComponent
                      programDetails={programDetails}
                      programIndex={programIndex}
                      checkedOption={checkedHierarchyOptions}
                      setCheckedOption={setCheckedHierarchyOptions}
                      searchQuery={debouncedSearchQuery}
                    />
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </div>
        )}
      </DialogContent>
      <DialogActions className="px-4 pb-4">
        <div className="d-flex justify-content-end gap-4">
          <Button
            variant="outlined"
            onClick={handleClose}
            className="text-secondary text-capitalize border-secondary"
          >
            <span className="px-3">Cancel</span>
          </Button>
          <Button
            variant="contained"
            className="text-capitalize"
            onClick={handleCourseApply}
            disabled={isNoDataVisible}
            sx={{
              backgroundColor: 'var(--backgroundTheme-color-vibrant)',
              '&.MuiButton-root:hover': {
                backgroundColor: 'var(--backgroundTheme-color-vibrant)',
              },
            }}
          >
            <span className="px-3">Apply</span>
          </Button>
        </div>
      </DialogActions>
    </DialogModal>
  );
};
ProgramSelectionModal.propTypes = {
  anchorEl: PropTypes.object.isRequired,
  handleClose: PropTypes.func.isRequired,
  checkedOption: PropTypes.instanceOf(List).isRequired,
  setCheckedOption: PropTypes.func.isRequired,
  setSelectValue: PropTypes.func.isRequired,
};
