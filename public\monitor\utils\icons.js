// Icons based on status
const icons = {
    completed: 'fa-check-circle',
    warn: 'fa-exclamation-triangle',
    late: 'fa-times-circle',
    missed: 'fa-times-circle',
    ongoing: 'fa-play-circle',
    upcoming: 'fa-clock',
    inprogress: 'fa-play-circle',  // Use the same icon as ongoing
    pending: 'fa-play-circle',
    ENDED: "fa-check-circle",
    ON_GOING: "fa-play-circle",
    NOT_STARTED:"fa-clock"
  };
  
  // Styles based on status
  const statusStyles = {
    completed: {
        borderColor: '#48bb78',
        boxShadow: '0 2px 5px rgba(72, 187, 120, 0.5)'
    },
    warn: {
        borderColor: '#ed8936',
        boxShadow: '0 2px 5px rgba(237, 137, 54, 0.5)'
    },
    late: {
        borderColor: '#e53e3e',
        boxShadow: '0 2px 5px rgba(229, 62, 62, 0.5)'
    },
    missed: {
        borderColor: '#e53e3e',
        boxShadow: '0 2px 5px rgba(229, 62, 62, 0.5)'
    },
    ongoing: {
        borderColor: '#4299e1',
        boxShadow: '0 2px 5px rgba(66, 153, 225, 0.5)'
    },
    upcoming: {
        borderColor: '#87CEEB', // Updated to light blue
        boxShadow: '0 2px 5px rgba(135, 206, 235, 0.5)' // Updated to light blue shadow
    },
    inprogress: {
        borderColor: '#4299e1',  // Use the same color as ongoing
        boxShadow: '0 2px 5px rgba(66, 153, 225, 0.5)'
    },
    pending: {
        borderColor: '#4299e1',  // Use the same color as ongoing
        boxShadow: '0 2px 5px rgba(66, 153, 225, 0.5)'
    },
    ENDED: {
      borderColor: '#48bb78',
      boxShadow: '0 2px 5px rgba(72, 187, 120, 0.5)'
    },
    ON_GOING: {
      borderColor: '#4299e1',
      boxShadow: '0 2px 5px rgba(66, 153, 225, 0.5)'
    },
    NOT_STARTED: {
      borderColor: '#ed8936',
      boxShadow: '0 2px 5px rgba(237, 137, 54, 0.5)'
    },
  };
  
  // Status text mapping
  const statusText = {
    completed: 'Completed',
    warn: 'Warn',
    late: 'Late',
    missed: 'Missed',
    ongoing: 'Ongoing',
    upcoming: 'Upcoming',
    inprogress: 'In Progress',
    pending: 'Yet to start',
  };