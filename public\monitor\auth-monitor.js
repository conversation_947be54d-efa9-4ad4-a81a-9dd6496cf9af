const ONE_DAY = 6 * 60 * 60 * 1000;

$(document).ready(function () {
  if (typeof authCheck === "undefined") {
    authCheck = true;
  }

  if (authCheck) {
    initializeAuth();
  }

  $(".home-container").on("click", function () {
    window.location.href = "../";
  });

  $(".company-logo-black").on("click", function () {
    window.location.href = "../";
  });
});

function addDynamicStyles() {
  const styles = `
    <style>
      .main-content.hidden {
        display: none;
      }
      .showpass-div {
        display:none
      }

      .login-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: auto;
        background-color: #f5f5f5;
      }

      #login-form {
        padding: 30px;
        box-sizing: border-box;
        background: white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
        text-align: center;
        max-width: 400px;
        width: 100%;
      }

      #login-form h2 {
        margin: 20px 0;
        color: #1877F2;
        font-size: 24px;
      }

      .title-logo-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
      }

      .logo-container {
        display: flex;
        justify-content: center;
        width: 100%;
      }

      .company-logo-black {
        width: 150px;
        height: auto;
        margin: 0 auto;
      }

      #login-form label {
        display: block;
        margin-bottom: 5px;
        text-align: left;
        color: #333;
      }

      #login-form input {
        width: 100%;
        padding: 10px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        outline: none;
      }

      #login-form button {
        width: 100%;
        padding: 12px;
        margin: 15px 0;
        background-color: #1877F2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
      }

      #login-form button:hover {
        background-color: #1664d9;
      }

      #forgot-password {
        margin: 15px 0;
        color: #1877F2;
        cursor: pointer;
      }

      #clear-cache {
        margin: 15px 0;
        color: #1877F2;
        cursor: pointer;
      }

      #error-message {
        color: #dc3545;
        margin-top: 10px;
      }

      @media screen and (max-width: 600px) {
        .login-container {
          padding: 15px;
        }
        #login-form {
          max-width: none;
        }
      }
    </style>
  `;
  $("head").append(styles);
}

function checkSession() {
  const sessionData = JSON.parse(localStorage.getItem('userDetails'));
  if (sessionData && new Date().getTime() - sessionData.sessionData.timestamp < ONE_DAY) {
      setUsername(sessionData.username);
      $('.main-content').removeClass('hidden');
      $('#login-form').remove();
  } else {
      $('.main-content').addClass('hidden');
      showLoginForm();
  }
}

async function validateLogin(username, password) {
  $("#login-button").text("Please Wait");
  showLoader(true);
  
  // Add artificial delay (2 seconds)
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Client-side validation
  if (username === userName && password === password) {

    let mockSessionData = {
      sessionData: {
        name: "SLA2025 Monitoring",
        employeeId: "SLA@2025",
        username: username,
        timestamp: new Date().getTime(),
        role: "admin",
        tokens: {
          access: { token: "test-access-token" },
          refresh: { token: "test-refresh-token" }
        }
      }
    };

    localStorage.setItem("userDetails", JSON.stringify(mockSessionData));
    localStorage.setItem("access", mockSessionData.sessionData.tokens.access.token);
    localStorage.setItem("refresh", mockSessionData.sessionData.tokens.refresh.token);
    
    showLoader(false);
    $("#login-button").text("Login");
    setUsername();
    return true;
  }

  showLoader(false);
  $("#login-button").text("Login");
  $("#error-message").text("Invalid username or password");
  return false;

  /* Original API validation code (commented out)
  const data = new URLSearchParams();
  data.append("app", "DA");
  data.append("otpService", "email");
  data.append("isTwoFactorAuthDisabled", "true");
  data.append("email", username);
  data.append("password", password);
  $("#login-button").text("Please Wait");

  try {
    showLoader(true);
    const response = await fetch(daVerifyPasswordUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: data.toString(),
    });

    const result = await response.json();

    showLoader(false);

    if (response.status !== 200) {
      const errorMessage =
        result.errors && result.errors.length > 0
          ? result.errors[0].msg
          : "Username or password is invalid.";
      $("#error-message").text(errorMessage);
      console.log(errorMessage);
      $("#login-button").text("Login");
      return false;
    }

    if (result.data) {
      const sessionData = {
        name: result.data.name,
        employeeId: result.data.employeeId,
        username: username,
        timestamp: new Date().getTime(),
        role: result.data.role,
        tokens: result.data.tokens,
      };
      // setUsername(username)
      staffId = sessionData.employeeId;
      staffName = sessionData.username;
      $("#evaluatorportal-evaluator-name").text(sessionData.name);
      
      localStorage.setItem("userDetails",JSON.stringify({sessionData}));
      localStorage.setItem("access", sessionData.tokens.access.token);
      localStorage.setItem("refresh", sessionData.tokens.refresh.token);

      $("#login-button").text("Login");
      // location.reload();
      return true;
    } else {
      console.log("Invalid user");
      $("#error-message").text("Invalid user.");
      $("#login-button").text("Login");
      return false;
    }
  } catch (error) {
    $("#login-button").text("Login");
    console.error("Error during authentication", error);
    $("#error-message").text("Error during authentication.");
    return false;
  }
  */
}

function setUsername() {
  const sessionData = JSON.parse(localStorage.getItem("userDetails"));
  if (sessionData) {
    const userInitial = sessionData.sessionData.name.charAt(0).toUpperCase();
    $("#user-Initial").text(userInitial);
    $("#user-name").text(sessionData.sessionData.name);
    $("#user-name-info").text(sessionData.sessionData.employeeId);
    $("#user-id").text(sessionData.sessionData.username);
  }

  $("#userAvatar").on("click", function () {
    $("#userDropdown").toggle();
  });

  $(document).on("click", function (event) {
    if (!$(event.target).closest("#userAvatar").length) {
      $("#userDropdown").hide();
    }
  });

}

function showLoginForm() {
  const loginFormHtml = `
  <div class="login-container">
    <div id="login-form">
      <div class="title-logo-container">
        <div class="logo-container">
          <img class="company-logo-black" src="./utils/images/logo.svg" alt="Logo">
        </div>
      </div>
      <h2>Admin Login</h2>
      <label for="username">Email:</label>
      <input type="text" id="username"><br>
      <label for="password">Password:</label>
      <div style="position: relative;">
        <input type="password" id="password">
        <span class="toggle-password" style="position: absolute; right: 20px; top: 50%; transform: translateY(-75%); cursor: pointer; font-size: 20px;">
          &#128065;
        </span>
      </div>
      <div class='showpass-div'>
        <span>Show Password</span><input type="checkbox" class="showpass">
      </div>
      <button id="login-button">Login</button>
      <p id="forgot-password">Forgot password?</p>
      <p id="clear-cache"><i class="fas fa-recycle"></i> Clear cache</p>
      <p id="error-message"></p>
    </div>
  </div>
  `;

  $("body").append(loginFormHtml);
  $("#login-form").show();
  $("footer").hide();

  $(".showpass").on("click", function () {
    var x = document.getElementById("password");
    if (x.type === "password") {
      x.type = "text";
    } else {
      x.type = "password";
    }
  });

  $(".toggle-password").on("click", function () {
    var x = document.getElementById("password");
    if (x.type === "password") {
      x.type = "text";
    } else {
      x.type = "password";
    }
  });

  $("#login-button").on("click", async function () {
    const username = $("#username").val();
    const password = $("#password").val();

    // Validate inputs
    if (!username || !password) {
      $("#error-message").text("Email and password must not be empty.");

      $("#login-form").effect("shake");
      return;
    }

    const loginSuccess = await validateLogin(username, password);
    if (loginSuccess) {
      location.reload();
  }
  });

  $("#forgot-password")
    .css("cursor", "pointer")
    .on("click", function () {
      window.location.href = daForgotPasswordUrl;
    });

  $("#clear-cache")
    .css("cursor", "pointer")
    .on("click", function () {
      clearAllCache();
    });
}

function handleLogout() {
    // Clear all stored data
    localStorage.removeItem('userDetails');
    localStorage.removeItem('access');
    localStorage.removeItem('refresh');
    localStorage.removeItem('selectedStatusesClass');
    localStorage.removeItem('selectedStatusExam');
    localStorage.removeItem('selectedTimeFilter');
    localStorage.removeItem('colorPicker');
    localStorage.removeItem('load_value');
    localStorage.removeItem('dateFilter');
    localStorage.removeItem('refreshPageTime');
    localStorage.removeItem('activeTabState');

    location.reload();
}

function clearAllCache() {

  showLoader(true);
    // Clear all stored data
    localStorage.removeItem('userDetails');
    localStorage.removeItem('access');
    localStorage.removeItem('refresh');
    localStorage.removeItem('selectedStatusesClass');
    localStorage.removeItem('selectedStatusExam');
    localStorage.removeItem('selectedTimeFilter');
    localStorage.removeItem('colorPicker');
    localStorage.removeItem('load_value');
    localStorage.removeItem('dateFilter');
    localStorage.removeItem('refreshPageTime');
    localStorage.removeItem('activeTabState');

    showLoader(false);

    location.reload();
}

// Main function to initialize the script
window.initializeAuth = function () {
  addDynamicStyles();
  checkSession();
};
