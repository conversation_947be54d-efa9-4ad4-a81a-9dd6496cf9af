/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[2],{319:function(ha,ea,f){(function(f){(function(f,ba){ha.exports=ba()})("undefined"!==typeof self?self:this,function(){return function(f){function z(ca){if(da[ca])return da[ca].exports;var y=da[ca]={i:ca,l:!1,exports:{}};f[ca].call(y.exports,y,y.exports,z);y.l=!0;return y.exports}var da={};z.m=f;z.c=da;z.d=function(f,y,x){z.o(f,y)||Object.defineProperty(f,y,{configurable:!1,enumerable:!0,get:x})};z.n=function(f){var y=f&&f.__esModule?
function(){return f["default"]}:function(){return f};z.d(y,"a",y);return y};z.o=function(f,y){return Object.prototype.hasOwnProperty.call(f,y)};z.p="";return z(z.s=109)}([function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});f=da(17);var z=da(18),y=da(19),x=da(45),w=da(46),e=da(47),h=da(48),r=da(49),aa=da(12),fa=da(32),n=da(33),ea=da(31);da=da(1);ba.default={Scope:da.Scope,create:da.create,find:da.find,query:da.query,register:da.register,Container:f.default,Format:z.default,Leaf:y.default,
Embed:h.default,Scroll:x.default,Block:e.default,Inline:w.default,Text:r.default,Attributor:{Attribute:aa.default,Class:fa.default,Style:n.default,Store:ea.default}}},function(f,ba){function z(e,f){void 0===f&&(f=!1);return null==e?null:null!=e[ba.DATA_KEY]?e[ba.DATA_KEY].blot:f?z(e.parentNode,f):null}function ca(f,w){void 0===w&&(w=fa.ANY);if("string"===typeof f)var n=aa[f]||e[f];else if(f instanceof Text||f.nodeType===Node.TEXT_NODE)n=aa.text;else if("number"===typeof f)f&fa.LEVEL&fa.BLOCK?n=aa.block:
f&fa.LEVEL&fa.INLINE&&(n=aa.inline);else if(f instanceof HTMLElement){var x=(f.getAttribute("class")||"").split(/\s+/),y;for(y in x)if(n=h[x[y]])break;n=n||r[f.tagName]}return null==n?null:w&fa.LEVEL&n.scope&&w&fa.TYPE&n.scope?n:null}function y(){for(var f=[],x=0;x<arguments.length;x++)f[x]=arguments[x];if(1<f.length)return f.map(function(e){return y(e)});var z=f[0];if("string"!==typeof z.blotName&&"string"!==typeof z.attrName)throw new w("Invalid definition");if("abstract"===z.blotName)throw new w("Cannot register abstract class");
aa[z.blotName||z.attrName]=z;"string"===typeof z.keyName?e[z.keyName]=z:(null!=z.className&&(h[z.className]=z),null!=z.tagName&&(Array.isArray(z.tagName)?z.tagName=z.tagName.map(function(e){return e.toUpperCase()}):z.tagName=z.tagName.toUpperCase(),(Array.isArray(z.tagName)?z.tagName:[z.tagName]).forEach(function(e){if(null==r[e]||null==z.className)r[e]=z})));return z}var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,f){e.__proto__=f}||
function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(f,h){function n(){this.constructor=f}e(f,h);f.prototype=null===h?Object.create(h):(n.prototype=h.prototype,new n)}}();Object.defineProperty(ba,"__esModule",{value:!0});var w=function(e){function f(f){f="[Parchment] "+f;var h=e.call(this,f)||this;h.message=f;h.name=h.constructor.name;return h}x(f,e);return f}(Error);ba.ParchmentError=w;var e={},h={},r={},aa={};ba.DATA_KEY="__blot";var fa;(function(e){e[e.TYPE=3]="TYPE";
e[e.LEVEL=12]="LEVEL";e[e.ATTRIBUTE=13]="ATTRIBUTE";e[e.BLOT=14]="BLOT";e[e.INLINE=7]="INLINE";e[e.BLOCK=11]="BLOCK";e[e.BLOCK_BLOT=10]="BLOCK_BLOT";e[e.INLINE_BLOT=6]="INLINE_BLOT";e[e.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE";e[e.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE";e[e.ANY=15]="ANY"})(fa=ba.Scope||(ba.Scope={}));ba.create=function(e,f){var h=ca(e);if(null==h)throw new w("Unable to create "+e+" blot");e=e instanceof Node||e.nodeType===Node.TEXT_NODE?e:h.create(f);return new h(e,f)};ba.find=z;ba.query=
ca;ba.register=y},function(f,ba,da){function z(e){Array.isArray(e)?this.ops=e:null!=e&&Array.isArray(e.ops)?this.ops=e.ops:this.ops=[]}var y=da(51),x=da(11),w=da(3),e=da(20),h=String.fromCharCode(0);z.prototype.insert=function(e,f){var h={};if(0===e.length)return this;h.insert=e;null!=f&&"object"===typeof f&&0<Object.keys(f).length&&(h.attributes=f);return this.push(h)};z.prototype["delete"]=function(e){return 0>=e?this:this.push({"delete":e})};z.prototype.retain=function(e,f){if(0>=e)return this;
e={retain:e};null!=f&&"object"===typeof f&&0<Object.keys(f).length&&(e.attributes=f);return this.push(e)};z.prototype.push=function(e){var f=this.ops.length,h=this.ops[f-1];e=w(!0,{},e);if("object"===typeof h){if("number"===typeof e["delete"]&&"number"===typeof h["delete"])return this.ops[f-1]={"delete":h["delete"]+e["delete"]},this;if("number"===typeof h["delete"]&&null!=e.insert&&(--f,h=this.ops[f-1],"object"!==typeof h))return this.ops.unshift(e),this;if(x(e.attributes,h.attributes)){if("string"===
typeof e.insert&&"string"===typeof h.insert)return this.ops[f-1]={insert:h.insert+e.insert},"object"===typeof e.attributes&&(this.ops[f-1].attributes=e.attributes),this;if("number"===typeof e.retain&&"number"===typeof h.retain)return this.ops[f-1]={retain:h.retain+e.retain},"object"===typeof e.attributes&&(this.ops[f-1].attributes=e.attributes),this}}f===this.ops.length?this.ops.push(e):this.ops.splice(f,0,e);return this};z.prototype.chop=function(){var e=this.ops[this.ops.length-1];e&&e.retain&&
!e.attributes&&this.ops.pop();return this};z.prototype.filter=function(e){return this.ops.filter(e)};z.prototype.forEach=function(e){this.ops.forEach(e)};z.prototype.map=function(e){return this.ops.map(e)};z.prototype.partition=function(e){var f=[],h=[];this.forEach(function(n){(e(n)?f:h).push(n)});return[f,h]};z.prototype.reduce=function(e,f){return this.ops.reduce(e,f)};z.prototype.changeLength=function(){return this.reduce(function(f,h){return h.insert?f+e.length(h):h.delete?f-h.delete:f},0)};
z.prototype.length=function(){return this.reduce(function(f,h){return f+e.length(h)},0)};z.prototype.slice=function(f,h){f=f||0;"number"!==typeof h&&(h=Infinity);for(var r=[],n=e.iterator(this.ops),w=0;w<h&&n.hasNext();){if(w<f)var x=n.next(f-w);else x=n.next(h-w),r.push(x);w+=e.length(x)}return new z(r)};z.prototype.compose=function(f){var h=e.iterator(this.ops);f=e.iterator(f.ops);var r=[],n=f.peek();if(null!=n&&"number"===typeof n.retain&&null==n.attributes){for(var w=n.retain;"insert"===h.peekType()&&
h.peekLength()<=w;)w-=h.peekLength(),r.push(h.next());0<n.retain-w&&f.next(n.retain-w)}for(r=new z(r);h.hasNext()||f.hasNext();)if("insert"===f.peekType())r.push(f.next());else if("delete"===h.peekType())r.push(h.next());else{w=Math.min(h.peekLength(),f.peekLength());var y=h.next(w),ca=f.next(w);if("number"===typeof ca.retain){n={};"number"===typeof y.retain?n.retain=w:n.insert=y.insert;if(w=e.attributes.compose(y.attributes,ca.attributes,"number"===typeof y.retain))n.attributes=w;r.push(n);if(!f.hasNext()&&
x(r.ops[r.ops.length-1],n))return h=new z(h.rest()),r.concat(h).chop()}else"number"===typeof ca["delete"]&&"number"===typeof y.retain&&r.push(ca)}return r.chop()};z.prototype.concat=function(e){var f=new z(this.ops.slice());0<e.ops.length&&(f.push(e.ops[0]),f.ops=f.ops.concat(e.ops.slice(1)));return f};z.prototype.diff=function(f,w){if(this.ops===f.ops)return new z;var r=[this,f].map(function(e){return e.map(function(n){if(null!=n.insert)return"string"===typeof n.insert?n.insert:h;throw Error("diff() called "+
(e===f?"on":"with")+" non-document");}).join("")}),n=new z;w=y(r[0],r[1],w);var aa=e.iterator(this.ops),ca=e.iterator(f.ops);w.forEach(function(f){for(var h=f[1].length;0<h;){var r=0;switch(f[0]){case y.INSERT:r=Math.min(ca.peekLength(),h);n.push(ca.next(r));break;case y.DELETE:r=Math.min(h,aa.peekLength());aa.next(r);n["delete"](r);break;case y.EQUAL:r=Math.min(aa.peekLength(),ca.peekLength(),h);var w=aa.next(r),z=ca.next(r);if(x(w.insert,z.insert))n.retain(r,e.attributes.diff(w.attributes,z.attributes));
else n.push(z)["delete"](r)}h-=r}});return n.chop()};z.prototype.eachLine=function(f,h){h=h||"\n";for(var r=e.iterator(this.ops),n=new z,w=0;r.hasNext();){if("insert"!==r.peekType())return;var x=r.peek(),aa=e.length(x)-r.peekLength();x="string"===typeof x.insert?x.insert.indexOf(h,aa)-aa:-1;if(0>x)n.push(r.next());else if(0<x)n.push(r.next(x));else{if(!1===f(n,r.next(1).attributes||{},w))return;w+=1;n=new z}}0<n.length()&&f(n,{},w)};z.prototype.transform=function(f,h){h=!!h;if("number"===typeof f)return this.transformPosition(f,
h);var r=e.iterator(this.ops);f=e.iterator(f.ops);for(var n=new z;r.hasNext()||f.hasNext();)if("insert"!==r.peekType()||!h&&"insert"===f.peekType())if("insert"===f.peekType())n.push(f.next());else{var w=Math.min(r.peekLength(),f.peekLength()),x=r.next(w),aa=f.next(w);x["delete"]||(aa["delete"]?n.push(aa):n.retain(w,e.attributes.transform(x.attributes,aa.attributes,h)))}else n.retain(e.length(r.next()));return n.chop()};z.prototype.transformPosition=function(f,h){h=!!h;for(var r=e.iterator(this.ops),
n=0;r.hasNext()&&n<=f;){var w=r.peekLength(),x=r.peekType();r.next();"delete"===x?f-=Math.min(w,f-n):("insert"===x&&(n<f||!h)&&(f+=w),n+=w)}return f};f.exports=z},function(f){function z(e,f){if("__proto__"===f){if(!x.call(e,f))return;if(h)return h(e,f).value}return e[f]}function da(f,h){e&&"__proto__"===h.name?e(f,h.name,{enumerable:!0,configurable:!0,value:h.newValue,writable:!0}):f[h.name]=h.newValue}function ca(e){if(!e||"[object Object]"!==w.call(e))return!1;var f=x.call(e,"constructor"),h=e.constructor&&
e.constructor.prototype&&x.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!f&&!h)return!1;for(var n in e);return"undefined"===typeof n||x.call(e,n)}function y(e){return"function"===typeof Array.isArray?Array.isArray(e):"[object Array]"===w.call(e)}var x=Object.prototype.hasOwnProperty,w=Object.prototype.toString,e=Object.defineProperty,h=Object.getOwnPropertyDescriptor;f.exports=function aa(){var e,f,h=arguments[0],w=1,x=arguments.length,ba=!1;"boolean"===typeof h&&(ba=h,h=arguments[1]||
{},w=2);if(null==h||"object"!==typeof h&&"function"!==typeof h)h={};for(;w<x;++w){var fa=arguments[w];if(null!=fa)for(e in fa){var ea=z(h,e);var ha=z(fa,e);h!==ha&&(ba&&ha&&(ca(ha)||(f=y(ha)))?(f?(f=!1,ea=ea&&y(ea)?ea:[]):ea=ea&&ca(ea)?ea:{},da(h,{name:e,newValue:aa(ba,ea,ha)})):"undefined"!==typeof ha&&da(h,{name:e,newValue:ha}))}}return h}},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");
}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function e(f){var h=
1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(null==f)return h;"function"===typeof f.formats&&(h=(0,aa.default)(h,f.formats()));return null==f.parent||"scroll"==f.parent.blotName||f.parent.statics.scope!==f.statics.scope?h:e(f.parent,h)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.BlockEmbed=ba.bubbleFormats=void 0;var h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,
n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),r=function ra(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ra(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};f=da(3);var aa=z(f);f=da(2);var fa=z(f);f=da(0);var n=z(f);f=da(16);var ea=z(f);f=da(6);f=z(f);da=da(7);da=z(da);var ha=function(e){function f(){y(this,f);return x(this,(f.__proto__||
Object.getPrototypeOf(f)).apply(this,arguments))}w(f,e);h(f,[{key:"attach",value:function(){r(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"attach",this).call(this);this.attributes=new n.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new fa.default).insert(this.value(),(0,aa.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(e,f){e=n.default.query(e,n.default.Scope.BLOCK_ATTRIBUTE);null!=e&&this.attributes.attribute(e,f)}},
{key:"formatAt",value:function(e,f,h,n){this.format(h,n)}},{key:"insertAt",value:function(e,h,w){"string"===typeof h&&h.endsWith("\n")?(w=n.default.create(oa.blotName),this.parent.insertBefore(w,0===e?this:this.next),w.insertAt(0,h.slice(0,-1))):r(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"insertAt",this).call(this,e,h,w)}}]);return f}(n.default.Embed);ha.scope=n.default.Scope.BLOCK_BLOT;var oa=function(f){function aa(e){y(this,aa);e=x(this,(aa.__proto__||Object.getPrototypeOf(aa)).call(this,
e));e.cache={};return e}w(aa,f);h(aa,[{key:"delta",value:function(){null==this.cache.delta&&(this.cache.delta=this.descendants(n.default.Leaf).reduce(function(f,h){return 0===h.length()?f:f.insert(h.value(),e(h))},new fa.default).insert("\n",e(this)));return this.cache.delta}},{key:"deleteAt",value:function(e,f){r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"deleteAt",this).call(this,e,f);this.cache={}}},{key:"formatAt",value:function(e,f,h,w){0>=f||(n.default.query(h,n.default.Scope.BLOCK)?
e+f===this.length()&&this.format(h,w):r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"formatAt",this).call(this,e,Math.min(f,this.length()-e-1),h,w),this.cache={})}},{key:"insertAt",value:function(e,f,h){if(null!=h)return r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"insertAt",this).call(this,e,f,h);if(0!==f.length){f=f.split("\n");h=f.shift();0<h.length&&(e<this.length()-1||null==this.children.tail?r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"insertAt",
this).call(this,Math.min(e,this.length()-1),h):this.children.tail.insertAt(this.children.tail.length(),h),this.cache={});var n=this;f.reduce(function(e,f){n=n.split(e,!0);n.insertAt(0,f);return f.length},e+h.length)}}},{key:"insertBefore",value:function(e,f){var h=this.children.head;r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"insertBefore",this).call(this,e,f);h instanceof ea.default&&h.remove();this.cache={}}},{key:"length",value:function(){null==this.cache.length&&(this.cache.length=
r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"length",this).call(this)+1);return this.cache.length}},{key:"moveChildren",value:function(e,f){r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"moveChildren",this).call(this,e,f);this.cache={}}},{key:"optimize",value:function(e){r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"optimize",this).call(this,e);this.cache={}}},{key:"path",value:function(e){return r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),
"path",this).call(this,e,!0)}},{key:"removeChild",value:function(e){r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"removeChild",this).call(this,e);this.cache={}}},{key:"split",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!1;if(f&&(0===e||e>=this.length()-1)){f=this.clone();if(0===e)return this.parent.insertBefore(f,this),this;this.parent.insertBefore(f,this.next);return f}f=r(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"split",this).call(this,
e,f);this.cache={};return f}}]);return aa}(n.default.Block);oa.blotName="block";oa.tagName="P";oa.defaultChild="break";oa.allowedChildren=[f.default,n.default.Embed,da.default];ba.bubbleFormats=e;ba.BlockEmbed=ha;ba.default=oa},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f,h){f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}function x(e,f){f=(0,ua.default)(!0,{container:e,modules:{clipboard:!0,keyboard:!0,history:!0}},
f);if(f.theme&&f.theme!==la.DEFAULTS.theme){if(f.theme=la.import("themes/"+f.theme),null==f.theme)throw Error("Invalid theme "+f.theme+". Did you register it?");}else f.theme=Ba.default;e=(0,ua.default)(!0,{},f.theme.DEFAULTS);[e,f].forEach(function(e){e.modules=e.modules||{};Object.keys(e.modules).forEach(function(f){!0===e.modules[f]&&(e.modules[f]={})})});var h=Object.keys(e.modules).concat(Object.keys(f.modules)).reduce(function(e,f){var h=la.import("modules/"+f);null==h?ma.error("Cannot load "+
f+" module. Are you sure you registered it?"):e[f]=h.DEFAULTS||{};return e},{});null!=f.modules&&f.modules.toolbar&&f.modules.toolbar.constructor!==Object&&(f.modules.toolbar={container:f.modules.toolbar});f=(0,ua.default)(!0,{},la.DEFAULTS,{modules:h},e,f);["bounds","container","scrollingContainer"].forEach(function(e){"string"===typeof f[e]&&(f[e]=document.querySelector(f[e]))});f.modules=Object.keys(f.modules).reduce(function(e,h){f.modules[h]&&(e[h]=f.modules[h]);return e},{});return f}function w(e,
f,r,w){if(this.options.strict&&!this.isEnabled()&&f===ha.default.sources.USER)return new n.default;var x=null==r?null:this.getSelection(),aa=this.editor.delta;e=e();null!=x&&(!0===r&&(r=x.index),null==w?x=h(x,e,f):0!==w&&(x=h(x,r,w,f)),this.setSelection(x,ha.default.sources.SILENT));if(0<e.length()){var y;r=[ha.default.events.TEXT_CHANGE,e,aa,f];(y=this.emitter).emit.apply(y,[ha.default.events.EDITOR_CHANGE].concat(r));if(f!==ha.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,r)}}return e}
function e(e,f,h,n,w){var x={};"number"===typeof e.index&&"number"===typeof e.length?("number"!==typeof f?(w=n,n=h,h=f,f=e.length):f=e.length,e=e.index):"number"!==typeof f&&(w=n,n=h,h=f,f=0);"object"===("undefined"===typeof h?"undefined":r(h))?(x=h,w=n):"string"===typeof h&&(null!=n?x[h]=n:w=h);w=w||ha.default.sources.API;return[e,f,x,w]}function h(e,f,h,r){if(null==e)return null;var w=void 0,x=void 0;f instanceof n.default?(w=[e.index,e.index+e.length].map(function(e){return f.transformPosition(e,
r!==ha.default.sources.USER)}),e=aa(w,2),w=e[0],x=e[1]):(w=[e.index,e.index+e.length].map(function(e){return e<f||e===f&&r===ha.default.sources.USER?e:0<=h?e+h:Math.max(f,e+h)}),e=aa(w,2),w=e[0],x=e[1]);return new ka.Range(w,x-w)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.overload=ba.expandConfig=void 0;var r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==
Symbol.prototype?"symbol":typeof e},aa=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!f||h.length!==f);n=!0);}catch(Ea){r=!0,w=Ea}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),fa=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=
f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();da(50);f=da(2);var n=z(f);f=da(14);var ea=z(f);f=da(8);var ha=z(f);f=da(9);f=z(f);var oa=da(0),qa=z(oa),ka=da(15),ta=z(ka);oa=da(3);var ua=z(oa);oa=da(10);var ra=z(oa);da=da(34);var Ba=z(da),ma=(0,ra.default)("quill"),la=function(){function f(e){var h=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(this instanceof
f))throw new TypeError("Cannot call a class as a function");this.options=x(e,n);this.container=this.options.container;if(null==this.container)return ma.error("Invalid Quill container",e);this.options.debug&&f.debug(this.options.debug);n=this.container.innerHTML.trim();this.container.classList.add("ql-container");this.container.innerHTML="";this.container.__quill=this;this.root=this.addContainer("ql-editor");this.root.classList.add("ql-blank");this.root.setAttribute("data-gramm",!1);this.scrollingContainer=
this.options.scrollingContainer||this.root;this.emitter=new ha.default;this.scroll=qa.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats});this.editor=new ea.default(this.scroll);this.selection=new ta.default(this.scroll,this.emitter);this.theme=new this.options.theme(this,this.options);this.keyboard=this.theme.addModule("keyboard");this.clipboard=this.theme.addModule("clipboard");this.history=this.theme.addModule("history");this.theme.init();this.emitter.on(ha.default.events.EDITOR_CHANGE,
function(e){e===ha.default.events.TEXT_CHANGE&&h.root.classList.toggle("ql-blank",h.editor.isBlank())});this.emitter.on(ha.default.events.SCROLL_UPDATE,function(e,f){var n=h.selection.lastRange,r=n&&0===n.length?n.index:void 0;w.call(h,function(){return h.editor.update(null,f,r)},e)});n=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+n+"<p><br></p></div>");this.setContents(n);this.history.clear();this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder);
this.options.readOnly&&this.disable()}fa(f,null,[{key:"debug",value:function(e){!0===e&&(e="log");ra.default.level(e)}},{key:"find",value:function(e){return e.__quill||qa.default.find(e)}},{key:"import",value:function(e){null==this.imports[e]&&ma.error("Cannot import "+e+". Are you sure it was registered?");return this.imports[e]}},{key:"register",value:function(e,f){var h=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:!1;"string"!==typeof e?(n=e.attrName||e.blotName,"string"===typeof n?
this.register("formats/"+n,e,f):Object.keys(e).forEach(function(n){h.register(n,e[n],f)})):(null==this.imports[e]||n||ma.warn("Overwriting "+e+" with",f),this.imports[e]=f,(e.startsWith("blots/")||e.startsWith("formats/"))&&"abstract"!==f.blotName?qa.default.register(f):e.startsWith("modules")&&"function"===typeof f.register&&f.register())}}]);fa(f,[{key:"addContainer",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if("string"===typeof e){var h=e;e=document.createElement("div");
e.classList.add(h)}this.container.insertBefore(e,f);return e}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(f,h,n){var r=this;n=e(f,h,n);n=aa(n,4);f=n[0];h=n[1];n=n[3];return w.call(this,function(){return r.editor.deleteText(f,h)},n,f,-1*h)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:!0;this.scroll.enable(e);this.container.classList.toggle("ql-disabled",
!e)}},{key:"focus",value:function(){var e=this.scrollingContainer.scrollTop;this.selection.focus();this.scrollingContainer.scrollTop=e;this.scrollIntoView()}},{key:"format",value:function(e,f){var h=this;return w.call(this,function(){var r=h.getSelection(!0),w=new n.default;if(null==r)return w;if(qa.default.query(e,qa.default.Scope.BLOCK))w=h.editor.formatLine(r.index,r.length,y({},e,f));else{if(0===r.length)return h.selection.format(e,f),w;w=h.editor.formatText(r.index,r.length,y({},e,f))}h.setSelection(r,
ha.default.sources.SILENT);return w},2<arguments.length&&void 0!==arguments[2]?arguments[2]:ha.default.sources.API)}},{key:"formatLine",value:function(f,h,n,r,x){var y=this,z=void 0;n=e(f,h,n,r,x);n=aa(n,4);f=n[0];h=n[1];z=n[2];x=n[3];return w.call(this,function(){return y.editor.formatLine(f,h,z)},x,f,0)}},{key:"formatText",value:function(f,h,n,r,x){var y=this,z=void 0;n=e(f,h,n,r,x);n=aa(n,4);f=n[0];h=n[1];z=n[2];x=n[3];return w.call(this,function(){return y.editor.formatText(f,h,z)},x,f,0)}},{key:"getBounds",
value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;f="number"===typeof e?this.selection.getBounds(e,f):this.selection.getBounds(e.index,e.length);var h=this.container.getBoundingClientRect();return{bottom:f.bottom-h.top,height:f.height,left:f.left-h.left,right:f.right-h.left,top:f.top-h.top,width:f.width}}},{key:"getContents",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,h=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.getLength()-
f;f=e(f,h);h=aa(f,2);f=h[0];h=h[1];return this.editor.getContents(f,h)}},{key:"getFormat",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return"number"===typeof e?this.editor.getFormat(e,f):this.editor.getFormat(e.index,e.length)}},{key:"getIndex",value:function(e){return e.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(e){return this.scroll.leaf(e)}},
{key:"getLine",value:function(e){return this.scroll.line(e)}},{key:"getLines",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!==typeof e?this.scroll.lines(e.index,e.length):this.scroll.lines(e,f)}},{key:"getModule",value:function(e){return this.theme.modules[e]}},{key:"getSelection",value:function(){0<arguments.length&&void 0!==arguments[0]&&arguments[0]&&this.focus();this.update();
return this.selection.getRange()[0]}},{key:"getText",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,h=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.getLength()-f;f=e(f,h);h=aa(f,2);f=h[0];h=h[1];return this.editor.getText(f,h)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(e,h,n){var r=this;return w.call(this,function(){return r.editor.insertEmbed(e,h,n)},3<arguments.length&&void 0!==arguments[3]?
arguments[3]:f.sources.API,e)}},{key:"insertText",value:function(f,h,n,r,x){var y=this,z=void 0;n=e(f,0,n,r,x);n=aa(n,4);f=n[0];z=n[2];x=n[3];return w.call(this,function(){return y.editor.insertText(f,h,z)},x,f,h.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,
arguments)}},{key:"pasteHTML",value:function(e,f,h){this.clipboard.dangerouslyPasteHTML(e,f,h)}},{key:"removeFormat",value:function(f,h,n){var r=this;n=e(f,h,n);n=aa(n,4);f=n[0];h=n[1];n=n[3];return w.call(this,function(){return r.editor.removeFormat(f,h)},n,f)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(e){var f=this;return w.call(this,function(){e=new n.default(e);var h=f.getLength();h=f.editor.deleteText(0,
h);var r=f.editor.applyDelta(e),w=r.ops[r.ops.length-1];null!=w&&"string"===typeof w.insert&&"\n"===w.insert[w.insert.length-1]&&(f.editor.deleteText(f.getLength()-1,1),r.delete(1));return h.compose(r)},1<arguments.length&&void 0!==arguments[1]?arguments[1]:ha.default.sources.API)}},{key:"setSelection",value:function(h,n,r){null==h?this.selection.setRange(null,n||f.sources.API):(h=e(h,n,r),r=aa(h,4),h=r[0],n=r[1],r=r[3],this.selection.setRange(new ka.Range(h,n),r),r!==ha.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer))}},
{key:"setText",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:ha.default.sources.API,h=(new n.default).insert(e);return this.setContents(h,f)}},{key:"update",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:ha.default.sources.USER,f=this.scroll.update(e);this.selection.update(e);return f}},{key:"updateContents",value:function(e){var f=this,h=1<arguments.length&&void 0!==arguments[1]?arguments[1]:ha.default.sources.API;return w.call(this,
function(){e=new n.default(e);return f.editor.applyDelta(e,h)},h,!0)}}]);return f}();la.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"};la.events=ha.default.events;la.sources=ha.default.sources;la.version="1.3.7";la.imports={delta:n.default,parchment:qa.default,"core/module":f.default,"core/theme":Ba.default};ba.expandConfig=x;ba.overload=e;ba.default=la},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==
f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,
r,w){r&&e(f.prototype,r);w&&e(f,w);return f}}(),x=function ja(f,r,w){null===f&&(f=Function.prototype);var h=Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===r?void 0:r.call(w)}};f=(f=da(7))&&f.__esModule?f:{default:f};var w=(da=da(0))&&da.__esModule?da:{default:da};da=function(f){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var f=(h.__proto__||
Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(h,f);y(h,[{key:"formatAt",value:function(f,r,n,y){0>h.compare(this.statics.blotName,n)&&w.default.query(n,w.default.Scope.BLOT)?(f=this.isolate(f,r),y&&f.wrap(n,y)):x(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"formatAt",this).call(this,f,r,n,y)}},{key:"optimize",value:function(f){x(h.prototype.__proto__||
Object.getPrototypeOf(h.prototype),"optimize",this).call(this,f);this.parent instanceof h&&0<h.compare(this.statics.blotName,this.parent.statics.blotName)&&(f=this.parent.isolate(this.offset(),this.length()),this.moveChildren(f),f.wrap(this))}}],[{key:"compare",value:function(f,r){var n=h.order.indexOf(f),w=h.order.indexOf(r);return 0<=n||0<=w?n-w:f===r?0:f<r?-1:1}}]);return h}(w.default.Inline);da.allowedChildren=[da,w.default.Embed,f.default];da.order="cursor inline underline strike italic bold script link code".split(" ");
ba.default=da},function(f,ba,da){function z(f,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+typeof x);f.prototype=Object.create(x&&x.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(f,x):f.__proto__=x)}Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function x(){if(!(this instanceof x))throw new TypeError("Cannot call a class as a function");
var f=(x.__proto__||Object.getPrototypeOf(x)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(x,f);return x}(function(f){return f&&f.__esModule?f:{default:f}}(da(0)).default.Text);ba.default=f},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&
f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,r,w){r&&e(f.prototype,r);w&&e(f,w);return f}}(),x=function ja(f,r,w){null===f&&(f=Function.prototype);var h=
Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===r?void 0:r.call(w)}};f=(f=da(54))&&f.__esModule?f:{default:f};da=(da=da(10))&&da.__esModule?da:{default:da};var w=(0,da.default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(function(f){document.addEventListener(f,function(){for(var f=arguments.length,h=Array(f),w=0;w<f;w++)h[w]=arguments[w];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(f){if(f.__quill&&
f.__quill.emitter){var n;(n=f.__quill.emitter).handleDOM.apply(n,h)}})})});da=function(f){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var f=(h.__proto__||Object.getPrototypeOf(h)).call(this);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");f=!f||"object"!==typeof f&&"function"!==typeof f?this:f;f.listeners={};f.on("error",w.error);return f}z(h,f);y(h,[{key:"emit",value:function(){w.log.apply(w,arguments);
x(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(f){for(var h=arguments.length,n=Array(1<h?h-1:0),r=1;r<h;r++)n[r-1]=arguments[r];(this.listeners[f.type]||[]).forEach(function(h){var r=h.node;h=h.handler;(f.target===r||r.contains(f.target))&&h.apply(void 0,[f].concat(n))})}},{key:"listenDOM",value:function(f,h,n){this.listeners[f]||(this.listeners[f]=[]);this.listeners[f].push({node:h,handler:n})}}]);return h}(f.default);
da.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"};da.sources={API:"api",SILENT:"silent",USER:"user"};ba.default=da},function(f,ba){Object.defineProperty(ba,"__esModule",{value:!0});f=function y(f){var x=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(this instanceof y))throw new TypeError("Cannot call a class as a function");
this.quill=f;this.options=x};f.DEFAULTS={};ba.default=f},function(f,ba){function z(f){if(y.indexOf(f)<=y.indexOf(x)){for(var e,h=arguments.length,r=Array(1<h?h-1:0),w=1;w<h;w++)r[w-1]=arguments[w];(e=console)[f].apply(e,r)}}function ca(f){return y.reduce(function(e,h){e[h]=z.bind(console,h,f);return e},{})}Object.defineProperty(ba,"__esModule",{value:!0});var y=["error","warn","log","info"],x="warn";z.level=ca.level=function(f){x=f};ba.default=ca},function(f,ba,da){function z(e){return!e||"object"!==
typeof e||"number"!==typeof e.length||"function"!==typeof e.copy||"function"!==typeof e.slice||0<e.length&&"number"!==typeof e[0]?!1:!0}function y(f,y,ca){var n;if(null===f||void 0===f||null===y||void 0===y||f.prototype!==y.prototype)return!1;if(e(f)){if(!e(y))return!1;f=x.call(f);y=x.call(y);return h(f,y,ca)}if(z(f)){if(!z(y)||f.length!==y.length)return!1;for(n=0;n<f.length;n++)if(f[n]!==y[n])return!1;return!0}try{var r=w(f);var aa=w(y)}catch(oa){return!1}if(r.length!=aa.length)return!1;r.sort();
aa.sort();for(n=r.length-1;0<=n;n--)if(r[n]!=aa[n])return!1;for(n=r.length-1;0<=n;n--)if(aa=r[n],!h(f[aa],y[aa],ca))return!1;return typeof f===typeof y}var x=Array.prototype.slice,w=da(52),e=da(53),h=f.exports=function(e,f,h){h||(h={});return e===f?!0:e instanceof Date&&f instanceof Date?e.getTime()===f.getTime():!e||!f||"object"!=typeof e&&"object"!=typeof f?h.strict?e===f:e==f:y(e,f,h)}},function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});var z=da(1);f=function(){function f(f,w,
e){void 0===e&&(e={});this.attrName=f;this.keyName=w;f=z.Scope.TYPE&z.Scope.ATTRIBUTE;this.scope=null!=e.scope?e.scope&z.Scope.LEVEL|f:z.Scope.ATTRIBUTE;null!=e.whitelist&&(this.whitelist=e.whitelist)}f.keys=function(f){return[].map.call(f.attributes,function(f){return f.name})};f.prototype.add=function(f,w){if(!this.canAdd(f,w))return!1;f.setAttribute(this.keyName,w);return!0};f.prototype.canAdd=function(f,w){return null==z.query(f,z.Scope.BLOT&(this.scope|z.Scope.TYPE))?!1:null==this.whitelist?
!0:"string"===typeof w?-1<this.whitelist.indexOf(w.replace(/["']/g,"")):-1<this.whitelist.indexOf(w)};f.prototype.remove=function(f){f.removeAttribute(this.keyName)};f.prototype.value=function(f){var w=f.getAttribute(this.keyName);return this.canAdd(f,w)&&w?w:""};return f}();ba.default=f},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.Code=void 0;var e=function(){return function(e,f){if(Array.isArray(e))return e;
if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(ma){r=!0,w=ma}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,
n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),r=function ta(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ta(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};f=da(2);var aa=z(f);f=da(0);var fa=z(f);f=da(4);f=z(f);var n=da(6);n=z(n);da=da(7);var ea=z(da);da=function(e){function f(){y(this,f);return x(this,(f.__proto__||Object.getPrototypeOf(f)).apply(this,
arguments))}w(f,e);return f}(n.default);da.blotName="code";da.tagName="CODE";f=function(f){function n(){y(this,n);return x(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}w(n,f);h(n,[{key:"delta",value:function(){var e=this,f=this.domNode.textContent;f.endsWith("\n")&&(f=f.slice(0,-1));return f.split("\n").reduce(function(f,h){return f.insert(h).insert("\n",e.formats())},new aa.default)}},{key:"format",value:function(f,h){if(f!==this.statics.blotName||!h){var w=this.descendant(ea.default,
this.length()-1);w=e(w,1)[0];null!=w&&w.deleteAt(w.length()-1,1);r(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"format",this).call(this,f,h)}}},{key:"formatAt",value:function(e,f,h,r){if(0!==f&&null!=fa.default.query(h,fa.default.Scope.BLOCK)&&(h!==this.statics.blotName||r!==this.statics.formats(this.domNode))){var w=this.newlineIndex(e);if(!(0>w||w>=e+f)){var x=this.newlineIndex(e,!0)+1;w=w-x+1;var y=this.isolate(x,w),aa=y.next;y.format(h,r);aa instanceof n&&aa.formatAt(0,e-x+f-w,h,
r)}}}},{key:"insertAt",value:function(f,h,n){null==n&&(f=this.descendant(ea.default,f),f=e(f,2),f[0].insertAt(f[1],h))}},{key:"length",value:function(){var e=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?e:e+1}},{key:"newlineIndex",value:function(e){if(1<arguments.length&&void 0!==arguments[1]&&arguments[1])return this.domNode.textContent.slice(0,e).lastIndexOf("\n");var f=this.domNode.textContent.slice(e).indexOf("\n");return-1<f?e+f:-1}},{key:"optimize",value:function(e){this.domNode.textContent.endsWith("\n")||
this.appendChild(fa.default.create("text","\n"));r(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"optimize",this).call(this,e);var f=this.next;null!=f&&f.prev===this&&f.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===f.statics.formats(f.domNode)&&(f.optimize(e),f.moveChildren(this),f.remove())}},{key:"replace",value:function(e){r(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"replace",this).call(this,e);[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(e){var f=
fa.default.find(e);null==f?e.parentNode.removeChild(e):f instanceof fa.default.Embed?f.remove():f.unwrap()})}}],[{key:"create",value:function(e){e=r(n.__proto__||Object.getPrototypeOf(n),"create",this).call(this,e);e.setAttribute("spellcheck",!1);return e}},{key:"formats",value:function(){return!0}}]);return n}(f.default);f.blotName="code-block";f.tagName="PRE";f.TAB="  ";ba.Code=da;ba.default=f},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f,h){f in e?Object.defineProperty(e,
f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}function x(e,f){return Object.keys(f).reduce(function(h,n){if(null==e[n])return h;f[n]===e[n]?h[n]=f[n]:Array.isArray(f[n])?0>f[n].indexOf(e[n])&&(h[n]=f[n].concat([e[n]])):h[n]=[f[n],e[n]];return h},{})}function w(e){return e.reduce(function(e,f){if(1===f.insert){var h=(0,ta.default)(f.attributes);delete h.image;return e.insert({image:f.attributes.image},h)}null==f.attributes||!0!==f.attributes.list&&!0!==f.attributes.bullet||
(f=(0,ta.default)(f),f.attributes.list?f.attributes.list="ordered":(f.attributes.list="bullet",delete f.attributes.bullet));return"string"===typeof f.insert?(h=f.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.insert(h,f.attributes)):e.push(f)},new aa.default)}Object.defineProperty(ba,"__esModule",{value:!0});var e="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?
"symbol":typeof e},h=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(sa){r=!0,w=sa}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),r=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=
n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(2);var aa=z(f);f=da(20);var fa=z(f);f=da(0);var n=z(f);f=da(13);var ea=z(f);f=da(24);var ha=z(f),oa=da(4),qa=z(oa);f=da(16);var ka=z(f);f=da(21);var ta=z(f);f=da(11);var ua=z(f);da=da(3);var ra=z(da),Ba=/^[ -~]*$/;da=function(){function f(e){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");this.scroll=
e;this.delta=this.getDelta()}r(f,[{key:"applyDelta",value:function(f){var r=this,x=!1;this.scroll.update();var y=this.scroll.length();this.scroll.batchStart();f=w(f);f.reduce(function(f,w){var aa=w.retain||w.delete||w.insert.length||1,z=w.attributes||{};if(null!=w.insert){if("string"===typeof w.insert){w=w.insert;w.endsWith("\n")&&x&&(x=!1,w=w.slice(0,-1));f>=y&&!w.endsWith("\n")&&(x=!0);r.scroll.insertAt(f,w);w=r.scroll.line(f);w=h(w,2);var ca=w[0],da=w[1];w=(0,ra.default)({},(0,oa.bubbleFormats)(ca));
ca instanceof qa.default&&(ca=ca.descendant(n.default.Leaf,da),ca=h(ca,1)[0],w=(0,ra.default)(w,(0,oa.bubbleFormats)(ca)));z=fa.default.attributes.diff(w,z)||{}}else if("object"===e(w.insert)){ca=Object.keys(w.insert)[0];if(null==ca)return f;r.scroll.insertAt(f,ca,w.insert[ca])}y+=aa}Object.keys(z).forEach(function(e){r.scroll.formatAt(f,aa,e,z[e])});return f+aa},0);f.reduce(function(e,f){return"number"===typeof f.delete?(r.scroll.deleteAt(e,f.delete),e):e+(f.retain||f.insert.length||1)},0);this.scroll.batchEnd();
return this.update(f)}},{key:"deleteText",value:function(e,f){this.scroll.deleteAt(e,f);return this.update((new aa.default).retain(e).delete(f))}},{key:"formatLine",value:function(e,f){var h=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update();Object.keys(n).forEach(function(r){if(null==h.scroll.whitelist||h.scroll.whitelist[r]){var w=h.scroll.lines(e,Math.max(f,1)),x=f;w.forEach(function(f){var w=f.length();if(f instanceof ea.default){var y=e-f.offset(h.scroll),aa=
f.newlineIndex(y+x)-y+1;f.formatAt(y,aa,r,n[r])}else f.format(r,n[r]);x-=w})}});this.scroll.optimize();return this.update((new aa.default).retain(e).retain(f,(0,ta.default)(n)))}},{key:"formatText",value:function(e,f){var h=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Object.keys(n).forEach(function(r){h.scroll.formatAt(e,f,r,n[r])});return this.update((new aa.default).retain(e).retain(f,(0,ta.default)(n)))}},{key:"getContents",value:function(e,f){return this.delta.slice(e,e+f)}},
{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(e,f){return e.concat(f.delta())},new aa.default)}},{key:"getFormat",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,r=[],w=[];0===f?this.scroll.path(e).forEach(function(e){e=h(e,1)[0];e instanceof qa.default?r.push(e):e instanceof n.default.Leaf&&w.push(e)}):(r=this.scroll.lines(e,f),w=this.scroll.descendants(n.default.Leaf,e,f));f=[r,w].map(function(e){if(0===e.length)return{};for(var f=(0,
oa.bubbleFormats)(e.shift());0<Object.keys(f).length;){var h=e.shift();if(null==h)break;f=x((0,oa.bubbleFormats)(h),f)}return f});return ra.default.apply(ra.default,f)}},{key:"getText",value:function(e,f){return this.getContents(e,f).filter(function(e){return"string"===typeof e.insert}).map(function(e){return e.insert}).join("")}},{key:"insertEmbed",value:function(e,f,h){this.scroll.insertAt(e,f,h);return this.update((new aa.default).retain(e).insert(y({},f,h)))}},{key:"insertText",value:function(e,
f){var h=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};f=f.replace(/\r\n/g,"\n").replace(/\r/g,"\n");this.scroll.insertAt(e,f);Object.keys(n).forEach(function(r){h.scroll.formatAt(e,f.length,r,n[r])});return this.update((new aa.default).retain(e).insert(f,(0,ta.default)(n)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(1<this.scroll.children.length)return!1;var e=this.scroll.children.head;return e.statics.blotName!==qa.default.blotName||1<e.children.length?
!1:e.children.head instanceof ka.default}},{key:"removeFormat",value:function(e,f){var n=this.getText(e,f),r=this.scroll.line(e+f),w=h(r,2);r=w[0];w=w[1];var x=0,y=new aa.default;null!=r&&(x=r instanceof ea.default?r.newlineIndex(w)-w+1:r.length()-w,y=r.delta().slice(w,w+x-1).insert("\n"));f=this.getContents(e,f+x).diff((new aa.default).insert(n).concat(y));e=(new aa.default).retain(e).concat(f);return this.applyDelta(e)}},{key:"update",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?
arguments[1]:[],h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0,r=this.delta;if(1===f.length&&"characterData"===f[0].type&&f[0].target.data.match(Ba)&&n.default.find(f[0].target)){var w=n.default.find(f[0].target),x=(0,oa.bubbleFormats)(w),y=w.offset(this.scroll);f=f[0].oldValue.replace(ha.default.CONTENTS,"");f=(new aa.default).insert(f);w=(new aa.default).insert(w.value());e=(new aa.default).retain(y).concat(f.diff(w,h)).reduce(function(e,f){return f.insert?e.insert(f.insert,x):
e.push(f)},new aa.default);this.delta=r.compose(e)}else this.delta=this.getDelta(),e&&(0,ua.default)(r.compose(e),this.delta)||(e=r.diff(this.delta,h));return e}}]);return f}();ba.default=da},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e){if(Array.isArray(e)){for(var f=0,h=Array(e.length);f<e.length;f++)h[f]=e[f];return h}return Array.from(e)}function x(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function w(e,f){try{f.parentNode}catch(ka){return!1}f instanceof
Text&&(f=f.parentNode);return e.contains(f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.Range=void 0;var e=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(la){r=!0,w=la}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");
}}(),h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(0);var r=z(f);f=da(21);var aa=z(f);f=da(11);var fa=z(f);f=da(8);var n=z(f);da=da(10);da=z(da);var ea=(0,da.default)("quill:selection"),ha=function ka(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;x(this,ka);this.index=e;this.length=
f};da=function(){function f(e,h){var w=this;x(this,f);this.emitter=h;this.scroll=e;this.mouseDown=this.composing=!1;this.root=this.scroll.domNode;this.cursor=r.default.create("cursor",this);this.lastRange=this.savedRange=new ha(0,0);this.handleComposition();this.handleDragging();this.emitter.listenDOM("selectionchange",document,function(){w.mouseDown||setTimeout(w.update.bind(w,n.default.sources.USER),1)});this.emitter.on(n.default.events.EDITOR_CHANGE,function(e,f){e===n.default.events.TEXT_CHANGE&&
0<f.length()&&w.update(n.default.sources.SILENT)});this.emitter.on(n.default.events.SCROLL_BEFORE_UPDATE,function(){if(w.hasFocus()){var e=w.getNativeRange();if(null!=e&&e.start.node!==w.cursor.textNode)w.emitter.once(n.default.events.SCROLL_UPDATE,function(){try{w.setNativeRange(e.start.node,e.start.offset,e.end.node,e.end.offset)}catch(Ba){}})}});this.emitter.on(n.default.events.SCROLL_OPTIMIZE,function(e,f){f.range&&(e=f.range,w.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset))});
this.update(n.default.sources.SILENT)}h(f,[{key:"handleComposition",value:function(){var e=this;this.root.addEventListener("compositionstart",function(){e.composing=!0});this.root.addEventListener("compositionend",function(){e.composing=!1;if(e.cursor.parent){var f=e.cursor.restore();f&&setTimeout(function(){e.setNativeRange(f.startNode,f.startOffset,f.endNode,f.endOffset)},1)}})}},{key:"handleDragging",value:function(){var e=this;this.emitter.listenDOM("mousedown",document.body,function(){e.mouseDown=
!0});this.emitter.listenDOM("mouseup",document.body,function(){e.mouseDown=!1;e.update(n.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(e,f){if(null==this.scroll.whitelist||this.scroll.whitelist[e]){this.scroll.update();var h=this.getNativeRange();if(null!=h&&h.native.collapsed&&!r.default.query(e,r.default.Scope.BLOCK)){if(h.start.node!==this.cursor.textNode){var n=r.default.find(h.start.node,
!1);if(null==n)return;n instanceof r.default.Leaf?(h=n.split(h.start.offset),n.parent.insertBefore(this.cursor,h)):n.insertBefore(this.cursor,h.start.node);this.cursor.attach()}this.cursor.format(e,f);this.scroll.optimize();this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length);this.update()}}}},{key:"getBounds",value:function(f){var h=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();f=Math.min(f,n-1);h=Math.min(f+h,n-1)-f;n=this.scroll.leaf(f);var r=
e(n,2);n=r[0];var w=r[1];if(null==n)return null;r=n.position(w,!0);r=e(r,2);var x=r[0];w=r[1];r=document.createRange();if(0<h){r.setStart(x,w);h=this.scroll.leaf(f+h);h=e(h,2);n=h[0];w=h[1];if(null==n)return null;h=n.position(w,!0);h=e(h,2);x=h[0];w=h[1];r.setEnd(x,w);return r.getBoundingClientRect()}h="left";x instanceof Text?(w<x.data.length?(r.setStart(x,w),r.setEnd(x,w+1)):(r.setStart(x,w-1),r.setEnd(x,w),h="right"),n=r.getBoundingClientRect()):(n=n.domNode.getBoundingClientRect(),0<w&&(h="right"));
return{bottom:n.top+n.height,height:n.height,left:n[h],right:n[h],top:n.top,width:0}}},{key:"getNativeRange",value:function(){var e=document.getSelection();if(null==e||0>=e.rangeCount)return null;e=e.getRangeAt(0);if(null==e)return null;e=this.normalizeNative(e);ea.info("getNativeRange",e);return e}},{key:"getRange",value:function(){var e=this.getNativeRange();return null==e?[null,null]:[this.normalizedToRange(e),e]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",
value:function(f){var h=this,n=[[f.start.node,f.start.offset]];f.native.collapsed||n.push([f.end.node,f.end.offset]);n=n.map(function(f){var n=e(f,2);f=n[0];n=n[1];var w=r.default.find(f,!0),x=w.offset(h.scroll);return 0===n?x:w instanceof r.default.Container?x+w.length():x+w.index(f,n)});f=Math.min(Math.max.apply(Math,y(n)),this.scroll.length()-1);n=Math.min.apply(Math,[f].concat(y(n)));return new ha(n,f-n)}},{key:"normalizeNative",value:function(e){if(!w(this.root,e.startContainer)||!e.collapsed&&
!w(this.root,e.endContainer))return null;e={start:{node:e.startContainer,offset:e.startOffset},end:{node:e.endContainer,offset:e.endOffset},native:e};[e.start,e.end].forEach(function(e){for(var f=e.node,h=e.offset;!(f instanceof Text)&&0<f.childNodes.length;)if(f.childNodes.length>h)f=f.childNodes[h],h=0;else if(f.childNodes.length===h)f=f.lastChild,h=f instanceof Text?f.data.length:f.childNodes.length+1;else break;e.node=f;e.offset=h});return e}},{key:"rangeToNative",value:function(f){var h=this;
f=f.collapsed?[f.index]:[f.index,f.index+f.length];var n=[],r=this.scroll.length();f.forEach(function(f,w){f=Math.min(r-1,f);f=h.scroll.leaf(f);var x=e(f,2);f=x[1];w=x[0].position(f,0!==w);f=e(w,2);w=f[0];f=f[1];n.push(w,f)});2>n.length&&(n=n.concat(n));return n}},{key:"scrollIntoView",value:function(f){var h=this.lastRange;if(null!=h){var n=this.getBounds(h.index,h.length);if(null!=n){var r=this.scroll.length()-1,w=this.scroll.line(Math.min(h.index,r)),x=w=e(w,1)[0];0<h.length&&(h=this.scroll.line(Math.min(h.index+
h.length,r)),x=e(h,1)[0]);null!=w&&null!=x&&(h=f.getBoundingClientRect(),n.top<h.top?f.scrollTop-=h.top-n.top:n.bottom>h.bottom&&(f.scrollTop+=n.bottom-h.bottom))}}}},{key:"setNativeRange",value:function(e,f){var h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:e,n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:f,r=4<arguments.length&&void 0!==arguments[4]?arguments[4]:!1;ea.info("setNativeRange",e,f,h,n);if(null==e||null!=this.root.parentNode&&null!=e.parentNode&&null!=h.parentNode){var w=
document.getSelection();if(null!=w)if(null!=e){this.hasFocus()||this.root.focus();var x=(this.getNativeRange()||{}).native;if(null==x||r||e!==x.startContainer||f!==x.startOffset||h!==x.endContainer||n!==x.endOffset)"BR"==e.tagName&&(f=[].indexOf.call(e.parentNode.childNodes,e),e=e.parentNode),"BR"==h.tagName&&(n=[].indexOf.call(h.parentNode.childNodes,h),h=h.parentNode),r=document.createRange(),r.setStart(e,f),r.setEnd(h,n),w.removeAllRanges(),w.addRange(r)}else w.removeAllRanges(),this.root.blur(),
document.body.focus()}}},{key:"setRange",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!1,h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:n.default.sources.API;"string"===typeof f&&(h=f,f=!1);ea.info("setRange",e);if(null!=e){var r=this.rangeToNative(e);this.setNativeRange.apply(this,y(r).concat([f]))}else this.setNativeRange(null);this.update(h)}},{key:"update",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:n.default.sources.USER,
h=this.lastRange,r=this.getRange();r=e(r,2);var w=r[1];this.lastRange=r[0];null!=this.lastRange&&(this.savedRange=this.lastRange);if(!(0,fa.default)(h,this.lastRange)){var x;!this.composing&&null!=w&&w.native.collapsed&&w.start.node!==this.cursor.textNode&&this.cursor.restore();h=[n.default.events.SELECTION_CHANGE,(0,aa.default)(this.lastRange),(0,aa.default)(h),f];(x=this.emitter).emit.apply(x,[n.default.events.EDITOR_CHANGE].concat(h));if(f!==n.default.sources.SILENT){var y;(y=this.emitter).emit.apply(y,
h)}}}}]);return f}();ba.Range=ha;ba.default=da},function(f,ba,da){function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function f(e,f){for(var h=0;h<f.length;h++){var w=f[h];
w.enumerable=w.enumerable||!1;w.configurable=!0;"value"in w&&(w.writable=!0);Object.defineProperty(e,w.key,w)}}return function(e,h,r){h&&f(e.prototype,h);r&&f(e,r);return e}}(),x=function aa(e,f,r){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return aa(e,f,r)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(r)}};f=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");
var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(f,e);y(f,[{key:"insertInto",value:function(e,h){0===e.children.length?x(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"insertInto",this).call(this,e,h):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",
value:function(){}}]);return f}(function(e){return e&&e.__esModule?e:{default:e}}(da(0)).default.Embed);f.blotName="break";f.tagName="BR";ba.default=f},function(f,ba,da){function z(e){var f=w.find(e);if(null==f)try{f=w.create(e)}catch(r){f=w.create(w.Scope.INLINE),[].slice.call(e.childNodes).forEach(function(e){f.domNode.appendChild(e)}),e.parentNode&&e.parentNode.replaceChild(f.domNode,e),f.attach()}return f}var y=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof
Array&&function(e,f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(f,r){function h(){this.constructor=f}e(f,r);f.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});var x=da(44);f=da(30);var w=da(1);da=function(e){function f(f){f=e.call(this,f)||this;f.build();return f}y(f,e);f.prototype.appendChild=function(e){this.insertBefore(e)};f.prototype.attach=function(){e.prototype.attach.call(this);
this.children.forEach(function(e){e.attach()})};f.prototype.build=function(){var e=this;this.children=new x.default;[].slice.call(this.domNode.childNodes).reverse().forEach(function(f){try{var h=z(f);e.insertBefore(h,e.children.head||void 0)}catch(n){if(!(n instanceof w.ParchmentError))throw n;}})};f.prototype.deleteAt=function(e,f){if(0===e&&f===this.length())return this.remove();this.children.forEachAt(e,f,function(e,f,h){e.deleteAt(f,h)})};f.prototype.descendant=function(e,h){var r=this.children.find(h);
h=r[0];r=r[1];return null==e.blotName&&e(h)||null!=e.blotName&&h instanceof e?[h,r]:h instanceof f?h.descendant(e,r):[null,-1]};f.prototype.descendants=function(e,h,w){void 0===h&&(h=0);void 0===w&&(w=Number.MAX_VALUE);var n=[],r=w;this.children.forEachAt(h,w,function(h,w,x){(null==e.blotName&&e(h)||null!=e.blotName&&h instanceof e)&&n.push(h);h instanceof f&&(n=n.concat(h.descendants(e,w,r)));r-=x});return n};f.prototype.detach=function(){this.children.forEach(function(e){e.detach()});e.prototype.detach.call(this)};
f.prototype.formatAt=function(e,f,h,n){this.children.forEachAt(e,f,function(e,f,r){e.formatAt(f,r,h,n)})};f.prototype.insertAt=function(e,f,h){var n=this.children.find(e);e=n[0];n=n[1];e?e.insertAt(n,f,h):(f=null==h?w.create("text",f):w.create(f,h),this.appendChild(f))};f.prototype.insertBefore=function(e,f){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(f){return e instanceof f}))throw new w.ParchmentError("Cannot insert "+e.statics.blotName+" into "+this.statics.blotName);
e.insertInto(this,f)};f.prototype.length=function(){return this.children.reduce(function(e,f){return e+f.length()},0)};f.prototype.moveChildren=function(e,f){this.children.forEach(function(h){e.insertBefore(h,f)})};f.prototype.optimize=function(f){e.prototype.optimize.call(this,f);if(0===this.children.length)if(null!=this.statics.defaultChild){var h=w.create(this.statics.defaultChild);this.appendChild(h);h.optimize(f)}else this.remove()};f.prototype.path=function(e,h){void 0===h&&(h=!1);var r=this.children.find(e,
h),n=r[0];r=r[1];e=[[this,e]];if(n instanceof f)return e.concat(n.path(r,h));null!=n&&e.push([n,r]);return e};f.prototype.removeChild=function(e){this.children.remove(e)};f.prototype.replace=function(h){h instanceof f&&h.moveChildren(this);e.prototype.replace.call(this,h)};f.prototype.split=function(e,f){void 0===f&&(f=!1);if(!f){if(0===e)return this;if(e===this.length())return this.next}var h=this.clone();this.parent.insertBefore(h,this.next);this.children.forEachAt(e,this.length(),function(e,r){e=
e.split(r,f);h.appendChild(e)});return h};f.prototype.unwrap=function(){this.moveChildren(this.parent,this.next);this.remove()};f.prototype.update=function(e){var f=this,h=[],n=[];e.forEach(function(e){e.target===f.domNode&&"childList"===e.type&&(h.push.apply(h,e.addedNodes),n.push.apply(n,e.removedNodes))});n.forEach(function(e){null!=e.parentNode&&"IFRAME"!==e.tagName&&document.body.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY||(e=w.find(e),null!=e&&(null!=e.domNode.parentNode&&
e.domNode.parentNode!==f.domNode||e.detach()))});h.filter(function(e){return e.parentNode==f.domNode}).sort(function(e,f){return e===f?0:e.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(e){var h=null;null!=e.nextSibling&&(h=w.find(e.nextSibling));e=z(e);if(e.next!=h||null==e.next)null!=e.parent&&e.parent.removeChild(f),f.insertBefore(e,h||void 0)})};return f}(f.default);ba.default=da},function(f,ba,da){var z=this&&this.__extends||function(){var e=Object.setPrototypeOf||
{__proto__:[]}instanceof Array&&function(e,f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(f,r){function h(){this.constructor=f}e(f,r);f.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});var y=da(12),x=da(31);f=da(17);var w=da(1);da=function(e){function f(f){f=e.call(this,f)||this;f.attributes=new x.default(f.domNode);return f}z(f,e);f.formats=function(e){if("string"===typeof this.tagName)return!0;
if(Array.isArray(this.tagName))return e.tagName.toLowerCase()};f.prototype.format=function(e,f){var h=w.query(e);h instanceof y.default?this.attributes.attribute(h,f):f&&(null==h||e===this.statics.blotName&&this.formats()[e]===f||this.replaceWith(e,f))};f.prototype.formats=function(){var e=this.attributes.values(),f=this.statics.formats(this.domNode);null!=f&&(e[this.statics.blotName]=f);return e};f.prototype.replaceWith=function(f,h){f=e.prototype.replaceWith.call(this,f,h);this.attributes.copy(f);
return f};f.prototype.update=function(f,h){var r=this;e.prototype.update.call(this,f,h);f.some(function(e){return e.target===r.domNode&&"attributes"===e.type})&&this.attributes.build()};f.prototype.wrap=function(h,w){h=e.prototype.wrap.call(this,h,w);h instanceof f&&h.statics.scope===this.statics.scope&&this.attributes.move(h);return h};return f}(f.default);ba.default=da},function(f,ba,da){var z=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,
e){f.__proto__=e}||function(f,e){for(var h in e)e.hasOwnProperty(h)&&(f[h]=e[h])};return function(w,e){function h(){this.constructor=w}f(w,e);w.prototype=null===e?Object.create(e):(h.prototype=e.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});f=da(30);var y=da(1);da=function(f){function w(){return null!==f&&f.apply(this,arguments)||this}z(w,f);w.value=function(){return!0};w.prototype.index=function(e,f){return this.domNode===e||this.domNode.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY?
Math.min(f,1):-1};w.prototype.position=function(e){var f=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);0<e&&(f+=1);return[this.parent.domNode,f]};w.prototype.value=function(){var e;return e={},e[this.statics.blotName]=this.statics.value(this.domNode)||!0,e};w.scope=y.Scope.INLINE_BLOT;return w}(f.default);ba.default=da},function(f,ba,da){function z(e){this.ops=e;this.offset=this.index=0}var y=da(11),x=da(3),w={attributes:{compose:function(e,f,r){"object"!==typeof e&&(e={});"object"!==
typeof f&&(f={});var h=x(!0,{},f);r||(h=Object.keys(h).reduce(function(e,f){null!=h[f]&&(e[f]=h[f]);return e},{}));for(var w in e)void 0!==e[w]&&void 0===f[w]&&(h[w]=e[w]);return 0<Object.keys(h).length?h:void 0},diff:function(e,f){"object"!==typeof e&&(e={});"object"!==typeof f&&(f={});var h=Object.keys(e).concat(Object.keys(f)).reduce(function(h,r){y(e[r],f[r])||(h[r]=void 0===f[r]?null:f[r]);return h},{});return 0<Object.keys(h).length?h:void 0},transform:function(e,f,r){if("object"!==typeof e)return f;
if("object"===typeof f){if(!r)return f;r=Object.keys(f).reduce(function(h,r){void 0===e[r]&&(h[r]=f[r]);return h},{});return 0<Object.keys(r).length?r:void 0}}},iterator:function(e){return new z(e)},length:function(e){return"number"===typeof e["delete"]?e["delete"]:"number"===typeof e.retain?e.retain:"string"===typeof e.insert?e.insert.length:1}};z.prototype.hasNext=function(){return Infinity>this.peekLength()};z.prototype.next=function(e){e||(e=Infinity);var f=this.ops[this.index];if(f){var r=this.offset,
x=w.length(f);e>=x-r?(e=x-r,this.index+=1,this.offset=0):this.offset+=e;if("number"===typeof f["delete"])return{"delete":e};x={};f.attributes&&(x.attributes=f.attributes);"number"===typeof f.retain?x.retain=e:x.insert="string"===typeof f.insert?f.insert.substr(r,e):f.insert;return x}return{retain:Infinity}};z.prototype.peek=function(){return this.ops[this.index]};z.prototype.peekLength=function(){return this.ops[this.index]?w.length(this.ops[this.index])-this.offset:Infinity};z.prototype.peekType=
function(){if(this.ops[this.index]){if("number"===typeof this.ops[this.index]["delete"])return"delete";if("number"!==typeof this.ops[this.index].retain)return"insert"}return"retain"};z.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var e=this.offset,f=this.index,r=this.next(),w=this.ops.slice(this.index);this.offset=e;this.index=f;return[r].concat(w)}return[]};f.exports=w},function(z){var ba=function(){function z(e,f){return null!=f&&e instanceof
f}function ca(r,y,da,n,ba){function aa(r,da){if(null===r)return null;if(0===da||"object"!=typeof r)return r;if(z(r,w))var ja=new w;else if(z(r,e))ja=new e;else if(z(r,h))ja=new h(function(e,f){r.then(function(f){e(aa(f,da-1))},function(e){f(aa(e,da-1))})});else if(ca.__isArray(r))ja=[];else if(ca.__isRegExp(r))ja=new RegExp(r.source,x(r)),r.lastIndex&&(ja.lastIndex=r.lastIndex);else if(ca.__isDate(r))ja=new Date(r.getTime());else{if(ia&&f.isBuffer(r))return ja=f.allocUnsafe?f.allocUnsafe(r.length):
new f(r.length),r.copy(ja),ja;if(z(r,Error))ja=Object.create(r);else if("undefined"==typeof n){var ha=Object.getPrototypeOf(r);ja=Object.create(ha)}else ja=Object.create(n),ha=n}if(y){var ma=fa.indexOf(r);if(-1!=ma)return ea[ma];fa.push(r);ea.push(ja)}z(r,w)&&r.forEach(function(e,f){f=aa(f,da-1);e=aa(e,da-1);ja.set(f,e)});z(r,e)&&r.forEach(function(e){e=aa(e,da-1);ja.add(e)});for(var ka in r){var na;ha&&(na=Object.getOwnPropertyDescriptor(ha,ka));na&&null==na.set||(ja[ka]=aa(r[ka],da-1))}if(Object.getOwnPropertySymbols)for(ma=
Object.getOwnPropertySymbols(r),ka=0;ka<ma.length;ka++)if(na=ma[ka],ha=Object.getOwnPropertyDescriptor(r,na),!ha||ha.enumerable||ba)ja[na]=aa(r[na],da-1),ha.enumerable||Object.defineProperty(ja,na,{enumerable:!1});if(ba)for(ma=Object.getOwnPropertyNames(r),ka=0;ka<ma.length;ka++)na=ma[ka],ha=Object.getOwnPropertyDescriptor(r,na),ha&&ha.enumerable||(ja[na]=aa(r[na],da-1),Object.defineProperty(ja,na,{enumerable:!1}));return ja}"object"===typeof y&&(da=y.depth,n=y.prototype,ba=y.includeNonEnumerable,
y=y.circular);var fa=[],ea=[],ia="undefined"!=typeof f;"undefined"==typeof y&&(y=!0);"undefined"==typeof da&&(da=Infinity);return aa(r,da)}function y(e){return Object.prototype.toString.call(e)}function x(e){var f="";e.global&&(f+="g");e.ignoreCase&&(f+="i");e.multiline&&(f+="m");return f}try{var w=Map}catch(r){w=function(){}}try{var e=Set}catch(r){e=function(){}}try{var h=Promise}catch(r){h=function(){}}ca.clonePrototype=function(e){function f(){}if(null===e)return null;f.prototype=e;return new f};
ca.__objToStr=y;ca.__isDate=function(e){return"object"===typeof e&&"[object Date]"===y(e)};ca.__isArray=function(e){return"object"===typeof e&&"[object Array]"===y(e)};ca.__isRegExp=function(e){return"object"===typeof e&&"[object RegExp]"===y(e)};ca.__getRegExpFlags=x;return ca}();"object"===typeof z&&z.exports&&(z.exports=ba)},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function x(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function w(e){return e instanceof ea.default||e instanceof n.BlockEmbed}Object.defineProperty(ba,"__esModule",{value:!0});var e=
function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(ya){r=!0,w=ya}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;
n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),r=function ra(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ra(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};f=da(0);var aa=z(f);f=da(8);var fa=z(f),n=da(4),ea=z(n);f=da(16);var ha=z(f);f=da(13);var oa=z(f);da=da(25);
da=z(da);f=function(f){function z(e,f){if(!(this instanceof z))throw new TypeError("Cannot call a class as a function");e=y(this,(z.__proto__||Object.getPrototypeOf(z)).call(this,e));e.emitter=f.emitter;Array.isArray(f.whitelist)&&(e.whitelist=f.whitelist.reduce(function(e,f){e[f]=!0;return e},{}));e.domNode.addEventListener("DOMNodeInserted",function(){});e.optimize();e.enable();return e}x(z,f);h(z,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1;
this.optimize()}},{key:"deleteAt",value:function(f,h){var w=this.line(f),x=e(w,2);w=x[0];var y=x[1];x=this.line(f+h);x=e(x,1)[0];r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"deleteAt",this).call(this,f,h);if(null!=x&&w!==x&&0<y){if(w instanceof n.BlockEmbed||x instanceof n.BlockEmbed){this.optimize();return}if(w instanceof oa.default){if(f=w.newlineIndex(w.length(),!0),-1<f&&(w=w.split(f+1),w===x)){this.optimize();return}}else x instanceof oa.default&&(f=x.newlineIndex(0),-1<f&&x.split(f+
1));w.moveChildren(x,x.children.head instanceof ha.default?null:x.children.head);w.remove()}this.optimize()}},{key:"enable",value:function(){this.domNode.setAttribute("contenteditable",0<arguments.length&&void 0!==arguments[0]?arguments[0]:!0)}},{key:"formatAt",value:function(e,f,h,n){if(null==this.whitelist||this.whitelist[h])r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"formatAt",this).call(this,e,f,h,n),this.optimize()}},{key:"insertAt",value:function(e,f,h){if(null==h||null==this.whitelist||
this.whitelist[f])e>=this.length()?null==h||null==aa.default.query(f,aa.default.Scope.BLOCK)?(e=aa.default.create(this.statics.defaultChild),this.appendChild(e),null==h&&f.endsWith("\n")&&(f=f.slice(0,-1)),e.insertAt(0,f,h)):(f=aa.default.create(f,h),this.appendChild(f)):r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"insertAt",this).call(this,e,f,h),this.optimize()}},{key:"insertBefore",value:function(e,f){if(e.statics.scope===aa.default.Scope.INLINE_BLOT){var h=aa.default.create(this.statics.defaultChild);
h.appendChild(e);e=h}r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"insertBefore",this).call(this,e,f)}},{key:"leaf",value:function(e){return this.path(e).pop()||[null,-1]}},{key:"line",value:function(e){return e===this.length()?this.line(e-1):this.descendant(w,e)}},{key:"lines",value:function(){return function la(e,f,h){var n=[],r=h;e.children.forEachAt(f,h,function(e,f,h){w(e)?n.push(e):e instanceof aa.default.Container&&(n=n.concat(la(e,f,r)));r-=h});return n}(this,0<arguments.length&&
void 0!==arguments[0]?arguments[0]:0,1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"optimize",this).call(this,e,f),0<e.length&&this.emitter.emit(fa.default.events.SCROLL_OPTIMIZE,e,f))}},{key:"path",value:function(e){return r(z.prototype.__proto__||
Object.getPrototypeOf(z.prototype),"path",this).call(this,e).slice(1)}},{key:"update",value:function(e){if(!0!==this.batch){var f=fa.default.sources.USER;"string"===typeof e&&(f=e);Array.isArray(e)||(e=this.observer.takeRecords());0<e.length&&this.emitter.emit(fa.default.events.SCROLL_BEFORE_UPDATE,f,e);r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"update",this).call(this,e.concat([]));0<e.length&&this.emitter.emit(fa.default.events.SCROLL_UPDATE,f,e)}}}]);return z}(aa.default.Scroll);
f.blotName="scroll";f.className="ql-editor";f.tagName="DIV";f.defaultChild="block";f.allowedChildren=[ea.default,n.BlockEmbed,da.default];ba.default=f},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f,h){f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?
e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function e(e,f){var h,n=e===va.keys.LEFT?"prefix":"suffix";return h={key:e,shiftKey:f,altKey:null},y(h,n,/^$/),y(h,"handler",function(h){var n=h.index;e===va.keys.RIGHT&&(n+=h.length+
1);n=this.quill.getLeaf(n);if(!(qa(n,1)[0]instanceof la.default.Embed))return!0;e===va.keys.LEFT?f?this.quill.setSelection(h.index-1,h.length+1,ya.default.sources.USER):this.quill.setSelection(h.index-1,ya.default.sources.USER):f?this.quill.setSelection(h.index,h.length+1,ya.default.sources.USER):this.quill.setSelection(h.index+h.length+1,ya.default.sources.USER);return!1}),h}function h(e,f){if(!(0===e.index||1>=this.quill.getLength())){var h=this.quill.getLine(e.index),n=qa(h,1)[0];h={};if(0===f.offset){var r=
this.quill.getLine(e.index-1);r=qa(r,1)[0];null!=r&&1<r.length()&&(h=n.formats(),n=this.quill.getFormat(e.index-1,1),h=ma.default.attributes.diff(h,n)||{})}f=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(f.prefix)?2:1;this.quill.deleteText(e.index-f,f,ya.default.sources.USER);0<Object.keys(h).length&&this.quill.formatLine(e.index-f,f,h,ya.default.sources.USER);this.quill.focus()}}function r(e,f){var h=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(f.suffix)?2:1;if(!(e.index>=this.quill.getLength()-h)){var n={},
r=0,w=this.quill.getLine(e.index);w=qa(w,1)[0];f.offset>=w.length()-1&&(f=this.quill.getLine(e.index+1),f=qa(f,1)[0])&&(n=w.formats(),r=this.quill.getFormat(e.index,1),n=ma.default.attributes.diff(n,r)||{},r=f.length());this.quill.deleteText(e.index,h,ya.default.sources.USER);0<Object.keys(n).length&&this.quill.formatLine(e.index+r-1,h,n,ya.default.sources.USER)}}function aa(e){var f=this.quill.getLines(e),h={};1<f.length&&(h=f[0].formats(),f=f[f.length-1].formats(),h=ma.default.attributes.diff(f,
h)||{});this.quill.deleteText(e,ya.default.sources.USER);0<Object.keys(h).length&&this.quill.formatLine(e.index,1,h,ya.default.sources.USER);this.quill.setSelection(e.index,ya.default.sources.SILENT);this.quill.focus()}function fa(e,f){var h=this;0<e.length&&this.quill.scroll.deleteAt(e.index,e.length);var n=Object.keys(f.format).reduce(function(e,h){la.default.query(h,la.default.Scope.BLOCK)&&!Array.isArray(f.format[h])&&(e[h]=f.format[h]);return e},{});this.quill.insertText(e.index,"\n",n,ya.default.sources.USER);
this.quill.setSelection(e.index+1,ya.default.sources.SILENT);this.quill.focus();Object.keys(f.format).forEach(function(e){null==n[e]&&(Array.isArray(f.format[e])||"link"!==e&&h.quill.format(e,f.format[e],ya.default.sources.USER))})}function n(e){return{key:va.keys.TAB,shiftKey:!e,format:{"code-block":!0},handler:function(f){var h=la.default.query("code-block"),n=f.index,r=f.length;f=this.quill.scroll.descendant(h,n);f=qa(f,2);var w=f[0],x=f[1];if(null!=w){f=this.quill.getIndex(w);var y=w.newlineIndex(x,
!0)+1;f=w.newlineIndex(f+x+r);f=w.domNode.textContent.slice(y,f).split("\n");x=0;f.forEach(function(f,z){e?(w.insertAt(y+x,h.TAB),x+=h.TAB.length,0===z?n+=h.TAB.length:r+=h.TAB.length):f.startsWith(h.TAB)&&(w.deleteAt(y+x,h.TAB.length),x-=h.TAB.length,0===z?n-=h.TAB.length:r-=h.TAB.length);x+=f.length+1});this.quill.update(ya.default.sources.USER);this.quill.setSelection(n,r,ya.default.sources.SILENT)}}}}function ea(e){return{key:e[0].toUpperCase(),shortKey:!0,handler:function(f,h){this.quill.format(e,
!h.format[e],ya.default.sources.USER)}}}function ha(e){if("string"===typeof e||"number"===typeof e)return ha({key:e});"object"===("undefined"===typeof e?"undefined":oa(e))&&(e=(0,ta.default)(e,!1));if("string"===typeof e.key)if(null!=va.keys[e.key.toUpperCase()])e.key=va.keys[e.key.toUpperCase()];else if(1===e.key.length)e.key=e.key.toUpperCase().charCodeAt(0);else return null;e.shortKey&&(e[Aa]=e.shortKey,delete e.shortKey);return e}Object.defineProperty(ba,"__esModule",{value:!0});ba.SHORTKEY=ba.default=
void 0;var oa="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qa=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(Ga){r=!0,w=Ga}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;
}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),ka=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(21);var ta=z(f);f=da(11);var ua=z(f);f=da(3);var ra=z(f);f=da(2);var Ba=z(f);f=da(20);var ma=z(f);f=da(0);var la=z(f);f=da(5);var ya=z(f);f=da(10);f=z(f);da=da(9);
da=z(da);var Fa=(0,f.default)("quill:keyboard"),Aa=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",va=function(e){function f(e,n){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var w=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,n));w.bindings={};Object.keys(w.options.bindings).forEach(function(f){("list autofill"!==f||null==e.scroll.whitelist||e.scroll.whitelist.list)&&w.options.bindings[f]&&w.addBinding(w.options.bindings[f])});w.addBinding({key:f.keys.ENTER,
shiftKey:null},fa);w.addBinding({key:f.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){});/Firefox/i.test(navigator.userAgent)?(w.addBinding({key:f.keys.BACKSPACE},{collapsed:!0},h),w.addBinding({key:f.keys.DELETE},{collapsed:!0},r)):(w.addBinding({key:f.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},h),w.addBinding({key:f.keys.DELETE},{collapsed:!0,suffix:/^.?$/},r));w.addBinding({key:f.keys.BACKSPACE},{collapsed:!1},aa);w.addBinding({key:f.keys.DELETE},{collapsed:!1},aa);w.addBinding({key:f.keys.BACKSPACE,
altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},h);w.listen();return w}w(f,e);ka(f,null,[{key:"match",value:function(e,f){f=ha(f);return["altKey","ctrlKey","metaKey","shiftKey"].some(function(h){return!!f[h]!==e[h]&&null!==f[h]})?!1:f.key===(e.which||e.keyCode)}}]);ka(f,[{key:"addBinding",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},n=ha(e);if(null==n||null==n.key)return Fa.warn("Attempted to add invalid keyboard binding",
n);"function"===typeof f&&(f={handler:f});"function"===typeof h&&(h={handler:h});n=(0,ra.default)(n,f,h);this.bindings[n.key]=this.bindings[n.key]||[];this.bindings[n.key].push(n)}},{key:"listen",value:function(){var e=this;this.quill.root.addEventListener("keydown",function(h){if(!h.defaultPrevented){var n=(e.bindings[h.which||h.keyCode]||[]).filter(function(e){return f.match(h,e)});if(0!==n.length){var r=e.quill.getSelection();if(null!=r&&e.quill.hasFocus()){var w=e.quill.getLine(r.index),x=qa(w,
2);w=x[0];x=x[1];var y=e.quill.getLeaf(r.index),z=qa(y,2);y=z[0];z=z[1];var aa=0===r.length?[y,z]:e.quill.getLeaf(r.index+r.length),ca=qa(aa,2);aa=ca[0];ca=ca[1];y=y instanceof la.default.Text?y.value().slice(0,z):"";z=aa instanceof la.default.Text?aa.value().slice(ca):"";var da={collapsed:0===r.length,empty:0===r.length&&1>=w.length(),format:e.quill.getFormat(r),offset:x,prefix:y,suffix:z};n.some(function(f){if(null!=f.collapsed&&f.collapsed!==da.collapsed||null!=f.empty&&f.empty!==da.empty||null!=
f.offset&&f.offset!==da.offset)return!1;if(Array.isArray(f.format)){if(f.format.every(function(e){return null==da.format[e]}))return!1}else if("object"===oa(f.format)&&!Object.keys(f.format).every(function(e){return!0===f.format[e]?null!=da.format[e]:!1===f.format[e]?null==da.format[e]:(0,ua.default)(f.format[e],da.format[e])}))return!1;return null!=f.prefix&&!f.prefix.test(da.prefix)||null!=f.suffix&&!f.suffix.test(da.suffix)?!1:!0!==f.handler.call(e,r,da)})&&h.preventDefault()}}}})}}]);return f}(da.default);
va.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46};va.DEFAULTS={bindings:{bold:ea("bold"),italic:ea("italic"),underline:ea("underline"),indent:{key:va.keys.TAB,format:["blockquote","indent","list"],handler:function(e,f){if(f.collapsed&&0!==f.offset)return!0;this.quill.format("indent","+1",ya.default.sources.USER)}},outdent:{key:va.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(e,f){if(f.collapsed&&0!==f.offset)return!0;this.quill.format("indent",
"-1",ya.default.sources.USER)}},"outdent backspace":{key:va.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(e,f){null!=f.format.indent?this.quill.format("indent","-1",ya.default.sources.USER):null!=f.format.list&&this.quill.format("list",!1,ya.default.sources.USER)}},"indent code-block":n(!0),"outdent code-block":n(!1),"remove tab":{key:va.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(e){this.quill.deleteText(e.index-
1,1,ya.default.sources.USER)}},tab:{key:va.keys.TAB,handler:function(e){this.quill.history.cutoff();var f=(new Ba.default).retain(e.index).delete(e.length).insert("\t");this.quill.updateContents(f,ya.default.sources.USER);this.quill.history.cutoff();this.quill.setSelection(e.index+1,ya.default.sources.SILENT)}},"list empty enter":{key:va.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(e,f){this.quill.format("list",!1,ya.default.sources.USER);f.format.indent&&this.quill.format("indent",
!1,ya.default.sources.USER)}},"checklist enter":{key:va.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(e){var f=this.quill.getLine(e.index),h=qa(f,2);f=h[0];h=h[1];var n=(0,ra.default)({},f.formats(),{list:"checked"});f=(new Ba.default).retain(e.index).insert("\n",n).retain(f.length()-h-1).retain(1,{list:"unchecked"});this.quill.updateContents(f,ya.default.sources.USER);this.quill.setSelection(e.index+1,ya.default.sources.SILENT);this.quill.scrollIntoView()}},"header enter":{key:va.keys.ENTER,
collapsed:!0,format:["header"],suffix:/^$/,handler:function(e,f){var h=this.quill.getLine(e.index),n=qa(h,2);h=n[0];n=n[1];f=(new Ba.default).retain(e.index).insert("\n",f.format).retain(h.length()-n-1).retain(1,{header:null});this.quill.updateContents(f,ya.default.sources.USER);this.quill.setSelection(e.index+1,ya.default.sources.SILENT);this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(e,f){var h=f.prefix.length,
n=this.quill.getLine(e.index),r=qa(n,2);n=r[0];r=r[1];if(r>h)return!0;switch(f.prefix.trim()){case "[]":case "[ ]":f="unchecked";break;case "[x]":f="checked";break;case "-":case "*":f="bullet";break;default:f="ordered"}this.quill.insertText(e.index," ",ya.default.sources.USER);this.quill.history.cutoff();f=(new Ba.default).retain(e.index-r).delete(h+1).retain(n.length()-2-r).retain(1,{list:f});this.quill.updateContents(f,ya.default.sources.USER);this.quill.history.cutoff();this.quill.setSelection(e.index-
h,ya.default.sources.SILENT)}},"code exit":{key:va.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(e){var f=this.quill.getLine(e.index),h=qa(f,2);f=h[0];h=h[1];e=(new Ba.default).retain(e.index+f.length()-h-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(e,ya.default.sources.USER)}},"embed left":e(va.keys.LEFT,!1),"embed left shift":e(va.keys.LEFT,!0),"embed right":e(va.keys.RIGHT,!1),"embed right shift":e(va.keys.RIGHT,!0)}};ba.default=
va;ba.SHORTKEY=Aa},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=
[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(ka){r=!0,w=ka}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),x=function ia(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ia(e,f,h)}else{if("value"in n)return n.value;
f=n.get;return void 0===f?void 0:f.call(h)}},w=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,w){h&&e(f.prototype,h);w&&e(f,w);return f}}(),e=(f=da(0))&&f.__esModule?f:{default:f},h=(da=da(7))&&da.__esModule?da:{default:da};da=function(f){function aa(e,f){if(!(this instanceof aa))throw new TypeError("Cannot call a class as a function");e=(aa.__proto__||
Object.getPrototypeOf(aa)).call(this,e);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");e=!e||"object"!==typeof e&&"function"!==typeof e?this:e;e.selection=f;e.textNode=document.createTextNode(aa.CONTENTS);e.domNode.appendChild(e.textNode);e._length=0;return e}z(aa,f);w(aa,null,[{key:"value",value:function(){}}]);w(aa,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(f,h){if(0!==this._length)return x(aa.prototype.__proto__||
Object.getPrototypeOf(aa.prototype),"format",this).call(this,f,h);for(var n=this,w=0;null!=n&&n.statics.scope!==e.default.Scope.BLOCK_BLOT;)w+=n.offset(n.parent),n=n.parent;null!=n&&(this._length=aa.CONTENTS.length,n.optimize(),n.formatAt(w,aa.CONTENTS.length,f,h),this._length=0)}},{key:"index",value:function(e,f){return e===this.textNode?0:x(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"index",this).call(this,e,f)}},{key:"length",value:function(){return this._length}},{key:"position",
value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){x(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"remove",this).call(this);this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var f=this.textNode,w=this.selection.getNativeRange(),x=void 0,z=void 0,ca=void 0;null!=w&&w.start.node===f&&w.end.node===f&&(ca=[f,w.start.offset,w.end.offset],x=ca[0],z=ca[1],ca=ca[2]);for(;null!=this.domNode.lastChild&&
this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);this.textNode.data!==aa.CONTENTS&&(f=this.textNode.data.split(aa.CONTENTS).join(""),this.next instanceof h.default?(x=this.next.domNode,this.next.insertAt(0,f),this.textNode.data=aa.CONTENTS):(this.textNode.data=f,this.parent.insertBefore(e.default.create(this.textNode),this),this.textNode=document.createTextNode(aa.CONTENTS),this.domNode.appendChild(this.textNode)));this.remove();if(null!=
z)return z=[z,ca].map(function(e){return Math.max(0,Math.min(x.data.length,e-1))}),ca=y(z,2),z=ca[0],ca=ca[1],{startNode:x,startOffset:z,endNode:x,endOffset:ca}}}},{key:"update",value:function(e,f){var h=this;e.some(function(e){return"characterData"===e.type&&e.target===h.textNode})&&(e=this.restore())&&(f.range=e)}},{key:"value",value:function(){return""}}]);return aa}(e.default.Embed);da.blotName="cursor";da.className="ql-cursor";da.tagName="span";da.CONTENTS="\ufeff";ba.default=da},function(f,
ba,da){function z(f,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+typeof w);f.prototype=Object.create(w&&w.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(f,w):f.__proto__=w)}Object.defineProperty(ba,"__esModule",{value:!0});f=(f=da(0))&&f.__esModule?f:{default:f};var y=(da=da(4))&&da.__esModule?da:{default:da};f=function(f){function w(){if(!(this instanceof
w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(w,f);return w}(f.default.Container);f.allowedChildren=[y.default,da.BlockEmbed,f];ba.default=f},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+
typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.ColorStyle=ba.ColorClass=ba.ColorAttributor=void 0;var y=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,r,w){r&&e(f.prototype,
r);w&&e(f,w);return f}}(),x=function ja(f,r,w){null===f&&(f=Function.prototype);var h=Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===r?void 0:r.call(w)}},w=function(f){return f&&f.__esModule?f:{default:f}}(da(0));f=function(f){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var f=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);
if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(h,f);y(h,[{key:"value",value:function(f){f=x(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"value",this).call(this,f);if(!f.startsWith("rgb("))return f;f=f.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"");return"#"+f.split(",").map(function(f){return("00"+parseInt(f).toString(16)).slice(-2)}).join("")}}]);return h}(w.default.Attributor.Style);
da=new w.default.Attributor.Class("color","ql-color",{scope:w.default.Scope.INLINE});w=new f("color","color",{scope:w.default.Scope.INLINE});ba.ColorAttributor=f;ba.ColorClass=da;ba.ColorStyle=w},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,
f):e.__proto__=f)}function y(e,f){var h=document.createElement("a");h.href=e;e=h.href.slice(0,h.href.indexOf(":"));return-1<f.indexOf(e)}Object.defineProperty(ba,"__esModule",{value:!0});ba.sanitize=ba.default=void 0;var x=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,r,w){r&&e(f.prototype,r);w&&e(f,w);return f}}(),w=function ja(f,r,w){null===f&&(f=Function.prototype);
var h=Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===r?void 0:r.call(w)}};f=function(f){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var f=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==
typeof f?this:f}z(h,f);x(h,[{key:"format",value:function(f,r){if(f!==this.statics.blotName||!r)return w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"format",this).call(this,f,r);r=this.constructor.sanitize(r);this.domNode.setAttribute("href",r)}}],[{key:"create",value:function(f){var r=w(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this,f);f=this.sanitize(f);r.setAttribute("href",f);r.setAttribute("rel","noopener noreferrer");r.setAttribute("target","_blank");return r}},
{key:"formats",value:function(f){return f.getAttribute("href")}},{key:"sanitize",value:function(f){return y(f,this.PROTOCOL_WHITELIST)?f:this.SANITIZED_URL}}]);return h}(function(f){return f&&f.__esModule?f:{default:f}}(da(6)).default);f.blotName="link";f.tagName="A";f.SANITIZED_URL="about:blank";f.PROTOCOL_WHITELIST=["http","https","mailto","tel"];ba.default=f;ba.sanitize=y},function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});var z="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?
function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,w){h&&e(f.prototype,h);w&&e(f,w);return f}}(),x=(f=da(23))&&f.__esModule?f:{default:f},w=(da=da(107))&&da.__esModule?da:{default:da},e=0;da=function(){function f(e){var h=
this;if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");this.select=e;this.container=document.createElement("span");this.buildPicker();this.select.style.display="none";this.select.parentNode.insertBefore(this.container,this.select);this.label.addEventListener("mousedown",function(){h.togglePicker()});this.label.addEventListener("keydown",function(e){switch(e.keyCode){case x.default.keys.ENTER:h.togglePicker();break;case x.default.keys.ESCAPE:h.escape(),e.preventDefault()}});
this.select.addEventListener("change",this.update.bind(this))}y(f,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded");var e=this.label;e.setAttribute("aria-expanded","true"!==e.getAttribute("aria-expanded"));e=this.options;e.setAttribute("aria-hidden","true"!==e.getAttribute("aria-hidden"))}},{key:"buildItem",value:function(e){var f=this,h=document.createElement("span");h.tabIndex="0";h.setAttribute("role","button");h.classList.add("ql-picker-item");e.hasAttribute("value")&&
h.setAttribute("data-value",e.getAttribute("value"));e.textContent&&h.setAttribute("data-label",e.textContent);h.addEventListener("click",function(){f.selectItem(h,!0)});h.addEventListener("keydown",function(e){switch(e.keyCode){case x.default.keys.ENTER:f.selectItem(h,!0);e.preventDefault();break;case x.default.keys.ESCAPE:f.escape(),e.preventDefault()}});return h}},{key:"buildLabel",value:function(){var e=document.createElement("span");e.classList.add("ql-picker-label");e.innerHTML=w.default;e.tabIndex=
"0";e.setAttribute("role","button");e.setAttribute("aria-expanded","false");this.container.appendChild(e);return e}},{key:"buildOptions",value:function(){var f=this,h=document.createElement("span");h.classList.add("ql-picker-options");h.setAttribute("aria-hidden","true");h.tabIndex="-1";h.id="ql-picker-options-"+e;e+=1;this.label.setAttribute("aria-controls",h.id);this.options=h;[].slice.call(this.select.options).forEach(function(e){var n=f.buildItem(e);h.appendChild(n);!0===e.selected&&f.selectItem(n)});
this.container.appendChild(h)}},{key:"buildPicker",value:function(){var e=this;[].slice.call(this.select.attributes).forEach(function(f){e.container.setAttribute(f.name,f.value)});this.container.classList.add("ql-picker");this.label=this.buildLabel();this.buildOptions()}},{key:"escape",value:function(){var e=this;this.close();setTimeout(function(){return e.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded");this.label.setAttribute("aria-expanded","false");
this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(e){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!1,h=this.container.querySelector(".ql-selected");e!==h&&(null!=h&&h.classList.remove("ql-selected"),null!=e&&(e.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(e.parentNode.children,e),e.hasAttribute("data-value")?this.label.setAttribute("data-value",e.getAttribute("data-value")):this.label.removeAttribute("data-value"),e.hasAttribute("data-label")?
this.label.setAttribute("data-label",e.getAttribute("data-label")):this.label.removeAttribute("data-label"),f&&("function"===typeof Event?this.select.dispatchEvent(new Event("change")):"object"===("undefined"===typeof Event?"undefined":z(Event))&&(f=document.createEvent("Event"),f.initEvent("change",!0,!0),this.select.dispatchEvent(f)),this.close())))}},{key:"update",value:function(){var e=void 0;if(-1<this.select.selectedIndex){var f=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];
e=this.select.options[this.select.selectedIndex];this.selectItem(f)}else this.selectItem(null);this.label.classList.toggle("ql-active",null!=e&&e!==this.select.querySelector("option[selected]"))}}]);return f}();ba.default=da},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(ba,"__esModule",{value:!0});f=da(0);f=z(f);var y=da(5);y=z(y);var x=da(4),w=z(x),e=da(16);e=z(e);var h=da(25);h=z(h);var r=da(24);r=z(r);var aa=da(35);aa=z(aa);var fa=da(6);fa=z(fa);var n=
da(22);n=z(n);var ea=da(7);ea=z(ea);var ha=da(55);ha=z(ha);var oa=da(42);oa=z(oa);da=da(23);y.default.register({"blots/block":w.default,"blots/block/embed":x.BlockEmbed,"blots/break":e.default,"blots/container":h.default,"blots/cursor":r.default,"blots/embed":aa.default,"blots/inline":fa.default,"blots/scroll":n.default,"blots/text":ea.default,"modules/clipboard":ha.default,"modules/history":oa.default,"modules/keyboard":z(da).default});f.default.register(w.default,e.default,r.default,fa.default,
n.default,ea.default);ba.default=y.default},function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});var z=da(1);f=function(){function f(f){this.domNode=f;this.domNode[z.DATA_KEY]={blot:this}}Object.defineProperty(f.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0});f.create=function(f){if(null==this.tagName)throw new z.ParchmentError("Blot definition missing tagName");Array.isArray(this.tagName)?("string"===typeof f&&(f=f.toUpperCase(),parseInt(f).toString()===
f&&(f=parseInt(f))),f="number"===typeof f?document.createElement(this.tagName[f-1]):-1<this.tagName.indexOf(f)?document.createElement(f):document.createElement(this.tagName[0])):f=document.createElement(this.tagName);this.className&&f.classList.add(this.className);return f};f.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)};f.prototype.clone=function(){var f=this.domNode.cloneNode(!1);return z.create(f)};f.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this);
delete this.domNode[z.DATA_KEY]};f.prototype.deleteAt=function(f,w){this.isolate(f,w).remove()};f.prototype.formatAt=function(f,w,e,h){f=this.isolate(f,w);null!=z.query(e,z.Scope.BLOT)&&h?f.wrap(e,h):null!=z.query(e,z.Scope.ATTRIBUTE)&&(w=z.create(this.statics.scope),f.wrap(w),w.format(e,h))};f.prototype.insertAt=function(f,w,e){w=null==e?z.create("text",w):z.create(w,e);f=this.split(f);this.parent.insertBefore(w,f)};f.prototype.insertInto=function(f,w){void 0===w&&(w=null);null!=this.parent&&this.parent.children.remove(this);
var e=null;f.children.insertBefore(this,w);null!=w&&(e=w.domNode);this.domNode.parentNode==f.domNode&&this.domNode.nextSibling==e||f.domNode.insertBefore(this.domNode,e);this.parent=f;this.attach()};f.prototype.isolate=function(f,w){f=this.split(f);f.split(w);return f};f.prototype.length=function(){return 1};f.prototype.offset=function(f){void 0===f&&(f=this.parent);return null==this.parent||this==f?0:this.parent.children.offset(this)+this.parent.offset(f)};f.prototype.optimize=function(){null!=this.domNode[z.DATA_KEY]&&
delete this.domNode[z.DATA_KEY].mutations};f.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode);this.detach()};f.prototype.replace=function(f){null!=f.parent&&(f.parent.insertBefore(this,f.next),f.remove())};f.prototype.replaceWith=function(f,w){f="string"===typeof f?z.create(f,w):f;f.replace(this);return f};f.prototype.split=function(f){return 0===f?this:this.next};f.prototype.update=function(){};f.prototype.wrap=function(f,w){f="string"===
typeof f?z.create(f,w):f;null!=this.parent&&this.parent.insertBefore(f,this.next);f.appendChild(this);return f};f.blotName="abstract";return f}();ba.default=f},function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});var z=da(12),y=da(32),x=da(33),w=da(1);f=function(){function e(e){this.attributes={};this.domNode=e;this.build()}e.prototype.attribute=function(e,f){f?e.add(this.domNode,f)&&(null!=e.value(this.domNode)?this.attributes[e.attrName]=e:delete this.attributes[e.attrName]):(e.remove(this.domNode),
delete this.attributes[e.attrName])};e.prototype.build=function(){var e=this;this.attributes={};var f=z.default.keys(this.domNode),aa=y.default.keys(this.domNode),ca=x.default.keys(this.domNode);f.concat(aa).concat(ca).forEach(function(f){f=w.query(f,w.Scope.ATTRIBUTE);f instanceof z.default&&(e.attributes[f.attrName]=f)})};e.prototype.copy=function(e){var f=this;Object.keys(this.attributes).forEach(function(h){var r=f.attributes[h].value(f.domNode);e.format(h,r)})};e.prototype.move=function(e){var f=
this;this.copy(e);Object.keys(this.attributes).forEach(function(e){f.attributes[e].remove(f.domNode)});this.attributes={}};e.prototype.values=function(){var e=this;return Object.keys(this.attributes).reduce(function(f,h){f[h]=e.attributes[h].value(e.domNode);return f},{})};return e}();ba.default=f},function(f,ba,da){function z(f,w){return(f.getAttribute("class")||"").split(/\s+/).filter(function(e){return 0===e.indexOf(w+"-")})}var y=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof
Array&&function(f,e){f.__proto__=e}||function(f,e){for(var h in e)e.hasOwnProperty(h)&&(f[h]=e[h])};return function(w,e){function h(){this.constructor=w}f(w,e);w.prototype=null===e?Object.create(e):(h.prototype=e.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function w(){return null!==f&&f.apply(this,arguments)||this}y(w,f);w.keys=function(e){return(e.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})};w.prototype.add=
function(e,f){if(!this.canAdd(e,f))return!1;this.remove(e);e.classList.add(this.keyName+"-"+f);return!0};w.prototype.remove=function(e){z(e,this.keyName).forEach(function(f){e.classList.remove(f)});0===e.classList.length&&e.removeAttribute("class")};w.prototype.value=function(e){var f=(z(e,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(e,f)?f:""};return w}(da(12).default);ba.default=f},function(f,ba,da){function z(f){f=f.split("-");var w=f.slice(1).map(function(e){return e[0].toUpperCase()+
e.slice(1)}).join("");return f[0]+w}var y=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,e){f.__proto__=e}||function(f,e){for(var h in e)e.hasOwnProperty(h)&&(f[h]=e[h])};return function(w,e){function h(){this.constructor=w}f(w,e);w.prototype=null===e?Object.create(e):(h.prototype=e.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function w(){return null!==f&&f.apply(this,arguments)||this}y(w,f);w.keys=
function(e){return(e.getAttribute("style")||"").split(";").map(function(e){return e.split(":")[0].trim()})};w.prototype.add=function(e,f){if(!this.canAdd(e,f))return!1;e.style[z(this.keyName)]=f;return!0};w.prototype.remove=function(e){e.style[z(this.keyName)]="";e.getAttribute("style")||e.removeAttribute("style")};w.prototype.value=function(e){var f=e.style[z(this.keyName)];return this.canAdd(e,f)?f:""};return w}(da(12).default);ba.default=f},function(f,ba){Object.defineProperty(ba,"__esModule",
{value:!0});var z=function(){function f(f,x){for(var w=0;w<x.length;w++){var e=x[w];e.enumerable=e.enumerable||!1;e.configurable=!0;"value"in e&&(e.writable=!0);Object.defineProperty(f,e.key,e)}}return function(y,x,w){x&&f(y.prototype,x);w&&f(y,w);return y}}();f=function(){function f(y,x){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");this.quill=y;this.options=x;this.modules={}}z(f,[{key:"init",value:function(){var f=this;Object.keys(this.options.modules).forEach(function(x){null==
f.modules[x]&&f.addModule(x)})}},{key:"addModule",value:function(f){var x=this.quill.constructor.import("modules/"+f);this.modules[f]=new x(this.quill,this.options.modules[f]||{});return this.modules[f]}}]);return f}();f.DEFAULTS={modules:{}};f.themes={"default":f};ba.default=f},function(f,ba,da){function z(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function y(e,f){if("function"!==typeof f&&
null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var x=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,
r)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),w=function ia(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ia(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}},e=(f=da(0))&&f.__esModule?f:{default:f},h=(da=da(7))&&da.__esModule?da:{default:da};da=function(f){function aa(e){if(!(this instanceof aa))throw new TypeError("Cannot call a class as a function");
var f=z(this,(aa.__proto__||Object.getPrototypeOf(aa)).call(this,e));f.contentNode=document.createElement("span");f.contentNode.setAttribute("contenteditable",!1);[].slice.call(f.domNode.childNodes).forEach(function(e){f.contentNode.appendChild(e)});f.leftGuard=document.createTextNode("\ufeff");f.rightGuard=document.createTextNode("\ufeff");f.domNode.appendChild(f.leftGuard);f.domNode.appendChild(f.contentNode);f.domNode.appendChild(f.rightGuard);return f}y(aa,f);x(aa,[{key:"index",value:function(e,
f){return e===this.leftGuard?0:e===this.rightGuard?1:w(aa.prototype.__proto__||Object.getPrototypeOf(aa.prototype),"index",this).call(this,e,f)}},{key:"restore",value:function(f){var n=void 0,w=f.data.split("\ufeff").join("");f===this.leftGuard?this.prev instanceof h.default?(n=this.prev.length(),this.prev.insertAt(n,w),n={startNode:this.prev.domNode,startOffset:n+w.length}):(n=document.createTextNode(w),this.parent.insertBefore(e.default.create(n),this),n={startNode:n,startOffset:w.length}):f===
this.rightGuard&&(this.next instanceof h.default?(this.next.insertAt(0,w),n={startNode:this.next.domNode,startOffset:w.length}):(n=document.createTextNode(w),this.parent.insertBefore(e.default.create(n),this.next),n={startNode:n,startOffset:w.length}));f.data="\ufeff";return n}},{key:"update",value:function(e,f){var h=this;e.forEach(function(e){"characterData"!==e.type||e.target!==h.leftGuard&&e.target!==h.rightGuard||!(e=h.restore(e.target))||(f.range=e)})}}]);return aa}(e.default.Embed);ba.default=
da},function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});ba.AlignStyle=ba.AlignClass=ba.AlignAttribute=void 0;var z=(f=da(0))&&f.__esModule?f:{default:f};var y={scope:z.default.Scope.BLOCK,whitelist:["right","center","justify"]};f=new z.default.Attributor.Attribute("align","align",y);da=new z.default.Attributor.Class("align","ql-align",y);z=new z.default.Attributor.Style("align","text-align",y);ba.AlignAttribute=f;ba.AlignClass=da;ba.AlignStyle=z},function(f,ba,da){Object.defineProperty(ba,
"__esModule",{value:!0});ba.BackgroundStyle=ba.BackgroundClass=void 0;f=(f=da(0))&&f.__esModule?f:{default:f};var z=da(26);da=new f.default.Attributor.Class("background","ql-bg",{scope:f.default.Scope.INLINE});f=new z.ColorAttributor("background","background-color",{scope:f.default.Scope.INLINE});ba.BackgroundClass=da;ba.BackgroundStyle=f},function(f,ba,da){Object.defineProperty(ba,"__esModule",{value:!0});ba.DirectionStyle=ba.DirectionClass=ba.DirectionAttribute=void 0;var z=(f=da(0))&&f.__esModule?
f:{default:f};var y={scope:z.default.Scope.BLOCK,whitelist:["rtl"]};f=new z.default.Attributor.Attribute("direction","dir",y);da=new z.default.Attributor.Class("direction","ql-direction",y);z=new z.default.Attributor.Style("direction","direction",y);ba.DirectionAttribute=f;ba.DirectionClass=da;ba.DirectionStyle=z},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&
f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.FontClass=ba.FontStyle=void 0;var y=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,r,w){r&&e(f.prototype,r);w&&e(f,w);return f}}(),x=function ja(f,r,w){null===
f&&(f=Function.prototype);var h=Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===r?void 0:r.call(w)}};da=function(f){return f&&f.__esModule?f:{default:f}}(da(0));var w={scope:da.default.Scope.INLINE,whitelist:["serif","monospace"]};f=new da.default.Attributor.Class("font","ql-font",w);da=new (function(f){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");
var f=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(h,f);y(h,[{key:"value",value:function(f){return x(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"value",this).call(this,f).replace(/["']/g,"")}}]);return h}(da.default.Attributor.Style))("font","font-family",w);ba.FontStyle=da;ba.FontClass=f},function(f,ba,da){Object.defineProperty(ba,
"__esModule",{value:!0});ba.SizeStyle=ba.SizeClass=void 0;da=(f=da(0))&&f.__esModule?f:{default:f};f=new da.default.Attributor.Class("size","ql-size",{scope:da.default.Scope.INLINE,whitelist:["small","large","huge"]});da=new da.default.Attributor.Style("size","font-size",{scope:da.default.Scope.INLINE,whitelist:["10px","18px","32px"]});ba.SizeClass=f;ba.SizeStyle=da},function(f,ba,da){f.exports={align:{"":da(76),center:da(77),right:da(78),justify:da(79)},background:da(80),blockquote:da(81),bold:da(82),
clean:da(83),code:da(58),"code-block":da(58),color:da(84),direction:{"":da(85),rtl:da(86)},"float":{center:da(87),full:da(88),left:da(89),right:da(90)},formula:da(91),header:{1:da(92),2:da(93)},italic:da(94),image:da(95),indent:{"+1":da(96),"-1":da(97)},link:da(98),list:{ordered:da(99),bullet:da(100),check:da(101)},script:{sub:da(102),"super":da(103)},strike:da(104),underline:da(105),video:da(106)}},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function x(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function w(e){e=e.ops[e.ops.length-1];return null==e?!1:null!=e.insert?"string"===typeof e.insert&&e.insert.endsWith("\n"):null!=
e.attributes?Object.keys(e.attributes).some(function(e){return null!=r.default.query(e,r.default.Scope.BLOCK)}):!1}function e(e){var f=e.reduce(function(e,f){return e+=f.delete||0},0);f=e.length()-f;w(e)&&--f;return f}Object.defineProperty(ba,"__esModule",{value:!0});ba.getLastChangeIndex=ba.default=void 0;var h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,
h,r){h&&e(f.prototype,h);r&&e(f,r);return f}}();f=da(0);var r=z(f);f=da(5);var aa=z(f);da=da(9);da=function(f){function n(e,f){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");var h=y(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e,f));h.lastRecorded=0;h.ignoreChange=!1;h.clear();h.quill.on(aa.default.events.EDITOR_CHANGE,function(e,f,n,r){e!==aa.default.events.TEXT_CHANGE||h.ignoreChange||(h.options.userOnly&&r!==aa.default.sources.USER?h.transform(f):h.record(f,
n))});h.quill.keyboard.addBinding({key:"Z",shortKey:!0},h.undo.bind(h));h.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},h.redo.bind(h));/Win/i.test(navigator.platform)&&h.quill.keyboard.addBinding({key:"Y",shortKey:!0},h.redo.bind(h));return h}x(n,f);h(n,[{key:"change",value:function(f,h){if(0!==this.stack[f].length){var n=this.stack[f].pop();this.stack[h].push(n);this.lastRecorded=0;this.ignoreChange=!0;this.quill.updateContents(n[f],aa.default.sources.USER);this.ignoreChange=!1;f=
e(n[f]);this.quill.setSelection(f)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(e,f){if(0!==e.ops.length){this.stack.redo=[];f=this.quill.getContents().diff(f);var h=Date.now();this.lastRecorded+this.options.delay>h&&0<this.stack.undo.length?(h=this.stack.undo.pop(),f=f.compose(h.undo),e=h.redo.compose(e)):this.lastRecorded=h;this.stack.undo.push({redo:e,undo:f});this.stack.undo.length>this.options.maxStack&&
this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(e){this.stack.undo.forEach(function(f){f.undo=e.transform(f.undo,!0);f.redo=e.transform(f.redo,!0)});this.stack.redo.forEach(function(f){f.undo=e.transform(f.undo,!0);f.redo=e.transform(f.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]);return n}(z(da).default);da.DEFAULTS={delay:1E3,maxStack:100,userOnly:!1};ba.default=da;ba.getLastChangeIndex=e},function(f,
ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,
enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function e(e,f){var h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:!1;f.forEach(function(f){var n=document.createElement("option");f===h?n.setAttribute("selected","selected"):n.setAttribute("value",f);e.appendChild(n)})}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.BaseTooltip=void 0;var h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=
n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),r=function va(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return va(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};f=da(3);f=z(f);var aa=da(2),ea=z(aa);aa=da(8);var n=z(aa);aa=da(23);var fa=z(aa);aa=da(34);
aa=z(aa);var ha=da(59),oa=z(ha);ha=da(60);var qa=z(ha);ha=da(28);var ka=z(ha);da=da(61);ha=z(da);var ta=[!1,"center","right","justify"],ua="#000000 #e60000 #ff9900 #ffff00 #008a00 #0066cc #9933ff #ffffff #facccc #ffebcc #ffffcc #cce8cc #cce0f5 #ebd6ff #bbbbbb #f06666 #ffc266 #ffff66 #66b966 #66a3e0 #c285ff #888888 #a10000 #b26b00 #b2b200 #006100 #0047b2 #6b24b2 #444444 #5c0000 #663d00 #666600 #003700 #002966 #3d1466".split(" "),ra=[!1,"serif","monospace"],Ba=["1","2","3",!1],ma=["small",!1,"large",
"huge"];da=function(f){function z(e,f){y(this,z);var h=x(this,(z.__proto__||Object.getPrototypeOf(z)).call(this,e,f));e.emitter.listenDOM("click",document.body,function Da(f){if(!document.body.contains(e.root))return document.body.removeEventListener("click",Da);null==h.tooltip||h.tooltip.root.contains(f.target)||document.activeElement===h.tooltip.textbox||h.quill.hasFocus()||h.tooltip.hide();null!=h.pickers&&h.pickers.forEach(function(e){e.container.contains(f.target)||e.close()})});return h}w(z,
f);h(z,[{key:"addModule",value:function(e){var f=r(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"addModule",this).call(this,e);"toolbar"===e&&this.extendToolbar(f);return f}},{key:"buildButtons",value:function(e,f){e.forEach(function(e){(e.getAttribute("class")||"").split(/\s+/).forEach(function(h){if(h.startsWith("ql-")&&(h=h.slice(3),null!=f[h]))if("direction"===h)e.innerHTML=f[h][""]+f[h].rtl;else if("string"===typeof f[h])e.innerHTML=f[h];else{var n=e.value||"";null!=n&&f[h][n]&&
(e.innerHTML=f[h][n])}})})}},{key:"buildPickers",value:function(f,h){var r=this;this.pickers=f.map(function(f){if(f.classList.contains("ql-align"))return null==f.querySelector("option")&&e(f,ta),new qa.default(f,h.align);if(f.classList.contains("ql-background")||f.classList.contains("ql-color")){var n=f.classList.contains("ql-background")?"background":"color";null==f.querySelector("option")&&e(f,ua,"background"===n?"#ffffff":"#000000");return new oa.default(f,h[n])}null==f.querySelector("option")&&
(f.classList.contains("ql-font")?e(f,ra):f.classList.contains("ql-header")?e(f,Ba):f.classList.contains("ql-size")&&e(f,ma));return new ka.default(f)});this.quill.on(n.default.events.EDITOR_CHANGE,function(){r.pickers.forEach(function(e){e.update()})})}}]);return z}(aa.default);da.DEFAULTS=(0,f.default)(!0,{},aa.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var e=this,f=this.container.querySelector("input.ql-image[type=file]");
null==f&&(f=document.createElement("input"),f.setAttribute("type","file"),f.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),f.classList.add("ql-image"),f.addEventListener("change",function(){if(null!=f.files&&null!=f.files[0]){var h=new FileReader;h.onload=function(h){var r=e.quill.getSelection(!0);e.quill.updateContents((new ea.default).retain(r.index).delete(r.length).insert({image:h.target.result}),n.default.sources.USER);e.quill.setSelection(r.index+1,n.default.sources.SILENT);
f.value=""};h.readAsDataURL(f.files[0])}}),this.container.appendChild(f));f.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});f=function(e){function f(e,h){y(this,f);e=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));e.textbox=e.root.querySelector('input[type="text"]');e.listen();return e}w(f,e);h(f,[{key:"listen",value:function(){var e=this;this.textbox.addEventListener("keydown",function(f){fa.default.match(f,"enter")?(e.save(),f.preventDefault()):fa.default.match(f,
"escape")&&(e.cancel(),f.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"link",f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden");this.root.classList.add("ql-editing");null!=f?this.textbox.value=f:e!==this.root.getAttribute("data-mode")&&(this.textbox.value="");this.position(this.quill.getBounds(this.quill.selection.savedRange));this.textbox.select();
this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+e)||"");this.root.setAttribute("data-mode",e)}},{key:"restoreFocus",value:function(){var e=this.quill.scrollingContainer.scrollTop;this.quill.focus();this.quill.scrollingContainer.scrollTop=e}},{key:"save",value:function(){var e=this.textbox.value;switch(this.root.getAttribute("data-mode")){case "link":var f=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",e,n.default.sources.USER),delete this.linkRange):
(this.restoreFocus(),this.quill.format("link",e,n.default.sources.USER));this.quill.root.scrollTop=f;break;case "video":e=(f=e.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||e.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?(f[1]||"https")+"://www.youtube.com/embed/"+f[2]+"?showinfo=0":(f=e.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(f[1]||"https")+"://player.vimeo.com/video/"+f[2]+"/":e;case "formula":e&&(f=this.quill.getSelection(!0),
null!=f&&(f=f.index+f.length,this.quill.insertEmbed(f,this.root.getAttribute("data-mode"),e,n.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(f+1," ",n.default.sources.USER),this.quill.setSelection(f+2,n.default.sources.USER)))}this.textbox.value="";this.hide()}}]);return f}(ha.default);ba.BaseTooltip=f;ba.default=da},function(f,ba){Object.defineProperty(ba,"__esModule",{value:!0});f=function(){function f(){this.head=this.tail=null;this.length=0}f.prototype.append=
function(){for(var f=[],y=0;y<arguments.length;y++)f[y]=arguments[y];this.insertBefore(f[0],null);1<f.length&&this.append.apply(this,f.slice(1))};f.prototype.contains=function(f){for(var y,x=this.iterator();y=x();)if(y===f)return!0;return!1};f.prototype.insertBefore=function(f,y){f&&(f.next=y,null!=y?(f.prev=y.prev,null!=y.prev&&(y.prev.next=f),y.prev=f,y===this.head&&(this.head=f)):null!=this.tail?(this.tail.next=f,f.prev=this.tail,this.tail=f):(f.prev=null,this.head=this.tail=f),this.length+=1)};
f.prototype.offset=function(f){for(var y=0,x=this.head;null!=x;){if(x===f)return y;y+=x.length();x=x.next}return-1};f.prototype.remove=function(f){this.contains(f)&&(null!=f.prev&&(f.prev.next=f.next),null!=f.next&&(f.next.prev=f.prev),f===this.head&&(this.head=f.next),f===this.tail&&(this.tail=f.prev),--this.length)};f.prototype.iterator=function(f){void 0===f&&(f=this.head);return function(){var y=f;null!=f&&(f=f.next);return y}};f.prototype.find=function(f,y){void 0===y&&(y=!1);for(var x,w=this.iterator();x=
w();){var e=x.length();if(f<e||y&&f===e&&(null==x.next||0!==x.next.length()))return[x,f];f-=e}return[null,0]};f.prototype.forEach=function(f){for(var y,x=this.iterator();y=x();)f(y)};f.prototype.forEachAt=function(f,y,x){if(!(0>=y))for(var w=this.find(f),e=f-w[1],h=this.iterator(w[0]);(w=h())&&e<f+y;){var r=w.length();f>e?x(w,f-e,Math.min(y,e+r-f)):x(w,0,Math.min(r,f+y-e));e+=r}};f.prototype.map=function(f){return this.reduce(function(y,x){y.push(f(x));return y},[])};f.prototype.reduce=function(f,
y){for(var x,w=this.iterator();x=w();)y=f(y,x);return y};return f}();ba.default=f},function(f,ba,da){var z=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(f,r){function h(){this.constructor=f}e(f,r);f.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});var y=da(17),x=da(1),w=
{attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0};f=function(e){function f(f){var h=e.call(this,f)||this;h.scroll=h;h.observer=new MutationObserver(function(e){h.update(e)});h.observer.observe(h.domNode,w);h.attach();return h}z(f,e);f.prototype.detach=function(){e.prototype.detach.call(this);this.observer.disconnect()};f.prototype.deleteAt=function(f,h){this.update();0===f&&h===this.length()?this.children.forEach(function(e){e.remove()}):e.prototype.deleteAt.call(this,
f,h)};f.prototype.formatAt=function(f,h,w,n){this.update();e.prototype.formatAt.call(this,f,h,w,n)};f.prototype.insertAt=function(f,h,w){this.update();e.prototype.insertAt.call(this,f,h,w)};f.prototype.optimize=function(f,h){function r(e){null!=e.domNode[x.DATA_KEY]&&null!=e.domNode[x.DATA_KEY].mutations&&(e instanceof y.default&&e.children.forEach(r),e.optimize(h))}function n(e,f){void 0===f&&(f=!0);null!=e&&e!==w&&null!=e.domNode.parentNode&&(null==e.domNode[x.DATA_KEY].mutations&&(e.domNode[x.DATA_KEY].mutations=
[]),f&&n(e.parent))}var w=this;void 0===f&&(f=[]);void 0===h&&(h={});e.prototype.optimize.call(this,h);for(var z=[].slice.call(this.observer.takeRecords());0<z.length;)f.push(z.pop());for(var aa=f,ca=0;0<aa.length;ca+=1){if(100<=ca)throw Error("[Parchment] Maximum optimize iterations reached");aa.forEach(function(e){var f=x.find(e.target,!0);null!=f&&(f.domNode===e.target&&("childList"===e.type?(n(x.find(e.previousSibling,!1)),[].forEach.call(e.addedNodes,function(e){e=x.find(e,!1);n(e,!1);e instanceof
y.default&&e.children.forEach(function(e){n(e,!1)})})):"attributes"===e.type&&n(f.prev)),n(f))});this.children.forEach(r);aa=[].slice.call(this.observer.takeRecords());for(z=aa.slice();0<z.length;)f.push(z.pop())}};f.prototype.update=function(f,h){var w=this;void 0===h&&(h={});f=f||this.observer.takeRecords();f.map(function(e){var f=x.find(e.target,!0);if(null==f)return null;if(null==f.domNode[x.DATA_KEY].mutations)return f.domNode[x.DATA_KEY].mutations=[e],f;f.domNode[x.DATA_KEY].mutations.push(e);
return null}).forEach(function(e){null!=e&&e!==w&&null!=e.domNode[x.DATA_KEY]&&e.update(e.domNode[x.DATA_KEY].mutations||[],h)});null!=this.domNode[x.DATA_KEY].mutations&&e.prototype.update.call(this,this.domNode[x.DATA_KEY].mutations,h);this.optimize(f,h)};f.blotName="scroll";f.defaultChild="block";f.scope=x.Scope.BLOCK_BLOT;f.tagName="DIV";return f}(y.default);ba.default=f},function(f,ba,da){var z=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,
f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(e,h){function w(){this.constructor=e}f(e,h);e.prototype=null===h?Object.create(h):(w.prototype=h.prototype,new w)}}();Object.defineProperty(ba,"__esModule",{value:!0});var y=da(18),x=da(1);f=function(f){function e(){return null!==f&&f.apply(this,arguments)||this}z(e,f);e.formats=function(h){if(h.tagName!==e.tagName)return f.formats.call(this,h)};e.prototype.format=function(h,w){var r=this;h!==this.statics.blotName||
w?f.prototype.format.call(this,h,w):(this.children.forEach(function(f){f instanceof y.default||(f=f.wrap(e.blotName,!0));r.attributes.copy(f)}),this.unwrap())};e.prototype.formatAt=function(e,w,y,z){null!=this.formats()[y]||x.query(y,x.Scope.ATTRIBUTE)?this.isolate(e,w).format(y,z):f.prototype.formatAt.call(this,e,w,y,z)};e.prototype.optimize=function(h){f.prototype.optimize.call(this,h);h=this.formats();if(0===Object.keys(h).length)return this.unwrap();var w=this.next,x;if(x=w instanceof e&&w.prev===
this)a:if(x=w.formats(),Object.keys(h).length!==Object.keys(x).length)x=!1;else{for(var y in h)if(h[y]!==x[y]){x=!1;break a}x=!0}x&&(w.moveChildren(this),w.remove())};e.blotName="inline";e.scope=x.Scope.INLINE_BLOT;e.tagName="SPAN";return e}(y.default);ba.default=f},function(f,ba,da){var z=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,e){f.__proto__=e}||function(f,e){for(var h in e)e.hasOwnProperty(h)&&(f[h]=e[h])};return function(w,e){function h(){this.constructor=
w}f(w,e);w.prototype=null===e?Object.create(e):(h.prototype=e.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});f=da(18);var y=da(1);da=function(f){function w(){return null!==f&&f.apply(this,arguments)||this}z(w,f);w.formats=function(e){var h=y.query(w.blotName).tagName;if(e.tagName!==h)return f.formats.call(this,e)};w.prototype.format=function(e,h){null!=y.query(e,y.Scope.BLOCK)&&(e!==this.statics.blotName||h?f.prototype.format.call(this,e,h):this.replaceWith(w.blotName))};w.prototype.formatAt=
function(e,h,w,x){null!=y.query(w,y.Scope.BLOCK)?this.format(w,x):f.prototype.formatAt.call(this,e,h,w,x)};w.prototype.insertAt=function(e,h,w){null==w||null!=y.query(h,y.Scope.INLINE)?f.prototype.insertAt.call(this,e,h,w):(e=this.split(e),h=y.create(h,w),e.parent.insertBefore(h,e))};w.prototype.update=function(e,h){navigator.userAgent.match(/Trident/)?this.build():f.prototype.update.call(this,e,h)};w.blotName="block";w.scope=y.Scope.BLOCK_BLOT;w.tagName="P";return w}(f.default);ba.default=da},function(f,
ba,da){var z=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,w){f.__proto__=w}||function(f,w){for(var e in w)w.hasOwnProperty(e)&&(f[e]=w[e])};return function(x,w){function e(){this.constructor=x}f(x,w);x.prototype=null===w?Object.create(w):(e.prototype=w.prototype,new e)}}();Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function x(){return null!==f&&f.apply(this,arguments)||this}z(x,f);x.formats=function(){};x.prototype.format=
function(w,e){f.prototype.formatAt.call(this,0,this.length(),w,e)};x.prototype.formatAt=function(w,e,h,r){0===w&&e===this.length()?this.format(h,r):f.prototype.formatAt.call(this,w,e,h,r)};x.prototype.formats=function(){return this.statics.formats(this.domNode)};return x}(da(19).default);ba.default=f},function(f,ba,da){var z=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,e){f.__proto__=e}||function(f,e){for(var h in e)e.hasOwnProperty(h)&&
(f[h]=e[h])};return function(w,e){function h(){this.constructor=w}f(w,e);w.prototype=null===e?Object.create(e):(h.prototype=e.prototype,new h)}}();Object.defineProperty(ba,"__esModule",{value:!0});f=da(19);var y=da(1);da=function(f){function w(e){e=f.call(this,e)||this;e.text=e.statics.value(e.domNode);return e}z(w,f);w.create=function(e){return document.createTextNode(e)};w.value=function(e){e=e.data;e.normalize&&(e=e.normalize());return e};w.prototype.deleteAt=function(e,f){this.domNode.data=this.text=
this.text.slice(0,e)+this.text.slice(e+f)};w.prototype.index=function(e,f){return this.domNode===e?f:-1};w.prototype.insertAt=function(e,h,w){null==w?(this.text=this.text.slice(0,e)+h+this.text.slice(e),this.domNode.data=this.text):f.prototype.insertAt.call(this,e,h,w)};w.prototype.length=function(){return this.text.length};w.prototype.optimize=function(e){f.prototype.optimize.call(this,e);this.text=this.statics.value(this.domNode);0===this.text.length?this.remove():this.next instanceof w&&this.next.prev===
this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())};w.prototype.position=function(e){return[this.domNode,e]};w.prototype.split=function(e,f){void 0===f&&(f=!1);if(!f){if(0===e)return this;if(e===this.length())return this.next}e=y.create(this.domNode.splitText(e));this.parent.insertBefore(e,this.next);this.text=this.statics.value(this.domNode);return e};w.prototype.update=function(e){var f=this;e.some(function(e){return"characterData"===e.type&&e.target===f.domNode})&&(this.text=
this.statics.value(this.domNode))};w.prototype.value=function(){return this.text};w.blotName="text";w.scope=y.Scope.INLINE_BLOT;return w}(f.default);ba.default=da},function(){var f=document.createElement("div");f.classList.toggle("test-class",!1);if(f.classList.contains("test-class")){var ba=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(f,z){return 1<arguments.length&&!this.contains(f)===!z?z:ba.call(this,f)}}String.prototype.startsWith||(String.prototype.startsWith=function(f,
z){return this.substr(z||0,f.length)===f});String.prototype.endsWith||(String.prototype.endsWith=function(f,z){var y=this.toString();if("number"!==typeof z||!isFinite(z)||Math.floor(z)!==z||z>y.length)z=y.length;z-=f.length;f=y.indexOf(f,z);return-1!==f&&f===z});Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(f,z){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!==typeof f)throw new TypeError("predicate must be a function");
for(var y=Object(this),x=y.length>>>0,w,e=0;e<x;e++)if(w=y[e],f.call(z,w,e,y))return w}});document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1);document.execCommand("autoUrlDetect",!1,!1)})},function(f){function z(f,n,w){if(f==n)return f?[[0,f]]:[];if(0>w||f.length<w)w=null;var z=y(f,n),aa=f.substring(0,z);f=f.substring(z);n=n.substring(z);z=x(f,n);var ca=f.substring(f.length-z);f=f.substring(0,f.length-z);n=n.substring(0,n.length-z);f=ba(f,n);
aa&&f.unshift([0,aa]);ca&&f.push([0,ca]);e(f);null!=w&&(f=h(f,w));return f=r(f)}function ba(e,f){if(!e)return[[1,f]];if(!f)return[[-1,e]];var h=e.length>f.length?e:f;var n=e.length>f.length?f:e,r=h.indexOf(n);if(-1!=r)return h=[[1,h.substring(0,r)],[0,n],[1,h.substring(r+n.length)]],e.length>f.length&&(h[0][0]=h[2][0]=-1),h;if(1==n.length)return[[-1,e],[1,f]];if(h=w(e,f))return f=h[1],n=h[3],e=h[4],h=z(h[0],h[2]),f=z(f,n),h.concat([[0,e]],f);a:{h=e.length;n=f.length;r=Math.ceil((h+n)/2);for(var x=
2*r,y=Array(x),aa=Array(x),ba=0;ba<x;ba++)y[ba]=-1,aa[ba]=-1;y[r+1]=0;aa[r+1]=0;ba=h-n;for(var da=0!=ba%2,ea=0,fa=0,ha=0,ja=0,Fa=0;Fa<r;Fa++){for(var Aa=-Fa+ea;Aa<=Fa-fa;Aa+=2){var va=r+Aa;var za=Aa==-Fa||Aa!=Fa&&y[va-1]<y[va+1]?y[va+1]:y[va-1]+1;for(var Ca=za-Aa;za<h&&Ca<n&&e.charAt(za)==f.charAt(Ca);)za++,Ca++;y[va]=za;if(za>h)fa+=2;else if(Ca>n)ea+=2;else if(da&&(va=r+ba-Aa,0<=va&&va<x&&-1!=aa[va])){var sa=h-aa[va];if(za>=sa){e=ca(e,f,za,Ca);break a}}}for(Aa=-Fa+ha;Aa<=Fa-ja;Aa+=2){va=r+Aa;sa=
Aa==-Fa||Aa!=Fa&&aa[va-1]<aa[va+1]?aa[va+1]:aa[va-1]+1;for(za=sa-Aa;sa<h&&za<n&&e.charAt(h-sa-1)==f.charAt(n-za-1);)sa++,za++;aa[va]=sa;if(sa>h)ja+=2;else if(za>n)ha+=2;else if(!da&&(va=r+ba-Aa,0<=va&&va<x&&-1!=y[va]&&(za=y[va],Ca=r+za-va,sa=h-sa,za>=sa))){e=ca(e,f,za,Ca);break a}}}e=[[-1,e],[1,f]]}return e}function ca(e,f,h,w){var n=e.substring(h),r=f.substring(w);e=z(e.substring(0,h),f.substring(0,w));n=z(n,r);return e.concat(n)}function y(e,f){if(!e||!f||e.charAt(0)!=f.charAt(0))return 0;for(var h=
0,n=Math.min(e.length,f.length),w=n,r=0;h<w;)e.substring(r,w)==f.substring(r,w)?r=h=w:n=w,w=Math.floor((n-h)/2+h);return w}function x(e,f){if(!e||!f||e.charAt(e.length-1)!=f.charAt(f.length-1))return 0;for(var h=0,n=Math.min(e.length,f.length),w=n,r=0;h<w;)e.substring(e.length-w,e.length-r)==f.substring(f.length-w,f.length-r)?r=h=w:n=w,w=Math.floor((n-h)/2+h);return w}function w(e,f){function h(e,f,h){for(var n=e.substring(h,h+Math.floor(e.length/4)),w=-1,r="",z,aa,ca,ba;-1!=(w=f.indexOf(n,w+1));){var da=
y(e.substring(h),f.substring(w)),ea=x(e.substring(0,h),f.substring(0,w));r.length<ea+da&&(r=f.substring(w-ea,w)+f.substring(w,w+da),z=e.substring(0,h-ea),aa=e.substring(h+da),ca=f.substring(0,w-ea),ba=f.substring(w+da))}return 2*r.length>=e.length?[z,aa,ca,ba,r]:null}var n=e.length>f.length?e:f,w=e.length>f.length?f:e;if(4>n.length||2*w.length<n.length)return null;var r=h(n,w,Math.ceil(n.length/4));n=h(n,w,Math.ceil(n.length/2));if(r||n)r=n?r?r[4].length>n[4].length?r:n:n:r;else return null;e.length>
f.length?(e=r[0],f=r[1],n=r[2],w=r[3]):(n=r[0],w=r[1],e=r[2],f=r[3]);return[e,f,n,w,r[4]]}function e(f){f.push([0,""]);for(var h=0,w=0,r=0,z="",aa="",ca;h<f.length;)switch(f[h][0]){case 1:r++;aa+=f[h][1];h++;break;case -1:w++;z+=f[h][1];h++;break;case 0:1<w+r?(0!==w&&0!==r&&(ca=y(aa,z),0!==ca&&(0<h-w-r&&0==f[h-w-r-1][0]?f[h-w-r-1][1]+=aa.substring(0,ca):(f.splice(0,0,[0,aa.substring(0,ca)]),h++),aa=aa.substring(ca),z=z.substring(ca)),ca=x(aa,z),0!==ca&&(f[h][1]=aa.substring(aa.length-ca)+f[h][1],
aa=aa.substring(0,aa.length-ca),z=z.substring(0,z.length-ca))),0===w?f.splice(h-r,w+r,[1,aa]):0===r?f.splice(h-w,w+r,[-1,z]):f.splice(h-w-r,w+r,[-1,z],[1,aa]),h=h-w-r+(w?1:0)+(r?1:0)+1):0!==h&&0==f[h-1][0]?(f[h-1][1]+=f[h][1],f.splice(h,1)):h++,w=r=0,aa=z=""}""===f[f.length-1][1]&&f.pop();w=!1;for(h=1;h<f.length-1;)0==f[h-1][0]&&0==f[h+1][0]&&(f[h][1].substring(f[h][1].length-f[h-1][1].length)==f[h-1][1]?(f[h][1]=f[h-1][1]+f[h][1].substring(0,f[h][1].length-f[h-1][1].length),f[h+1][1]=f[h-1][1]+f[h+
1][1],f.splice(h-1,1),w=!0):f[h][1].substring(0,f[h+1][1].length)==f[h+1][1]&&(f[h-1][1]+=f[h+1][1],f[h][1]=f[h][1].substring(f[h+1][1].length)+f[h+1][1],f.splice(h+1,1),w=!0)),h++;w&&e(f)}function h(e,f){a:{var h=e;if(0===f)var n=[0,h];else{var w=0;for(n=0;n<h.length;n++){var r=h[n];if(-1===r[0]||0===r[0]){var x=w+r[1].length;if(f===x){n=[n+1,h];break a}if(f<x){h=h.slice();w=f-w;f=[r[0],r[1].slice(0,w)];r=[r[0],r[1].slice(w)];h.splice(n,1,f,r);n=[n+1,h];break a}w=x}}throw Error("cursor_pos is out of bounds!");
}}h=n[1];n=n[0];f=h[n];r=h[n+1];return null==f||0!==f[0]?e:null!=r&&f[1]+r[1]===r[1]+f[1]?(h.splice(n,2,r,f),aa(h,n,2)):null!=r&&0===r[1].indexOf(f[1])?(h.splice(n,2,[r[0],f[1]],[0,f[1]]),e=r[1].slice(f[1].length),0<e.length&&h.splice(n+2,0,[r[0],e]),aa(h,n,3)):e}function r(e){function f(e){return 55296<=e.charCodeAt(e.length-1)&&56319>=e.charCodeAt(e.length-1)}function h(e){return 56320<=e.charCodeAt(0)&&57343>=e.charCodeAt(0)}for(var w=!1,r=2;r<e.length;r+=1)0===e[r-2][0]&&f(e[r-2][1])&&-1===e[r-
1][0]&&h(e[r-1][1])&&1===e[r][0]&&h(e[r][1])&&(w=!0,e[r-1][1]=e[r-2][1].slice(-1)+e[r-1][1],e[r][1]=e[r-2][1].slice(-1)+e[r][1],e[r-2][1]=e[r-2][1].slice(0,-1));if(!w)return e;w=[];for(r=0;r<e.length;r+=1)0<e[r][1].length&&w.push(e[r]);return w}function aa(e,f,h){for(h=f+h-1;0<=h&&h>=f-1;h--)if(h+1<e.length){var n=e[h],w=e[h+1];n[0]===w[1]&&e.splice(h,2,[n[0],n[1]+w[1]])}return e}z.INSERT=1;z.DELETE=-1;z.EQUAL=0;f.exports=z},function(f,ba){function z(f){var y=[],x;for(x in f)y.push(x);return y}ba=
f.exports="function"===typeof Object.keys?Object.keys:z;ba.shim=z},function(f,ba){function z(f){return"[object Arguments]"==Object.prototype.toString.call(f)}function ca(f){return f&&"object"==typeof f&&"number"==typeof f.length&&Object.prototype.hasOwnProperty.call(f,"callee")&&!Object.prototype.propertyIsEnumerable.call(f,"callee")||!1}ba="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();ba=f.exports=ba?z:ca;ba.supported=z;ba.unsupported=ca},function(f){function z(){}
function ba(f,e,h){this.fn=f;this.context=e;this.once=h||!1}function ca(){this._events=new z;this._eventsCount=0}var y=Object.prototype.hasOwnProperty,x="~";Object.create&&(z.prototype=Object.create(null),(new z).__proto__||(x=!1));ca.prototype.eventNames=function(){var f=[],e,h;if(0===this._eventsCount)return f;for(h in e=this._events)y.call(e,h)&&f.push(x?h.slice(1):h);return Object.getOwnPropertySymbols?f.concat(Object.getOwnPropertySymbols(e)):f};ca.prototype.listeners=function(f,e){f=this._events[x?
x+f:f];if(e)return!!f;if(!f)return[];if(f.fn)return[f.fn];e=0;for(var h=f.length,w=Array(h);e<h;e++)w[e]=f[e].fn;return w};ca.prototype.emit=function(f,e,h,r,y,z){var n=x?x+f:f;if(!this._events[n])return!1;n=this._events[n];var w=arguments.length,aa;if(n.fn){n.once&&this.removeListener(f,n.fn,void 0,!0);switch(w){case 1:return n.fn.call(n.context),!0;case 2:return n.fn.call(n.context,e),!0;case 3:return n.fn.call(n.context,e,h),!0;case 4:return n.fn.call(n.context,e,h,r),!0;case 5:return n.fn.call(n.context,
e,h,r,y),!0;case 6:return n.fn.call(n.context,e,h,r,y,z),!0}var ca=1;for(aa=Array(w-1);ca<w;ca++)aa[ca-1]=arguments[ca];n.fn.apply(n.context,aa)}else{var ba=n.length;for(ca=0;ca<ba;ca++)switch(n[ca].once&&this.removeListener(f,n[ca].fn,void 0,!0),w){case 1:n[ca].fn.call(n[ca].context);break;case 2:n[ca].fn.call(n[ca].context,e);break;case 3:n[ca].fn.call(n[ca].context,e,h);break;case 4:n[ca].fn.call(n[ca].context,e,h,r);break;default:if(!aa){var da=1;for(aa=Array(w-1);da<w;da++)aa[da-1]=arguments[da]}n[ca].fn.apply(n[ca].context,
aa)}}return!0};ca.prototype.on=function(f,e,h){e=new ba(e,h||this);f=x?x+f:f;this._events[f]?this._events[f].fn?this._events[f]=[this._events[f],e]:this._events[f].push(e):(this._events[f]=e,this._eventsCount++);return this};ca.prototype.once=function(f,e,h){e=new ba(e,h||this,!0);f=x?x+f:f;this._events[f]?this._events[f].fn?this._events[f]=[this._events[f],e]:this._events[f].push(e):(this._events[f]=e,this._eventsCount++);return this};ca.prototype.removeListener=function(f,e,h,r){f=x?x+f:f;if(!this._events[f])return this;
if(!e)return 0===--this._eventsCount?this._events=new z:delete this._events[f],this;var w=this._events[f];if(w.fn)w.fn!==e||r&&!w.once||h&&w.context!==h||(0===--this._eventsCount?this._events=new z:delete this._events[f]);else{for(var y=0,n=[],ca=w.length;y<ca;y++)(w[y].fn!==e||r&&!w[y].once||h&&w[y].context!==h)&&n.push(w[y]);n.length?this._events[f]=1===n.length?n[0]:n:0===--this._eventsCount?this._events=new z:delete this._events[f]}return this};ca.prototype.removeAllListeners=function(f){f?(f=
x?x+f:f,this._events[f]&&(0===--this._eventsCount?this._events=new z:delete this._events[f])):(this._events=new z,this._eventsCount=0);return this};ca.prototype.off=ca.prototype.removeListener;ca.prototype.addListener=ca.prototype.on;ca.prototype.setMaxListeners=function(){return this};ca.prefixed=x;ca.EventEmitter=ca;"undefined"!==typeof f&&(f.exports=ca)},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f,h){f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,
configurable:!0,writable:!0}):e[f]=h;return e}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,
f):e.__proto__=f)}function e(f,h,n){return"object"===("undefined"===typeof h?"undefined":ta(h))?Object.keys(h).reduce(function(f,n){return e(f,n,h[n])},f):f.reduce(function(e,f){return f.attributes&&f.attributes[h]?e.push(f):e.insert(f.insert,(0,Ba.default)({},y({},h,n),f.attributes))},new ma.default)}function h(e){return e.nodeType!==Node.ELEMENT_NODE?{}:e["__ql-computed-style"]||(e["__ql-computed-style"]=window.getComputedStyle(e))}function r(e,f){for(var h="",n=e.ops.length-1;0<=n&&h.length<f.length;--n){var r=
e.ops[n];if("string"!==typeof r.insert)break;h=r.insert+h}return h.slice(-1*f.length)===f}function aa(e){if(0===e.childNodes.length)return!1;e=h(e);return-1<["block","list-item"].indexOf(e.display)}function ea(e,f,h){return e.nodeType===e.TEXT_NODE?h.reduce(function(f,h){return h(e,f)},new ma.default):e.nodeType===e.ELEMENT_NODE?[].reduce.call(e.childNodes||[],function(n,r){var w=ea(r,f,h);r.nodeType===e.ELEMENT_NODE&&(w=f.reduce(function(e,f){return f(r,e)},w),w=(r["__ql-matcher"]||[]).reduce(function(e,
f){return f(r,e)},w));return n.concat(w)},new ma.default):new ma.default}function n(f,h,n){return e(n,f,!0)}function fa(f,h){var n=la.default.Attributor.Attribute.keys(f),r=la.default.Attributor.Class.keys(f),w=la.default.Attributor.Style.keys(f),x={};n.concat(r).concat(w).forEach(function(e){var h=la.default.query(e,la.default.Scope.ATTRIBUTE);if(null!=h&&(x[h.attrName]=h.value(f),x[h.attrName]))return;h=Ka[e];null==h||h.attrName!==e&&h.keyName!==e||(x[h.attrName]=h.value(f)||void 0);h=wa[e];null==
h||h.attrName!==e&&h.keyName!==e||(h=wa[e],x[h.attrName]=h.value(f)||void 0)});0<Object.keys(x).length&&(h=e(h,x));return h}function ha(f,h){var n=la.default.query(f);if(null==n)return h;if(n.prototype instanceof la.default.Embed){var r={},w=n.value(f);null!=w&&(r[n.blotName]=w,h=(new ma.default).insert(r,n.formats(f)))}else"function"===typeof n.formats&&(h=e(h,n.blotName,n.formats(f)));return h}function oa(e,f){r(f,"\n")||(aa(e)||0<f.length()&&e.nextSibling&&aa(e.nextSibling))&&f.insert("\n");return f}
function qa(e,f){if(aa(e)&&null!=e.nextElementSibling&&!r(f,"\n\n")){var n=e.offsetHeight+parseFloat(h(e).marginTop)+parseFloat(h(e).marginBottom);e.nextElementSibling.offsetTop>e.offsetTop*****n&&f.insert("\n")}return f}function ka(e,f){var n=e.data;if("O:P"===e.parentNode.tagName)return f.insert(n.trim());if(0===n.trim().length&&e.parentNode.classList.contains("ql-clipboard"))return f;if(!h(e.parentNode).whiteSpace.startsWith("pre")){var r=function(e,f){f=f.replace(/[^\u00a0]/g,"");return 1>f.length&&
e?" ":f};n=n.replace(/\r\n/g," ").replace(/\n/g," ");n=n.replace(/\s\s+/g,r.bind(r,!0));if(null==e.previousSibling&&aa(e.parentNode)||null!=e.previousSibling&&aa(e.previousSibling))n=n.replace(/^\s+/,r.bind(r,!1));if(null==e.nextSibling&&aa(e.parentNode)||null!=e.nextSibling&&aa(e.nextSibling))n=n.replace(/\s+$/,r.bind(r,!1))}return f.insert(n)}Object.defineProperty(ba,"__esModule",{value:!0});ba.matchText=ba.matchSpacing=ba.matchNewline=ba.matchBlot=ba.matchAttributor=ba.default=void 0;var ta="function"===
typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ua=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(Sa){r=!0,w=Sa}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;
}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),ra=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(3);var Ba=z(f);f=da(2);var ma=z(f);f=da(0);var la=z(f);f=da(5);var ya=z(f);f=da(10);f=z(f);var Fa=da(9);Fa=z(Fa);var Aa=da(36),va=da(37),za=da(13),Ca=z(za);za=
da(26);var sa=da(38),Da=da(39);da=da(40);var Ea=(0,f.default)("quill:clipboard"),Ia=[[Node.TEXT_NODE,ka],[Node.TEXT_NODE,oa],["br",function(e,f){r(f,"\n")||f.insert("\n");return f}],[Node.ELEMENT_NODE,oa],[Node.ELEMENT_NODE,ha],[Node.ELEMENT_NODE,qa],[Node.ELEMENT_NODE,fa],[Node.ELEMENT_NODE,function(f,n){var r={},w=f.style||{};w.fontStyle&&"italic"===h(f).fontStyle&&(r.italic=!0);w.fontWeight&&(h(f).fontWeight.startsWith("bold")||700<=parseInt(h(f).fontWeight))&&(r.bold=!0);0<Object.keys(r).length&&
(n=e(n,r));0<parseFloat(w.textIndent||0)&&(n=(new ma.default).insert("\t").concat(n));return n}],["li",function(e,f){var h=la.default.query(e);if(null==h||"list-item"!==h.blotName||!r(f,"\n"))return f;h=-1;for(e=e.parentNode;!e.classList.contains("ql-clipboard");)"list"===(la.default.query(e)||{}).blotName&&(h+=1),e=e.parentNode;return 0>=h?f:f.compose((new ma.default).retain(f.length()-1).retain(1,{indent:h}))}],["b",n.bind(n,"bold")],["i",n.bind(n,"italic")],["style",function(){return new ma.default}]],
Ka=[Aa.AlignAttribute,sa.DirectionAttribute].reduce(function(e,f){e[f.keyName]=f;return e},{}),wa=[Aa.AlignStyle,va.BackgroundStyle,za.ColorStyle,sa.DirectionStyle,Da.FontStyle,da.SizeStyle].reduce(function(e,f){e[f.keyName]=f;return e},{});da=function(e){function f(e,h){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var n=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));n.quill.root.addEventListener("paste",n.onPaste.bind(n));n.container=n.quill.addContainer("ql-clipboard");
n.container.setAttribute("contenteditable",!0);n.container.setAttribute("tabindex",-1);n.matchers=[];Ia.concat(n.options.matchers).forEach(function(e){e=ua(e,2);var f=e[1];(h.matchVisual||f!==qa)&&n.addMatcher(e[0],f)});return n}w(f,e);ra(f,[{key:"addMatcher",value:function(e,f){this.matchers.push([e,f])}},{key:"convert",value:function(e){if("string"===typeof e)return this.container.innerHTML=e.replace(/>\r?\n +</g,"><"),this.convert();e=this.quill.getFormat(this.quill.selection.savedRange.index);
if(e[Ca.default.blotName]){var f=this.container.innerText;this.container.innerHTML="";return(new ma.default).insert(f,y({},Ca.default.blotName,e[Ca.default.blotName]))}e=this.prepareMatching();e=ua(e,2);e=ea(this.container,e[0],e[1]);r(e,"\n")&&null==e.ops[e.ops.length-1].attributes&&(e=e.compose((new ma.default).retain(e.length()-1).delete(1)));Ea.log("convert",this.container.innerHTML,e);this.container.innerHTML="";return e}},{key:"dangerouslyPasteHTML",value:function(e,f){var h=2<arguments.length&&
void 0!==arguments[2]?arguments[2]:ya.default.sources.API;if("string"===typeof e)this.quill.setContents(this.convert(e),f),this.quill.setSelection(0,ya.default.sources.SILENT);else{var n=this.convert(f);this.quill.updateContents((new ma.default).retain(e).concat(n),h);this.quill.setSelection(e+n.length(),ya.default.sources.SILENT)}}},{key:"onPaste",value:function(e){var f=this;if(!e.defaultPrevented&&this.quill.isEnabled()){var h=this.quill.getSelection(),n=(new ma.default).retain(h.index),r=this.quill.scrollingContainer.scrollTop;
this.container.focus();this.quill.selection.update(ya.default.sources.SILENT);setTimeout(function(){n=n.concat(f.convert()).delete(h.length);f.quill.updateContents(n,ya.default.sources.USER);f.quill.setSelection(n.length()-h.length,ya.default.sources.SILENT);f.quill.scrollingContainer.scrollTop=r;f.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var e=this,f=[],h=[];this.matchers.forEach(function(n){n=ua(n,2);var r=n[0],w=n[1];switch(r){case Node.TEXT_NODE:h.push(w);break;case Node.ELEMENT_NODE:f.push(w);
break;default:[].forEach.call(e.container.querySelectorAll(r),function(e){e["__ql-matcher"]=e["__ql-matcher"]||[];e["__ql-matcher"].push(w)})}});return[f,h]}}]);return f}(Fa.default);da.DEFAULTS={matchers:[],matchVisual:!0};ba.default=da;ba.matchAttributor=fa;ba.matchBlot=ha;ba.matchNewline=oa;ba.matchSpacing=qa;ba.matchText=ka},function(f,ba,da){function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=
Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function f(e,f){for(var h=0;h<f.length;h++){var w=f[h];w.enumerable=w.enumerable||!1;w.configurable=!0;"value"in w&&(w.writable=!0);Object.defineProperty(e,w.key,w)}}return function(e,h,r){h&&f(e.prototype,h);r&&f(e,r);return e}}(),x=function aa(e,f,r){null===e&&(e=Function.prototype);
var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return aa(e,f,r)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(r)}};f=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==
typeof e?this:e}z(f,e);y(f,[{key:"optimize",value:function(e){x(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"optimize",this).call(this,e);this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return x(f.__proto__||Object.getPrototypeOf(f),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]);return f}(function(e){return e&&e.__esModule?e:{default:e}}(da(6)).default);f.blotName="bold";f.tagName=["STRONG",
"B"];ba.default=f},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f,h){f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+
typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function e(e,f,h){var n=document.createElement("button");n.setAttribute("type","button");n.classList.add("ql-"+f);null!=h&&(n.value=h);e.appendChild(n)}function h(f,h){Array.isArray(h[0])||(h=[h]);h.forEach(function(h){var n=document.createElement("span");n.classList.add("ql-formats");h.forEach(function(f){if("string"===
typeof f)e(n,f);else{var h=Object.keys(f)[0];f=f[h];Array.isArray(f)?r(n,h,f):e(n,h,f)}});f.appendChild(n)})}function r(e,f,h){var n=document.createElement("select");n.classList.add("ql-"+f);h.forEach(function(e){var f=document.createElement("option");!1!==e?f.setAttribute("value",e):f.setAttribute("selected","selected");n.appendChild(f)});e.appendChild(n)}Object.defineProperty(ba,"__esModule",{value:!0});ba.addControls=ba.default=void 0;var aa=function(){return function(e,f){if(Array.isArray(e))return e;
if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(ya){r=!0,w=ya}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),ea=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,
n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(2);var n=z(f);f=da(0);var fa=z(f);f=da(5);var ha=z(f);f=da(10);f=z(f);da=da(9);da=z(da);var oa=(0,f.default)("quill:toolbar");da=function(e){function f(e,n){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var r=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,n));Array.isArray(r.options.container)?(n=document.createElement("div"),h(n,r.options.container),e.container.parentNode.insertBefore(n,
e.container),r.container=n):r.container="string"===typeof r.options.container?document.querySelector(r.options.container):r.options.container;if(!(r.container instanceof HTMLElement)){var w;return w=oa.error("Container required for toolbar",r.options),x(r,w)}r.container.classList.add("ql-toolbar");r.controls=[];r.handlers={};Object.keys(r.options.handlers).forEach(function(e){r.addHandler(e,r.options.handlers[e])});[].forEach.call(r.container.querySelectorAll("button, select"),function(e){r.attach(e)});
r.quill.on(ha.default.events.EDITOR_CHANGE,function(e,f){e===ha.default.events.SELECTION_CHANGE&&r.update(f)});r.quill.on(ha.default.events.SCROLL_OPTIMIZE,function(){var e=r.quill.selection.getRange();e=aa(e,1)[0];r.update(e)});return r}w(f,e);ea(f,[{key:"addHandler",value:function(e,f){this.handlers[e]=f}},{key:"attach",value:function(e){var f=this,h=[].find.call(e.classList,function(e){return 0===e.indexOf("ql-")});if(h){h=h.slice(3);"BUTTON"===e.tagName&&e.setAttribute("type","button");if(null==
this.handlers[h]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[h]){oa.warn("ignoring attaching to disabled format",h,e);return}if(null==fa.default.query(h)){oa.warn("ignoring attaching to nonexistent format",h,e);return}}e.addEventListener("SELECT"===e.tagName?"change":"click",function(r){if("SELECT"===e.tagName){if(0>e.selectedIndex)return;var w=e.options[e.selectedIndex];w=w.hasAttribute("selected")?!1:w.value||!1}else w=e.classList.contains("ql-active")?!1:e.value||!e.hasAttribute("value"),
r.preventDefault();f.quill.focus();r=f.quill.selection.getRange();r=aa(r,1)[0];if(null!=f.handlers[h])f.handlers[h].call(f,w);else if(fa.default.query(h).prototype instanceof fa.default.Embed){w=prompt("Enter "+h);if(!w)return;f.quill.updateContents((new n.default).retain(r.index).delete(r.length).insert(y({},h,w)),ha.default.sources.USER)}else f.quill.format(h,w,ha.default.sources.USER);f.update(r)});this.controls.push([h,e])}}},{key:"update",value:function(e){var f=null==e?{}:this.quill.getFormat(e);
this.controls.forEach(function(h){h=aa(h,2);var n=h[0];h=h[1];if("SELECT"===h.tagName){var r=void 0;null==e?r=null:null==f[n]?r=h.querySelector("option[selected]"):Array.isArray(f[n])||(n=f[n],"string"===typeof n&&(n=n.replace(/"/g,'\\"')),r=h.querySelector('option[value="'+n+'"]'));null==r?(h.value="",h.selectedIndex=-1):r.selected=!0}else null==e?h.classList.remove("ql-active"):h.hasAttribute("value")?(n=f[n]===h.getAttribute("value")||null!=f[n]&&f[n].toString()===h.getAttribute("value")||null==
f[n]&&!h.getAttribute("value"),h.classList.toggle("ql-active",n)):h.classList.toggle("ql-active",null!=f[n])})}}]);return f}(da.default);da.DEFAULTS={};da.DEFAULTS={container:null,handlers:{clean:function(){var e=this,f=this.quill.getSelection();null!=f&&(0==f.length?(f=this.quill.getFormat(),Object.keys(f).forEach(function(f){null!=fa.default.query(f,fa.default.Scope.INLINE)&&e.quill.format(f,!1)})):this.quill.removeFormat(f,ha.default.sources.USER))},direction:function(e){var f=this.quill.getFormat().align;
"rtl"===e&&null==f?this.quill.format("align","right",ha.default.sources.USER):e||"right"!==f||this.quill.format("align",!1,ha.default.sources.USER);this.quill.format("direction",e,ha.default.sources.USER)},indent:function(e){var f=this.quill.getFormat(this.quill.getSelection()),h=parseInt(f.indent||0);if("+1"===e||"-1"===e)e="+1"===e?1:-1,"rtl"===f.direction&&(e*=-1),this.quill.format("indent",h+e,ha.default.sources.USER)},link:function(e){!0===e&&(e=prompt("Enter link URL:"));this.quill.format("link",
e,ha.default.sources.USER)},list:function(e){var f=this.quill.getFormat(this.quill.getSelection());"check"===e?"checked"===f.list||"unchecked"===f.list?this.quill.format("list",!1,ha.default.sources.USER):this.quill.format("list","unchecked",ha.default.sources.USER):this.quill.format("list",e,ha.default.sources.USER)}}};ba.default=da;ba.addControls=h},function(f){f.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},
function(f,ba,da){function z(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function y(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,
"__esModule",{value:!0});var x=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,r,w){r&&e(f.prototype,r);w&&e(f,w);return f}}(),w=function ja(f,r,w){null===f&&(f=Function.prototype);var h=Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===
r?void 0:r.call(w)}};f=function(f){function h(f,r){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");f=z(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,f));f.label.innerHTML=r;f.container.classList.add("ql-color-picker");[].slice.call(f.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(f){f.classList.add("ql-primary")});return f}y(h,f);x(h,[{key:"buildItem",value:function(f){var r=w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),
"buildItem",this).call(this,f);r.style.backgroundColor=f.getAttribute("value")||"";return r}},{key:"selectItem",value:function(f,r){w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"selectItem",this).call(this,f,r);r=this.label.querySelector(".ql-color-label");f=f?f.getAttribute("data-value")||"":"";r&&("line"===r.tagName?r.style.stroke=f:r.style.fill=f)}}]);return h}(function(f){return f&&f.__esModule?f:{default:f}}(da(28)).default);ba.default=f},function(f,ba,da){function z(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function y(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var x=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=
r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(f,r,w){r&&e(f.prototype,r);w&&e(f,w);return f}}(),w=function ja(f,r,w){null===f&&(f=Function.prototype);var h=Object.getOwnPropertyDescriptor(f,r);if(void 0===h){if(f=Object.getPrototypeOf(f),null!==f)return ja(f,r,w)}else{if("value"in h)return h.value;r=h.get;return void 0===r?void 0:r.call(w)}};f=function(f){function h(f,r){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");
f=z(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,f));f.container.classList.add("ql-icon-picker");[].forEach.call(f.container.querySelectorAll(".ql-picker-item"),function(f){f.innerHTML=r[f.getAttribute("data-value")||""]});f.defaultItem=f.container.querySelector(".ql-selected");f.selectItem(f.defaultItem);return f}y(h,f);x(h,[{key:"selectItem",value:function(f,r){w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"selectItem",this).call(this,f,r);f=f||this.defaultItem;this.label.innerHTML=
f.innerHTML}}]);return h}(function(f){return f&&f.__esModule?f:{default:f}}(da(28)).default);ba.default=f},function(f,ba){Object.defineProperty(ba,"__esModule",{value:!0});var z=function(){function f(f,x){for(var w=0;w<x.length;w++){var e=x[w];e.enumerable=e.enumerable||!1;e.configurable=!0;"value"in e&&(e.writable=!0);Object.defineProperty(f,e.key,e)}}return function(y,x,w){x&&f(y.prototype,x);w&&f(y,w);return y}}();f=function(){function f(y,x){var w=this;if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");
this.quill=y;this.boundsContainer=x||document.body;this.root=y.addContainer("ql-tooltip");this.root.innerHTML=this.constructor.TEMPLATE;this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){w.root.style.marginTop=-1*w.quill.root.scrollTop+"px"});this.hide()}z(f,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(f){var x=f.left+f.width/2-this.root.offsetWidth/2,w=f.bottom+this.quill.root.scrollTop;this.root.style.left=
x+"px";this.root.style.top=w+"px";this.root.classList.remove("ql-flip");var e=this.boundsContainer.getBoundingClientRect(),h=this.root.getBoundingClientRect(),r=0;h.right>e.right&&(r=e.right-h.right,this.root.style.left=x+r+"px");h.left<e.left&&(r=e.left-h.left,this.root.style.left=x+r+"px");h.bottom>e.bottom&&(this.root.style.top=w-(f.bottom-f.top+(h.bottom-h.top))+"px",this.root.classList.add("ql-flip"));return r}},{key:"show",value:function(){this.root.classList.remove("ql-editing");this.root.classList.remove("ql-hidden")}}]);
return f}();ba.default=f},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=
Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var e=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),y;!(n=(y=x.next()).done)&&(h.push(y.value),!f||h.length!==f);n=!0);}catch(va){r=!0,w=va}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;
}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),h=function la(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return la(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}},r=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,
n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(3);f=z(f);var aa=da(8),ea=z(aa);aa=da(43);var n=z(aa),fa=da(27),ha=z(fa),oa=da(15);da=da(41);var qa=z(da),ka=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];da=function(e){function f(e,h){y(this,f);null!=h.modules.toolbar&&null==h.modules.toolbar.container&&(h.modules.toolbar.container=ka);e=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));
e.quill.container.classList.add("ql-snow");return e}w(f,e);r(f,[{key:"extendToolbar",value:function(e){e.container.classList.add("ql-snow");this.buildButtons([].slice.call(e.container.querySelectorAll("button")),qa.default);this.buildPickers([].slice.call(e.container.querySelectorAll("select")),qa.default);this.tooltip=new ta(this.quill,this.options.bounds);e.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(f,h){e.handlers.link.call(e,!h.format.link)})}}]);
return f}(n.default);da.DEFAULTS=(0,f.default)(!0,{},n.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(e){e?(e=this.quill.getSelection(),null!=e&&0!=e.length&&(e=this.quill.getText(e),/^\S+@\S+\.\S+$/.test(e)&&0!==e.indexOf("mailto:")&&(e="mailto:"+e),this.quill.theme.tooltip.edit("link",e))):this.quill.format("link",!1)}}}}});var ta=function(f){function n(e,f){y(this,n);e=x(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e,f));e.preview=e.root.querySelector("a.ql-preview");return e}
w(n,f);r(n,[{key:"listen",value:function(){var f=this;h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"listen",this).call(this);this.root.querySelector("a.ql-action").addEventListener("click",function(e){f.root.classList.contains("ql-editing")?f.save():f.edit("link",f.preview.textContent);e.preventDefault()});this.root.querySelector("a.ql-remove").addEventListener("click",function(e){if(null!=f.linkRange){var h=f.linkRange;f.restoreFocus();f.quill.formatText(h,"link",!1,ea.default.sources.USER);
delete f.linkRange}e.preventDefault();f.hide()});this.quill.on(ea.default.events.SELECTION_CHANGE,function(h,n,r){if(null!=h){if(0===h.length&&r===ea.default.sources.USER){if(n=f.quill.scroll.descendant(ha.default,h.index),r=e(n,2),n=r[0],r=r[1],null!=n){f.linkRange=new oa.Range(h.index-r,n.length());h=ha.default.formats(n.domNode);f.preview.textContent=h;f.preview.setAttribute("href",h);f.show();f.position(f.quill.getBounds(f.linkRange));return}}else delete f.linkRange;f.hide()}})}},{key:"show",
value:function(){h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"show",this).call(this);this.root.removeAttribute("data-mode")}}]);return n}(aa.BaseTooltip);ta.TEMPLATE='<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-action"></a><a class="ql-remove"></a>';ba.default=da},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}
Object.defineProperty(ba,"__esModule",{value:!0});f=da(29);f=z(f);var y=da(36),x=da(38),w=da(64),e=da(65);e=z(e);var h=da(66);h=z(h);var r=da(67),aa=z(r),ea=da(37),n=da(26),fa=da(39),ha=da(40),oa=da(56);oa=z(oa);var qa=da(68);qa=z(qa);var ka=da(27);ka=z(ka);var ta=da(69);ta=z(ta);var ua=da(70);ua=z(ua);var ra=da(71);ra=z(ra);var Ba=da(72);Ba=z(Ba);var ma=da(73);ma=z(ma);var la=da(13),ya=z(la),Fa=da(74);Fa=z(Fa);var Aa=da(75);Aa=z(Aa);var va=da(57);va=z(va);var za=da(41);za=z(za);var Ca=da(28);Ca=
z(Ca);var sa=da(59);sa=z(sa);var Da=da(60);Da=z(Da);var Ea=da(61);Ea=z(Ea);var Ia=da(108);Ia=z(Ia);da=da(62);da=z(da);f.default.register({"attributors/attribute/direction":x.DirectionAttribute,"attributors/class/align":y.AlignClass,"attributors/class/background":ea.BackgroundClass,"attributors/class/color":n.ColorClass,"attributors/class/direction":x.DirectionClass,"attributors/class/font":fa.FontClass,"attributors/class/size":ha.SizeClass,"attributors/style/align":y.AlignStyle,"attributors/style/background":ea.BackgroundStyle,
"attributors/style/color":n.ColorStyle,"attributors/style/direction":x.DirectionStyle,"attributors/style/font":fa.FontStyle,"attributors/style/size":ha.SizeStyle},!0);f.default.register({"formats/align":y.AlignClass,"formats/direction":x.DirectionClass,"formats/indent":w.IndentClass,"formats/background":ea.BackgroundStyle,"formats/color":n.ColorStyle,"formats/font":fa.FontClass,"formats/size":ha.SizeClass,"formats/blockquote":e.default,"formats/code-block":ya.default,"formats/header":h.default,"formats/list":aa.default,
"formats/bold":oa.default,"formats/code":la.Code,"formats/italic":qa.default,"formats/link":ka.default,"formats/script":ta.default,"formats/strike":ua.default,"formats/underline":ra.default,"formats/image":Ba.default,"formats/video":ma.default,"formats/list/item":r.ListItem,"modules/formula":Fa.default,"modules/syntax":Aa.default,"modules/toolbar":va.default,"themes/bubble":Ia.default,"themes/snow":da.default,"ui/icons":za.default,"ui/picker":Ca.default,"ui/icon-picker":Da.default,"ui/color-picker":sa.default,
"ui/tooltip":Ea.default},!0);ba.default=f.default},function(f,ba,da){function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(ba,"__esModule",{value:!0});ba.IndentClass=void 0;var y=function(){function f(e,f){for(var h=0;h<
f.length;h++){var w=f[h];w.enumerable=w.enumerable||!1;w.configurable=!0;"value"in w&&(w.writable=!0);Object.defineProperty(e,w.key,w)}}return function(e,h,r){h&&f(e.prototype,h);r&&f(e,r);return e}}(),x=function aa(e,f,r){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return aa(e,f,r)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(r)}};f=function(e){return e&&e.__esModule?e:{default:e}}(da(0));
f=new (function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(f,e);y(f,[{key:"add",value:function(e,h){if("+1"===h||"-1"===h){var r=this.value(e)||0;h="+1"===h?r+1:r-1}return 0===h?(this.remove(e),!0):x(f.prototype.__proto__||
Object.getPrototypeOf(f.prototype),"add",this).call(this,e,h)}},{key:"canAdd",value:function(e,h){return x(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"canAdd",this).call(this,e,h)||x(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"canAdd",this).call(this,e,parseInt(h))}},{key:"value",value:function(e){return parseInt(x(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"value",this).call(this,e))||void 0}}]);return f}(f.default.Attributor.Class))("indent","ql-indent",
{scope:f.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});ba.IndentClass=f},function(f,ba,da){function z(f,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+typeof x);f.prototype=Object.create(x&&x.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(f,x):f.__proto__=x)}Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function x(){if(!(this instanceof
x))throw new TypeError("Cannot call a class as a function");var f=(x.__proto__||Object.getPrototypeOf(x)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(x,f);return x}(function(f){return f&&f.__esModule?f:{default:f}}(da(4)).default);f.blotName="blockquote";f.tagName="blockquote";ba.default=f},function(f,ba,da){function z(f,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+
typeof w);f.prototype=Object.create(w&&w.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(f,w):f.__proto__=w)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function f(f,e){for(var h=0;h<e.length;h++){var r=e[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(f,r.key,r)}}return function(w,e,h){e&&f(w.prototype,e);h&&f(w,h);return w}}();f=function(f){function w(){if(!(this instanceof
w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(w,f);y(w,null,[{key:"formats",value:function(e){return this.tagName.indexOf(e.tagName)+1}}]);return w}(function(f){return f&&f.__esModule?f:{default:f}}(da(4)).default);f.blotName="header";f.tagName="H1 H2 H3 H4 H5 H6".split(" ");
ba.default=f},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&
f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.ListItem=void 0;var e=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),h=function qa(e,f,h){null===
e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,f);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return qa(e,f,h)}else{if("value"in r)return r.value;f=r.get;return void 0===f?void 0:f.call(h)}};f=da(0);var r=z(f);f=da(4);f=z(f);da=da(25);da=z(da);var aa=function(f){function z(){y(this,z);return x(this,(z.__proto__||Object.getPrototypeOf(z)).apply(this,arguments))}w(z,f);e(z,[{key:"format",value:function(e,f){e!==ea.blotName||f?h(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),
"format",this).call(this,e,f):this.replaceWith(r.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():h(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(e,f){this.parent.isolate(this.offset(this.parent),this.length());if(e===this.parent.statics.blotName)return this.parent.replaceWith(e,f),this;this.parent.unwrap();return h(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),
"replaceWith",this).call(this,e,f)}}],[{key:"formats",value:function(e){return e.tagName===this.tagName?void 0:h(z.__proto__||Object.getPrototypeOf(z),"formats",this).call(this,e)}}]);return z}(f.default);aa.blotName="list-item";aa.tagName="LI";var ea=function(f){function z(e){function f(f){if(f.target.parentNode===e){var w=h.statics.formats(e);f=r.default.find(f.target);"checked"===w?f.format("list","unchecked"):"unchecked"===w&&f.format("list","checked")}}y(this,z);var h=x(this,(z.__proto__||Object.getPrototypeOf(z)).call(this,
e));e.addEventListener("touchstart",f);e.addEventListener("mousedown",f);return h}w(z,f);e(z,null,[{key:"create",value:function(e){var f="ordered"===e?"OL":"UL";f=h(z.__proto__||Object.getPrototypeOf(z),"create",this).call(this,f);"checked"!==e&&"unchecked"!==e||f.setAttribute("data-checked","checked"===e);return f}},{key:"formats",value:function(e){if("OL"===e.tagName)return"ordered";if("UL"===e.tagName)return e.hasAttribute("data-checked")?"true"===e.getAttribute("data-checked")?"checked":"unchecked":
"bullet"}}]);e(z,[{key:"format",value:function(e,f){0<this.children.length&&this.children.tail.format(e,f)}},{key:"formats",value:function(){var e={},f=this.statics.blotName,h=this.statics.formats(this.domNode);f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}},{key:"insertBefore",value:function(e,f){e instanceof aa?h(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"insertBefore",this).call(this,e,f):(f=null==f?this.length():f.offset(this),
f=this.split(f),f.parent.insertBefore(e,f))}},{key:"optimize",value:function(e){h(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"optimize",this).call(this,e);e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&e.domNode.tagName===this.domNode.tagName&&e.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(e.moveChildren(this),e.remove())}},{key:"replace",value:function(e){if(e.statics.blotName!==this.statics.blotName){var f=r.default.create(this.statics.defaultChild);
e.moveChildren(f);this.appendChild(f)}h(z.prototype.__proto__||Object.getPrototypeOf(z.prototype),"replace",this).call(this,e)}}]);return z}(da.default);ea.blotName="list";ea.scope=r.default.Scope.BLOCK_BLOT;ea.tagName=["OL","UL"];ea.defaultChild="list-item";ea.allowedChildren=[aa];ba.ListItem=aa;ba.default=ea},function(f,ba,da){function z(f,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+typeof x);f.prototype=Object.create(x&&x.prototype,
{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(f,x):f.__proto__=x)}Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function x(){if(!(this instanceof x))throw new TypeError("Cannot call a class as a function");var f=(x.__proto__||Object.getPrototypeOf(x)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?
this:f}z(x,f);return x}(function(f){return f&&f.__esModule?f:{default:f}}(da(56)).default);f.blotName="italic";f.tagName=["EM","I"];ba.default=f},function(f,ba,da){function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(ba,
"__esModule",{value:!0});var y=function(){function f(e,f){for(var h=0;h<f.length;h++){var w=f[h];w.enumerable=w.enumerable||!1;w.configurable=!0;"value"in w&&(w.writable=!0);Object.defineProperty(e,w.key,w)}}return function(e,h,r){h&&f(e.prototype,h);r&&f(e,r);return e}}(),x=function aa(e,f,r){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return aa(e,f,r)}else{if("value"in h)return h.value;f=h.get;return void 0===
f?void 0:f.call(r)}};f=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(f,e);y(f,null,[{key:"create",value:function(e){return"super"===e?document.createElement("sup"):"sub"===e?document.createElement("sub"):x(f.__proto__||
Object.getPrototypeOf(f),"create",this).call(this,e)}},{key:"formats",value:function(e){if("SUB"===e.tagName)return"sub";if("SUP"===e.tagName)return"super"}}]);return f}(function(e){return e&&e.__esModule?e:{default:e}}(da(6)).default);f.blotName="script";f.tagName=["SUB","SUP"];ba.default=f},function(f,ba,da){function z(f,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+typeof x);f.prototype=Object.create(x&&x.prototype,{constructor:{value:f,
enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(f,x):f.__proto__=x)}Object.defineProperty(ba,"__esModule",{value:!0});f=function(f){function x(){if(!(this instanceof x))throw new TypeError("Cannot call a class as a function");var f=(x.__proto__||Object.getPrototypeOf(x)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(x,f);
return x}(function(f){return f&&f.__esModule?f:{default:f}}(da(6)).default);f.blotName="strike";f.tagName="S";ba.default=f},function(f,ba,da){function z(f,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+typeof x);f.prototype=Object.create(x&&x.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(f,x):f.__proto__=x)}Object.defineProperty(ba,"__esModule",{value:!0});
f=function(f){function x(){if(!(this instanceof x))throw new TypeError("Cannot call a class as a function");var f=(x.__proto__||Object.getPrototypeOf(x)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}z(x,f);return x}(function(f){return f&&f.__esModule?f:{default:f}}(da(6)).default);f.blotName="underline";f.tagName="U";ba.default=f},function(f,ba,da){function z(e,f){if("function"!==
typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,
n.key,n)}}return function(f,h,w){h&&e(f.prototype,h);w&&e(f,w);return f}}(),x=function n(e,f,w){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,f);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return n(e,f,w)}else{if("value"in r)return r.value;f=r.get;return void 0===f?void 0:f.call(w)}};f=function(e){return e&&e.__esModule?e:{default:e}}(da(0));var w=da(27),e=["alt","height","width"];da=function(f){function r(){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function");
var e=(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(r,f);y(r,[{key:"format",value:function(f,n){-1<e.indexOf(f)?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):x(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(e){var f=x(r.__proto__||Object.getPrototypeOf(r),
"create",this).call(this,e);"string"===typeof e&&f.setAttribute("src",this.sanitize(e));return f}},{key:"formats",value:function(f){return e.reduce(function(e,r){f.hasAttribute(r)&&(e[r]=f.getAttribute(r));return e},{})}},{key:"match",value:function(e){return/\.(jpe?g|gif|png)$/.test(e)||/^data:image\/.+;base64/.test(e)}},{key:"sanitize",value:function(e){return(0,w.sanitize)(e,["http","https","data"])?e:"//:0"}},{key:"value",value:function(e){return e.getAttribute("src")}}]);return r}(f.default.Embed);
da.blotName="image";da.tagName="IMG";ba.default=da},function(f,ba,da){function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});var y=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=
f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,w){h&&e(f.prototype,h);w&&e(f,w);return f}}(),x=function n(e,f,w){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,f);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return n(e,f,w)}else{if("value"in r)return r.value;f=r.get;return void 0===f?void 0:f.call(w)}};f=da(4);var w=function(e){return e&&e.__esModule?e:{default:e}}(da(27)),e=
["height","width"];da=function(f){function r(){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function");var e=(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}z(r,f);y(r,[{key:"format",value:function(f,n){-1<e.indexOf(f)?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):x(r.prototype.__proto__||
Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(e){var f=x(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,e);f.setAttribute("frameborder","0");f.setAttribute("allowfullscreen",!0);f.setAttribute("src",this.sanitize(e));return f}},{key:"formats",value:function(f){return e.reduce(function(e,r){f.hasAttribute(r)&&(e[r]=f.getAttribute(r));return e},{})}},{key:"sanitize",value:function(e){return w.default.sanitize(e)}},{key:"value",value:function(e){return e.getAttribute("src")}}]);
return r}(f.BlockEmbed);da.blotName="video";da.className="ql-video";da.tagName="IFRAME";ba.default=da},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+
typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.FormulaBlot=void 0;var e=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,
n);return f}}(),h=function qa(e,f,h){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,f);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return qa(e,f,h)}else{if("value"in r)return r.value;f=r.get;return void 0===f?void 0:f.call(h)}};f=da(35);f=z(f);var r=da(5),aa=z(r);da=da(9);da=z(da);var ea=function(f){function r(){y(this,r);return x(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}w(r,f);e(r,null,[{key:"create",value:function(e){var f=h(r.__proto__||
Object.getPrototypeOf(r),"create",this).call(this,e);"string"===typeof e&&(window.katex.render(e,f,{throwOnError:!1,errorColor:"#f00"}),f.setAttribute("data-value",e));return f}},{key:"value",value:function(e){return e.getAttribute("data-value")}}]);return r}(f.default);ea.blotName="formula";ea.className="ql-formula";ea.tagName="SPAN";da=function(f){function h(){y(this,h);var e=x(this,(h.__proto__||Object.getPrototypeOf(h)).call(this));if(null==window.katex)throw Error("Formula module requires KaTeX.");
return e}w(h,f);e(h,null,[{key:"register",value:function(){aa.default.register(ea,!0)}}]);return h}(da.default);ba.FormulaBlot=ea;ba.default=da},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function w(e,f){if("function"!==
typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.CodeToken=ba.CodeBlock=void 0;var e=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in
n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),h=function ka(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ka(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};f=da(0);f=z(f);var r=da(5),aa=z(r);r=da(9);r=z(r);da=da(13);var ea=function(f){function n(){y(this,n);return x(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,
arguments))}w(n,f);e(n,[{key:"replaceWith",value:function(e){this.domNode.textContent=this.domNode.textContent;this.attach();h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"replaceWith",this).call(this,e)}},{key:"highlight",value:function(e){var f=this.domNode.textContent;if(this.cachedText!==f){if(0<f.trim().length||null==this.cachedText)this.domNode.innerHTML=e(f),this.domNode.normalize(),this.attach();this.cachedText=f}}}]);return n}(z(da).default);ea.className="ql-syntax";var n=new f.default.Attributor.Class("token",
"hljs",{scope:f.default.Scope.INLINE});da=function(f){function h(e,f){y(this,h);var n=x(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,e,f));if("function"!==typeof n.options.highlight)throw Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var r=null;n.quill.on(aa.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(r);r=setTimeout(function(){n.highlight();r=null},n.options.interval)});n.highlight();return n}w(h,f);e(h,null,[{key:"register",
value:function(){aa.default.register(n,!0);aa.default.register(ea,!0)}}]);e(h,[{key:"highlight",value:function(){var e=this;if(!this.quill.selection.composing){this.quill.update(aa.default.sources.USER);var f=this.quill.getSelection();this.quill.scroll.descendants(ea).forEach(function(f){f.highlight(e.options.highlight)});this.quill.update(aa.default.sources.SILENT);null!=f&&this.quill.setSelection(f,aa.default.sources.SILENT)}}}]);return h}(r.default);da.DEFAULTS={highlight:function(){return null==
window.hljs?null:function(e){return window.hljs.highlightAuto(e).value}}(),interval:1E3};ba.CodeBlock=ea;ba.CodeToken=n;ba.default=da},function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},
function(f){f.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(f){f.exports=
'<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},
function(f){f.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},
function(f){f.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(f){f.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(f){f.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},
function(f){f.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(f,ba,da){function z(e){return e&&e.__esModule?e:{default:e}}function y(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function x(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==
typeof f?e:f}function w(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(ba,"__esModule",{value:!0});ba.default=ba.BubbleTooltip=void 0;var e=function ra(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,
f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ra(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}},h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();f=da(3);f=z(f);var r=da(8),aa=z(r);r=da(43);var ea=z(r),n=da(15);da=da(41);var fa=z(da),ha=[["bold","italic",
"link"],[{header:1},{header:2},"blockquote"]];da=function(e){function f(e,h){y(this,f);null!=h.modules.toolbar&&null==h.modules.toolbar.container&&(h.modules.toolbar.container=ha);e=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));e.quill.container.classList.add("ql-bubble");return e}w(f,e);h(f,[{key:"extendToolbar",value:function(e){this.tooltip=new oa(this.quill,this.options.bounds);this.tooltip.root.appendChild(e.container);this.buildButtons([].slice.call(e.container.querySelectorAll("button")),
fa.default);this.buildPickers([].slice.call(e.container.querySelectorAll("select")),fa.default)}}]);return f}(ea.default);da.DEFAULTS=(0,f.default)(!0,{},ea.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(e){e?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var oa=function(f){function r(e,f){y(this,r);var h=x(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,f));h.quill.on(aa.default.events.EDITOR_CHANGE,function(e,f,r,w){e===aa.default.events.SELECTION_CHANGE&&
(null!=f&&0<f.length&&w===aa.default.sources.USER?(h.show(),h.root.style.left="0px",h.root.style.width="",h.root.style.width=h.root.offsetWidth+"px",e=h.quill.getLines(f.index,f.length),1===e.length?h.position(h.quill.getBounds(f)):(r=e[e.length-1],e=h.quill.getIndex(r),f=Math.min(r.length()-1,f.index+f.length-e),f=h.quill.getBounds(new n.Range(e,f)),h.position(f))):document.activeElement!==h.textbox&&h.quill.hasFocus()&&h.hide())});return h}w(r,f);h(r,[{key:"listen",value:function(){var f=this;e(r.prototype.__proto__||
Object.getPrototypeOf(r.prototype),"listen",this).call(this);this.root.querySelector(".ql-close").addEventListener("click",function(){f.root.classList.remove("ql-editing")});this.quill.on(aa.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!f.root.classList.contains("ql-hidden")){var e=f.quill.getSelection();null!=e&&f.position(f.quill.getBounds(e))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(f){f=e(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),
"position",this).call(this,f);var h=this.root.querySelector(".ql-tooltip-arrow");h.style.marginLeft="";if(0===f)return f;h.style.marginLeft=-1*f-h.offsetWidth/2+"px"}}]);return r}(r.BaseTooltip);oa.TEMPLATE='<span class="ql-tooltip-arrow"></span><div class="ql-tooltip-editor"><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-close"></a></div>';ba.BubbleTooltip=oa;ba.default=da},function(f,ba,da){f.exports=da(63)}])["default"]})}).call(this,
f(383).Buffer)}}]);}).call(this || window)
