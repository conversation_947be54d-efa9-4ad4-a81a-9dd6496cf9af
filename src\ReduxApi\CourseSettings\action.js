import { createAction } from '../util';
import axios from '../../axios';
import { fromJS } from 'immutable';
export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const RESET_BREADCRUMB_SUCCESS = 'RESET_BREADCRUMB_SUCCESS';
const resetBreadCrumbSuccess = createAction(
  RESET_BREADCRUMB_SUCCESS,
  'breadcrumbs'
);
export function resetBreadCrumb(name) {
  return function (dispatch) {
    dispatch(resetBreadCrumbSuccess(setBreadCrumbName(name)));
  };
}

export const SET_BREADCRUMB_SUCCESS = 'SET_BREADCRUMB_SUCCESS';

const setBreadCrumbSuccess = createAction(
  SET_BREADCRUMB_SUCCESS,
  'breadcrumbs'
);
export const setBreadCrumbName = (name) => {
  return (dispatch) => {
    dispatch(setBreadCrumbSuccess(name));
  };
};

export const UPDATE_COURSE_BASED_FACIAL_REQUEST =
  'UPDATE_COURSE_BASED_FACIAL_REQUEST';
export const UPDATE_COURSE_BASED_FACIAL_SUCCESS =
  'UPDATE_COURSE_BASED_FACIAL_SUCCESS';
export const UPDATE_COURSE_BASED_FACIAL_FAILURE =
  'UPDATE_COURSE_BASED_FACIAL_FAILURE';

const updateStaffApprovalRequest = createAction(
  UPDATE_COURSE_BASED_FACIAL_REQUEST,
  'isLoading'
);
const updateStaffApprovalSuccess = createAction(
  UPDATE_COURSE_BASED_FACIAL_SUCCESS,
  'data'
);
const updateStaffApprovalFailure = createAction(
  UPDATE_COURSE_BASED_FACIAL_FAILURE,
  'error'
);

export function updateCourseBasedFacial(query, callBack) {
  return function (dispatch) {
    dispatch(updateStaffApprovalRequest());
    axios
      .put(`/digiclass/courseSettings/courseWise-setting`, query)
      .then((res) => {
        dispatch(updateStaffApprovalSuccess(res.data.data));
        callBack && callBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(updateStaffApprovalFailure(error));
      });
  };
}

export const GET_COURSE_BASED_FACIAL_REQUEST =
  'GET_COURSE_BASED_FACIAL_REQUEST';
export const GET_COURSE_BASED_FACIAL_SUCCESS =
  'GET_COURSE_BASED_FACIAL_SUCCESS';
export const GET_COURSE_BASED_FACIAL_FAILURE =
  'GET_COURSE_BASED_FACIAL_FAILURE';

const getStaffApprovalRequest = createAction(
  GET_COURSE_BASED_FACIAL_REQUEST,
  'isLoading'
);
const getStaffApprovalSuccess = createAction(
  GET_COURSE_BASED_FACIAL_SUCCESS,
  'data'
);
const getStaffApprovalFailure = createAction(
  GET_COURSE_BASED_FACIAL_FAILURE,
  'error'
);

export function getCourseBasedFacial(params, callBack) {
  return function (dispatch) {
    dispatch(getStaffApprovalRequest());
    axios
      .get(`/digiclass/courseSettings/getCourseWise-settings`, { params })
      .then((res) => {
        dispatch(getStaffApprovalSuccess(res.data.data));
        callBack && callBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(getStaffApprovalFailure(error));
      });
  };
}

export const GET_COURSE_BASED_STAFF_DETAILS_REQUEST =
  'GET_COURSE_BASED_STAFF_DETAILS_REQUEST';
export const GET_COURSE_BASED_STAFF_DETAILS_SUCCESS =
  'GET_COURSE_BASED_STAFF_DETAILS_SUCCESS';
export const GET_COURSE_BASED_STAFF_DETAILS_FAILURE =
  'GET_COURSE_BASED_STAFF_DETAILS_FAILURE';

const getCourseBasedStaffDetailsRequest = createAction(
  GET_COURSE_BASED_STAFF_DETAILS_REQUEST,
  'isLoading'
);
const getCourseBasedStaffDetailsSuccess = createAction(
  GET_COURSE_BASED_STAFF_DETAILS_SUCCESS,
  'data'
);
const getCourseBasedStaffDetailsFailure = createAction(
  GET_COURSE_BASED_STAFF_DETAILS_FAILURE,
  'error'
);

export function getCourseBasedStaffDetails(params, callBack) {
  return function (dispatch) {
    dispatch(getCourseBasedStaffDetailsRequest());
    axios
      .get(`/digiclass/courseSettings/getCourseWise-userDetails`, { params })
      .then((res) => {
        dispatch(getCourseBasedStaffDetailsSuccess(res.data.data));
        callBack && callBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(getCourseBasedStaffDetailsFailure(error));
      });
  };
}

export const UPDATE_COURSE_WISE_DELIVERY_TYPE_REQUEST =
  'UPDATE_COURSE_WISE_DELIVERY_TYPE_REQUEST';
export const UPDATE_COURSE_WISE_DELIVERY_TYPE_SUCCESS =
  'UPDATE_COURSE_WISE_DELIVERY_TYPE_SUCCESS';
export const UPDATE_COURSE_WISE_DELIVERY_TYPE_FAILURE =
  'UPDATE_COURSE_WISE_DELIVERY_TYPE_FAILURE';

const updateCourseWiseDeliveryTypeRequest = createAction(
  UPDATE_COURSE_WISE_DELIVERY_TYPE_REQUEST,
  'isLoading'
);
const updateCourseWiseDeliveryTypeSuccess = createAction(
  UPDATE_COURSE_WISE_DELIVERY_TYPE_SUCCESS,
  'data'
);
const updateCourseWiseDeliveryTypeFailure = createAction(
  UPDATE_COURSE_WISE_DELIVERY_TYPE_FAILURE,
  'error'
);

export function updateCourseWiseDeliveryType(body, callBack) {
  return function (dispatch) {
    dispatch(updateCourseWiseDeliveryTypeRequest());
    axios
      .put(
        `/digiclass/courseSettings/getCourseWise-updateCourseWiseDeliveryType`,
        body
      )
      .then((res) => {
        dispatch(updateCourseWiseDeliveryTypeSuccess(res.data.data));
        callBack && callBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(updateCourseWiseDeliveryTypeFailure(error));
      });
  };
}

export const GET_DELIVERY_BASED_STAFF_REQUEST =
  'GET_DELIVERY_BASED_STAFF_REQUEST';
export const GET_DELIVERY_BASED_STAFF_SUCCESS =
  'GET_DELIVERY_BASED_STAFF_SUCCESS';
export const GET_DELIVERY_BASED_STAFF_FAILURE =
  'GET_DELIVERY_BASED_STAFF_FAILURE';

const getDeliveryBasedStaffRequest = createAction(
  GET_DELIVERY_BASED_STAFF_REQUEST,
  'isLoading'
);
const getDeliveryBasedStaffSuccess = createAction(
  GET_DELIVERY_BASED_STAFF_SUCCESS,
  'data'
);
const getDeliveryBasedStaffFailure = createAction(
  GET_DELIVERY_BASED_STAFF_FAILURE,
  'error'
);

export function getDeliveryBasedStaff(params, callBack) {
  return function (dispatch) {
    dispatch(getDeliveryBasedStaffRequest());
    axios
      .get(`/digiclass/courseSettings/getCourseWise-staffDetails`, { params })
      .then((res) => {
        dispatch(getDeliveryBasedStaffSuccess(res.data.data));
        callBack && callBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(getDeliveryBasedStaffFailure(error));
      });
  };
}
