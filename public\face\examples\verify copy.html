<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Face Photo Capture with MediaPipe</title>
    <link
      rel="stylesheet"
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
    />

    <style>
      #camera {
        width: 100%;
        max-width: 500px;
        margin: auto;
      }
      .preview-box {
        width: 100px;
        height: 100px;
        margin: 5px;
        overflow: hidden;
        border: 2px solid #ddd;
      }
      .preview-box img {
        width: 100%;
        /* height: auto; */
        height: 100px;
      }
      .oval-image {
        position: absolute;
      }
      #img img {
        width: 40px;
      }
    </style>
    <script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="./js/face-api.js"></script>
  </head>
  <body>
    <div class="container text-center mt-1">
      <h2>Face Verification Tool <a href="index1.html"> &lt;- Go Back</a></h2>

      <div id="camera-container" class="mb-3">
        <img src="./images/oval.png" alt="Oval shape" class="oval-image" />
        <video id="camera" autoplay></video>
      </div>

      <button
        class="btn btn-primary"
        id="startCaptureBtn"
        onclick="captureImage()"
      >
        Capture
      </button>
      <p id="resultMessage" class="mt-1"></p>
      <div id="img"></div>
    </div>

    <script type="module">
      let uploadedDescriptors = [];
      let video = document.getElementById('camera');
      const minConfidence = 0.7;
      // Start video stream
      async function startVideo() {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        video.srcObject = stream;
      }
      // Capture and process the image

      async function capturePosition() {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const context = canvas.getContext('2d');
        context.filter = 'brightness(1.2) contrast(1.2)';
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        document.getElementById(`img`).innerHTML = ``;
        const detections = await faceapi
          .detectSingleFace(
            video,
            new faceapi.SsdMobilenetv1Options({ minConfidence })
          )
          .withFaceLandmarks()
          .withFaceDescriptor();

        if (
          detections &&
          detections.landmarks &&
          detections.landmarks._positions.length > 0
        ) {
          const box = detections.detection.box; // Bounding box of the face
          const landmarks = detections.landmarks; // 68-point landmarks

          let { x: minX, y: minY, width: boxWidth, height: boxHeight } = box;

          // Add padding to the bounding box
          const padding = 0.4; // Adjust padding as needed (e.g., 40%)
          const paddedWidth = boxWidth * (1 + padding);
          const paddedHeight = boxHeight * (1 + padding);

          // Adjust `minX` and `minY` to apply padding
          minX -= (paddedWidth - boxWidth) / 2;
          minY -= (paddedHeight - boxHeight) / 2;

          // Ensure the boundaries stay within the canvas dimensions
          minX = Math.max(0, minX);
          minY = Math.max(0, minY);
          const maxWidth = Math.min(canvas.width - minX, paddedWidth);
          const maxHeight = Math.min(canvas.height - minY, paddedHeight);

          // Create a new canvas for the cropped face
          const croppedCanvas = document.createElement('canvas');
          croppedCanvas.width = maxWidth;
          croppedCanvas.height = maxHeight;

          // Draw the cropped face onto the new canvas
          const croppedCtx = croppedCanvas.getContext('2d');
          croppedCtx.drawImage(
            canvas, // Source canvas
            minX, // Source x
            minY, // Source y
            maxWidth, // Source width
            maxHeight, // Source height
            0, // Destination x
            0, // Destination y
            maxWidth, // Destination width
            maxHeight // Destination height
          );

          const croppedImage = new Image();
          croppedImage.src = croppedCanvas.toDataURL();
          await croppedImage.decode();

          // console.log(croppedImage);

          // Detect face descriptor from the cropped image
          const detectionsFace = await faceapi
            .detectSingleFace(
              video,
              new faceapi.SsdMobilenetv1Options({ minConfidence })
              // new faceapi.TinyFaceDetectorOptions()
            )
            .withFaceLandmarks()
            .withFaceDescriptor();
          // console.log(detectionsFace);
          if (detectionsFace) {
            // console.log('Face descriptor:', detectionsFace.descriptor);
            const capturedDescriptor = detectionsFace.descriptor;
            const matchFound = compareDescriptors(capturedDescriptor);
            // console.log(matchFound);
            if (matchFound) {
              document.getElementById('resultMessage').textContent =
                'Success! Match found.';
              document.getElementById('startCaptureBtn').style.display =
                'inline-block';
              document.getElementById(
                `img`
              ).innerHTML = `<img src="${croppedImage.src}" alt="Captured Image">`;
            } else {
              // document.getElementById('resultMessage').textContent =
              // 	'No match found. Try again.';
              await countdownAndCapture();
            }
          } else {
            // document.getElementById('resultMessage').textContent =
            // 	'No face detected in cropped image.';
            await countdownAndCapture();
          }
        } else {
          // document.getElementById('resultMessage').textContent =
          // 	'Face not detected, please adjust your position!';
          await countdownAndCapture();
        }
      }
      let secondsPassed = 0;
      async function countdownAndCapture() {
        let countdown = 1;

        const countdownInterval = setInterval(() => {
          secondsPassed++;
          if (secondsPassed == 60) {
            clearInterval(countdownInterval);
            document.getElementById('startCaptureBtn').style.display =
              'inline-block';
            document.getElementById('startCaptureBtn').innerHTML = 'Re Verify';
            secondsPassed = 0;
            document.getElementById(
              'resultMessage'
            ).innerText = `Face not detected, please adjust your position and Re verify`;
            return;
          }
          if (countdown > 0) {
            document.getElementById(
              'resultMessage'
            ).innerText = `Verifying in progress. Please wait...`;
            countdown--;
          } else {
            clearInterval(countdownInterval);
            capturePosition();
          }
        }, 1000);
      }

      async function captureImage() {
        document.getElementById('startCaptureBtn').style.display = 'none';
        await countdownAndCapture();
      }

      function compareDescriptors(capturedDescriptor) {
        let threshold = 0.47; // Define a threshold for matching
        let matchCount = 0;
        // console.log({ uploadedDescriptors, capturedDescriptor });
        uploadedDescriptors.forEach((descriptor) => {
          const distance = faceapi.euclideanDistance(
            capturedDescriptor,
            descriptor
          );
          if (distance < threshold) {
            matchCount++;
          }
        });

        const averageDistance = matchCount / uploadedDescriptors.length;
        console.log({ averageDistance, matchCount });
        // return averageDistance >= 0.5 || matchCount > 0;
        return averageDistance >= 0.75 && matchCount >= 2;
      }

      // Initialize everything when the page loads
      window.onload = async () => {
        await startVideo();
      };
      window.captureImage = captureImage;

      const daAuthAppUrl =
        'https://auth-staging.gcp.digivalitsolutions.com/api/v0/auth/facial-labeled-descriptors-v2?employeeOrAcademicId=';

      function loadSingleDescfromDb(acadamicId) {
        console.log('load from db called');
        // const descriptors = localStorage.getItem('descriptors');
        // if (descriptors !== null) {
        // 	const decodeDescriptors = JSON.parse(descriptors);
        // 	console.log({ decodeDescriptors });
        // 	uploadedDescriptors = decodeDescriptors;
        // }
        // return;
        $.ajax({
          url: daAuthAppUrl + acadamicId,
          type: 'GET',
          beforeSend: function (request) {
            request.setRequestHeader('api_key', 'apikey123');
          },
          dataType: 'json', // added data type
          success: function (res) {
            uploadedDescriptors = res.data.flatMap((item) => item.descriptors);
            console.log({ uploadedDescriptors });
          },
          error: function (err) {
            console.log('error occured load from db', err);
          },
        });
      }

      async function loadLibraries() {
        const MODEL_URL = 'models';
        await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
        await faceapi.loadFaceLandmarkModel(MODEL_URL);
        await faceapi.loadFaceRecognitionModel(MODEL_URL);
        // await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
        console.log('load facial Libraries done');
      }

      $(document).ready(async function () {
        console.log('webcam is ready');
        await loadLibraries();
        loadSingleDescfromDb(localStorage.getItem('biometricUserName'));
      });
    </script>
  </body>
</html>
