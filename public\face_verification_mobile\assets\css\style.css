html,
body {
  margin: 0;
  padding: 0;
  background-color: white;
  font-size: 16px;
  height: 100%;
  width: 100%;
}

body.digi-ios-font {
  font-family: 'Inter', sans-serif;
}

body.digi-android-font {
  font-family: 'Roboto', sans-serif;
}

.digi-header-container {
  background: linear-gradient(84.06deg, #147afc 13%, #56b8ff 107.28%);
  padding: 15px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 90px;
}

.digi-header-center-part {
  max-width: 250px;
  flex: 1;
  display: flex;
  justify-content: center;
  overflow: hidden;
}

.digi-header-left-part {
  flex: 1;
  max-width: calc(50% - (250px / 2));
  overflow: hidden;
}

.digi-header-right-part {
  flex: 1;
  max-width: calc(50% - (250px / 2));
  display: flex;
  overflow: hidden;
}

.digi-college-detail-container {
  display: flex;
}

.digi-college-detail-container .digi-logo {
  width: 100%;
  height: auto;
  max-width: 50px;
}

.digi-college-detail {
  max-width: 105px;
  overflow: hidden;
}

.digi-college-title {
  color: white;
  font-weight: 500;
  font-family: inherit;
  font-size: 1.2rem;
  margin: 0;
}

.digi-college-subtitle {
  color: white;
  font-family: inherit;
  font-size: 0.8rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.digi-page-title {
  color: white;
  font-family: inherit;
  font-size: 2rem;
  text-align: center;
}

.digi-cancel-button {
  background: transparent;
  color: white;
  border: 1px solid white;
  padding: 8px 15px;
  box-sizing: border-box;
  border-radius: 5px;
  overflow: hidden;
  flex: 1;
  font-size: 0.8rem;
  font-family: inherit;
  cursor: pointer;
}

.digi-body-content {
  overflow: auto;
  max-height: calc(100vh - (90px + 60px));
  height: 100vh;
  background: #5a5a5a;
}

.digi-camera-container {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.digi-camera-center-container {
  width: 100%;
  max-width: 250px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.digi-frame-container {
  display: flex;
  justify-content: center;
  background: black;
}

.digi-video {
  /* max-width: 100%; */
}

.digi-bottom-pannel-container {
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  z-index: 2;
}

.digi-bg-black {
  background: #333333;
}

.digi-bg-white {
  background-color: white;
}

.digi-control-panel {
  display: none;
  align-items: center;
}

.digi-control-panel.digi-active {
  display: flex;
  height: 90px;
}

.digi-control-panel .digi-icon-container {
  min-width: 70px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.digi-control-panel .digi-icon-container img {
  max-width: 100%;
  height: auto;
}

.digi-control-panel .digi-info-icon {
  width: 18px;
}

.digi-control-panel .digi-close-icon {
  width: 22px;
}

.digi-control-panel .digi-home-icon {
  color: #fff;
  transform: scale(1.5);
}

.digi-loader-panel {
  display: none;
  height: 90px;
  align-items: center;
  justify-content: center;
}

.digi-loader-panel.digi-active {
  display: flex;
}

.digi-loader-panel-center {
  display: flex;
  align-items: center;
}

.digi-loader-panel .digi-loading-text {
  font-family: inherit;
  font-size: 1rem;
  color: white;
}

.digi-loader-panel .digi-loading-icon-container {
  width: 25px;
}

.digi-loader-panel .digi-loading-icon {
  width: 100%;
}

.digi-blink-panel {
  display: none;
  height: 90px;
  align-items: center;
  justify-content: center;
}

.digi-blink-panel.digi-active {
  display: flex;
}

.digi-blink-panel-center {
  display: flex;
  align-items: center;
}

.digi-blink-panel .digi-blink-icon-container {
  width: 20px;
  height: auto;
}

.digi-blink-icon-container img {
  width: 100%;
}

.digi-blink-panel .digi-blink-text {
  color: white;
  font-size: 1.5rem;
}

.digi-blink-fail-panel {
  background-color: white;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}

.digi-blink-fail-status-container {
  display: flex;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
  border-bottom: 1px solid #dddddd;
}

.digi-blink-fail-text-container {
  flex: 1;
}

.digi-warning-icon-container {
  width: 36px;
  height: 36px;
  background: #fecaca;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.digi-warning-icon-container .digi-warning-icon {
  color: #f44336;
}

.digi-blink-fail-text-container .digi-blink-fail-title {
  color: black;
  margin: 0;
  font-size: 0.8rem;
  font-family: inherit;
}

.digi-blink-fail-text-container .digi-blink-fail-subtitle {
  color: #9e9e9e;
  font-size: 0.7rem;
  font-family: inherit;
}

.digi-blink-fail-action-container {
  padding: 10px;
  box-sizing: border-box;
  display: flex;
}

.digi-fail-button {
  background: white;
  color: #147afc;
  border: 1px solid #147afc;
  padding: 8px 15px;
  box-sizing: border-box;
  border-radius: 20px;
  overflow: hidden;
  flex: 1;
  font-size: 0.9rem;
  font-family: inherit;
  cursor: pointer;
}

.digi-fail-button.digi-active {
  background: #147afc;
  color: white;
}

.digi-footer-container {
  background-color: white;
  padding: 20px 15px;
  box-sizing: border-box;
  font-family: inherit;
  font-size: 1rem;
  height: 60px;
}

.digi-info-icon-container {
  position: relative;
}

.digi-info-icon-container .digi-tooltip-container {
  background-color: white;
  padding: 15px;
  box-sizing: border-box;
  position: absolute;
  bottom: 40px;
  left: -15px;
  z-index: 3;
  box-shadow: 0px 0px 2px;
  width: 285px;
  border-radius: 10px;
}

.digi-down-arrow {
  position: absolute;
  width: 20px;
  height: 20px;
  left: 17px;
  bottom: -5px;
  background: #ffffff;
  transform: rotate(226deg);
}

.digi-tooltip-header {
  display: flex;
  align-items: center;
  margin: 0 0 10px;
}

.digi-tooltip-title {
  font-size: 1.2rem;
  font-family: inherit;
  font-weight: 500;
  color: black;
}

.digi-tooltip-content {
  font-size: 0.8rem;
  font-family: inherit;
  color: #9ca3af;
}

.digi-control-panel .digi-eye-icon {
  width: 100%;
  max-width: 50px;
}

.digi-control-panel .digi-close-icon {
  width: 100%;
  max-width: 50px;
}

.digi-curve-cut-frame-container {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  z-index: 1;
  overflow: hidden;
}

.digi-curve-cut-frame-container .digi-curve-cut-frame {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.digi-authentication-container {
  background-color: white;
  height: 100%;
  width: 100%;
  overflow: auto;
  position: relative;
  z-index: 0;
}

.digi-authentication-title {
  text-align: center;
  font-size: 1.2rem;
  font-family: inherit;
  margin: 15px 0 0 0;
}

.digi-profile-picture-container {
  padding: 0 15px;
  box-sizing: border-box;
  margin: 15px 0 0 0;
  width: 100%;
}

.digi-profile-picture-container img {
  max-width: 100%;
  width: 100%;
  height: auto;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #2ac00f;
}

.digi-success-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: 25px 0 0 0;
}

.digi-success-content {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.digi-success-image-container .digi-success-image {
  width: 25px;
  height: auto;
}

.digi-success-text {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-family: inherit;
}

.digi-processing-bottom-panel {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1;
}

.digi-processing-button-container {
  background-color: white;
  box-shadow: 0px -1px 10px #aaaaaaa3;
  padding: 5px 15px;
}

.digi-processing-button {
  padding: 7px 15px;
  box-sizing: border-box;
  border: 1px solid #c2ebfa;
  background: white;
  color: #6b7280;
  border-radius: 15px;
  width: 100%;
  font-size: 1rem;
  font-family: inherit;
}

.digi-camera-page,
.digi-verification-success-page,
.digi-info-icon-container .digi-tooltip-container,
.digi-blink-fail-panel {
  display: none;
}

.digi-camera-page.digi-active,
.digi-verification-success-page.digi-active,
.digi-info-icon-container .digi-tooltip-container.digi-active,
.digi-blink-fail-panel.digi-active {
  display: block;
}

.digi-white-color {
  color: #fff;
}

.digi-eyes-icon {
  position: relative;
  top: 2px;
}

.digi-success-icon {
  color: #2ac00f;
}

.digi-eyes {
  display: flex;
  align-items: center;
  position: relative;
  bottom: 60px;
}

.digi-blinker {
  display: none;
  height: 90px;
  align-items: flex-start;
  justify-content: center;
  z-index: 3;
  position: relative;
  transform: scale(2.5);
}

.digi-blinker.digi-active {
  display: flex;
}

.digi-eyes-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.digi-blinker .digi-eyes-gif {
  width: 20px;
  height: auto;
  transform: scale(1.5);
}

.digi-eyes-gif img {
  width: 100%;
}

.digi-blinker .digi-blink-now-text {
  color: white;
  font-size: 1.5rem;
  position: absolute;
  bottom: -15px;
}

.loading__dot {
  animation: dot ease-in-out 1s infinite;
  background-color: grey;
  display: inline-block;
  height: 5px;
  margin: 3px;
  width: 5px;
}

.loading__dot:nth-of-type(2) {
  animation-delay: 0.2s;
}

.loading__dot:nth-of-type(3) {
  animation-delay: 0.3s;
}

@keyframes dot {
  0% {
    background-color: grey;
    transform: scale(1);
  }

  50% {
    background-color: #ffffff;
    transform: scale(1.3);
  }

  100% {
    background-color: grey;
    transform: scale(1);
  }
}

.digi-blink-now-text {
  display: none;
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translate(-50%, -50%) scale(2.5);
  color: white;
  z-index: 3;
}

.digi-blink-now-text.digi-active {
  display: block;
}

.blink-soft {
  animation: blinker 1.5s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

@media screen and (max-width: 1200px) {
  .digi-camera-center-container {
    max-width: 400px;
  }
}

@media screen and (max-width: 750px) {
  header,
  footer {
    display: none !important;
  }

  .digi-body-content {
    max-height: 100%;
  }

  .digi-camera-center-container {
    max-width: none;
    height: 100vh;
    background-color: black;
  }

  .digi-camera-container {
    padding: 0px;
    max-height: none;
    min-width: 300px;
  }

  .digi-video {
    transform: scale(-1, 1.5) !important;
  }
}

/*loader css*/

.loader {
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  width: 120px;
  height: 120px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: fixed;
  z-index: 1;
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.dummyfacialimage {
  display: none;
  width: 640px !important;
  height: 480px !important;
}

#taptocapture {
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  text-align: center;
  margin-top: -25px;
  color: #ffffff;
}
