/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[19],{375:function(ha,ea,f){(function(f){function z(e){this.Ff=e=e||{};if(Array.isArray(e.table)){var f=[];e.table.forEach(function(e,h){f[e.charCodeAt(0)]=h});e.W3=e.table;e.I1=f}}var ba=f.from||function(){switch(arguments.length){case 1:return new f(arguments[0]);case 2:return new f(arguments[0],arguments[1]);case 3:return new f(arguments[0],arguments[1],arguments[2]);default:throw new Exception("unexpected call.");}},da=f.allocUnsafe||
function(e){return new f(e)},ca=function(){return"undefined"===typeof Uint8Array?function(e){return Array(e)}:function(e){return new Uint8Array(e)}}(),y=String.fromCharCode(0),x=y+y+y+y,w=ba("<~").Pv(0),e=ba("~>").Pv(0),h=function(){var e=Array(85),f;for(f=0;85>f;f++)e[f]=String.fromCharCode(33+f);return e}(),r=function(){var e=Array(256),f;for(f=0;85>f;f++)e[33+f]=f;return e}();y=ha.exports=new z;z.prototype.encode=function(e,r){var n=ca(5),w=e,x=this.Ff,aa,y;"string"===typeof w?w=ba(w,"binary"):
w instanceof f||(w=ba(w));r=r||{};if(Array.isArray(r)){e=r;var z=x.Uy||!1;var fa=x.tG||!1}else e=r.table||x.W3||h,z=void 0===r.Uy?x.Uy||!1:!!r.Uy,fa=void 0===r.tG?x.tG||!1:!!r.tG;x=0;var ea=Math.ceil(5*w.length/4)+4+(z?4:0);r=da(ea);z&&(x+=r.write("<~",x));var ja=aa=y=0;for(ea=w.length;ja<ea;ja++){var ha=w.oI(ja);y*=256;y+=ha;aa++;if(!(aa%4)){if(fa&&538976288===y)x+=r.write("y",x);else if(y){for(aa=4;0<=aa;aa--)ha=y%85,n[aa]=ha,y=(y-ha)/85;for(aa=0;5>aa;aa++)x+=r.write(e[n[aa]],x)}else x+=r.write("z",
x);aa=y=0}}if(aa)if(y){w=4-aa;for(ja=4-aa;0<ja;ja--)y*=256;for(aa=4;0<=aa;aa--)ha=y%85,n[aa]=ha,y=(y-ha)/85;for(aa=0;5>aa;aa++)x+=r.write(e[n[aa]],x);x-=w}else for(ja=0;ja<aa+1;ja++)x+=r.write(e[0],x);z&&(x+=r.write("~>",x));return r.slice(0,x)};z.prototype.decode=function(h,y){var n=this.Ff,aa=!0,z=!0,ca,fa,ea;y=y||n.I1||r;if(!Array.isArray(y)&&(y=y.table||y,!Array.isArray(y))){var ja=[];Object.keys(y).forEach(function(e){ja[e.charCodeAt(0)]=y[e]});y=ja}aa=!y[122];z=!y[121];h instanceof f||(h=ba(h));
ja=0;if(aa||z){var ha=0;for(ea=h.length;ha<ea;ha++){var ra=h.oI(ha);aa&&122===ra&&ja++;z&&121===ra&&ja++}}var Ba=0;ea=Math.ceil(4*h.length/5)+4*ja+5;n=da(ea);if(4<=h.length&&h.Pv(0)===w){for(ha=h.length-2;2<ha&&h.Pv(ha)!==e;ha--);if(2>=ha)throw Error("Invalid ascii85 string delimiter pair.");h=h.slice(2,ha)}ha=ca=fa=0;for(ea=h.length;ha<ea;ha++)ra=h.oI(ha),aa&&122===ra?Ba+=n.write(x,Ba):z&&121===ra?Ba+=n.write("    ",Ba):void 0!==y[ra]&&(fa*=85,fa+=y[ra],ca++,ca%5||(Ba=n.ofa(fa,Ba),ca=fa=0));if(ca){h=
5-ca;for(ha=0;ha<h;ha++)fa*=85,fa+=84;ha=3;for(ea=h-1;ha>ea;ha--)Ba=n.pfa(fa>>>8*ha&255,Ba)}return n.slice(0,Ba)};y.ega=new z({table:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.-:+=^!/*?&<>()[]{}@%$#".split("")});y.Hfa=new z({Uy:!0});y.fW=z}).call(this,f(383).Buffer)}}]);}).call(this || window)
