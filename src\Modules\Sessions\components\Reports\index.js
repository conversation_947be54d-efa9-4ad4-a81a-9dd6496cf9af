import React, { useState, useCallback } from 'react';
import ReportsHeader from './ReportsHeader';
import Overview from './Overview';
import SessionStatus from './SessionStatus';
import AttendanceLog from './AttendanceLog';
import StudentDetails from './StudentDetails';
import StaffDetails from './StaffDetails';
import Activity from './Activity';

const Reports = () => {
  const [selectedFilter, setSelectedFilter] = useState('overview');

  const handleFilterChange = useCallback((filterType) => {
    setSelectedFilter(filterType);
  }, []);

  const renderContent = () => {
    switch (selectedFilter) {
      case 'overview':
        return <Overview />;
      case 'session_status':
        return <SessionStatus />;
      case 'attendance_log':
        return <AttendanceLog />;
      case 'student_details':
        return <StudentDetails />;
      case 'staff_details':
        return <StaffDetails />;
      case 'activity':
        return <Activity />;
      default:
        return null;
    }
  };

  return (
    <>
      <ReportsHeader
        selectedFilter={selectedFilter}
        handleFilterChange={handleFilterChange}
      />
      <div className="pl-4 pr-4 min_height_400">
        <div className="d-flex justify-content-between pt-3">
          <div className="mt-4 pt-6">{renderContent()}</div>
        </div>
      </div>
    </>
  );
};

export default Reports;
