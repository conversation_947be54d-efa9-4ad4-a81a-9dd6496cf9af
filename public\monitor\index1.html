<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SLA - Riyadh - Session & Exams Monitor</title>
    <link rel="stylesheet" href="common/commonLibaries/bootstrap.min.css" />
    <link rel="stylesheet" href="sla_monitor.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css"
    />
    <link rel="stylesheet" href="./loader.css" />
    <script src="common/commonLibaries/jquery-3.6.0.min.js"></script>
    <script src="common/commonLibaries/bootstrap.bundle.min.js"></script>
    <script src="auth-monitor.js"></script>
    <script src="./loader.js"></script>
    <script src="./config/service.js"></script>
    <script src="./config/setIntervalTime.js"></script>
    <script src="./utils/icons.js"></script>
    <script src="./utils/timeFormat.js"></script>
    <script src="common.js"></script>
    <script src="classes.js"></script>
    <script src="sidepanel.js"></script>
    <!-- <script src="slider.js"></script> -->
  </head>
  <body>
    <header class="d-flex header text-white text-center py-2 main-content">
      <div class="container d-flex">
        <div class="mr-4">
          <img
            class="college-logo"
            src="./utils/images/logo1.jpeg"
            width="100"
            height="100"
            alt="logo"
          />
        </div>
        <div>
          <h1 class="title-name">
            <span class="name" id="college-name">Saudi Logistics Academy</span>
            - Monitoring
          </h1>
          <div
            id="current-date-time"
            class="d-flex justify-content-center align-items-center"
          >
            <span
              >Today: <span id="current-date"></span> | Current Time:
              <span id="current-time"></span
            ></span>
            <!-- <span class="instruction-wrapper ml-2"
              >(Dashboard updated every minute)</span
            > -->
            <span class="ml-2">| Last Updated: <span id="last-refresh-time">Just now</span></span>
            <i
              class="fas fa-cog settings-icon ml-2"
              onclick="openSettings()"
            ></i>
          </div>
        </div>
      </div>
      <div class="user-avatar-container">
        <div class="user-avatar" id="userAvatar">
          <span id="user-Initial"></span>
        </div>
        <div class="user-dropdown" id="userDropdown" style="display: none">
          <div class="user-info">
            <span class="user-name" id="user-name"></span>
            <span class="user-id" id="user-id"></span>
          </div>
          <div class="dropdown-divider"></div>
          <button class="dropdown-item">
            <i class="fas fa-user mr-3"></i> <span id="user-name-info"></span>
          </button>
          <button id="logoutButton" class="dropdown-item">
            <i class="fas fa-sign-out-alt mr-3"></i> Logout
          </button>
        </div>
      </div>
    </header>
    <main class="my-10 main-content">
      <div class="tab-container">
        <ul class="nav nav-tabs" id="monitorTabs" role="tablist">
          <li class="nav-item">
            <a class="nav-link active" id="sessions-tab" data-toggle="tab" href="#sessions" role="tab" aria-controls="sessions" aria-selected="true">Session Status</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="exams-tab" data-toggle="tab" href="#exams" role="tab" aria-controls="exams" aria-selected="false">Exams Status</a>
          </li>
        </ul>
        
        <div class="tab-content" id="monitorTabContent">
          <div class="tab-pane fade show active" id="sessions" role="tabpanel" aria-labelledby="sessions-tab">
            <div class="px-4 py-3">
              <div id="classes-grid" class="status-grid row">
                <!-- Classes will be loaded here from JSON -->
              </div>
              <div id="no-sessions-grid" style="display: none">
                <div class="no-exam">
                  <div class="text-container">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p class="m-0">There are no sessions scheduled for today</p>
                  </div>
                </div>
              </div>
              <div id="no-sessions-grid-filter" style="display: none">
                <div class="no-exam">
                  <div class="text-container">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p class="m-0">No sessions items match the selected filters</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tab-pane fade" id="exams" role="tabpanel" aria-labelledby="exams-tab">
            <div class="p-4">
              <div id="exams-grid" class="status-grid row">
                <!-- Exams will be loaded here from JSON -->
              </div>
              <div id="no-exams-grid" style="display: none">
                <div class="no-exam">
                  <div class="text-container">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p class="m-0">There are no exams scheduled for today</p>
                  </div>
                </div>
              </div>
              <div id="no-exams-grid-filter" style="display: none">
                <div class="no-exam">
                  <div class="text-container">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p class="m-0">No exams items match the selected filters</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <aside id="sidebar" class="sidebar main-content">
      <div class="sidebar-content">
        <button class="close-btn" onclick="closeSidebar()">×</button>
        <div id="details-content"></div>
      </div>
    </aside>
    <aside id="settings-sidebar" class="sidebar main-content">
      <div class="sidebar-content">
        <button class="close-btn" onclick="closeSettings()">×</button>
        <h4>Filter Settings</h4>
        <div class="filter-section">
          <h4>Show only (Sessions)</h4>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input class-filter"
              value="completed"
              id="completedCheck"
            />
            <label class="form-check-label" for="completedCheck"
              >Completed</label
            >
          </div>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input class-filter"
              value="ENDED"
              id="endedCheck"
            />
            <label class="form-check-label" for="endedCheck">Ended</label>
          </div>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input class-filter"
              value="missed"
              id="missedCheck"
            />
            <label class="form-check-label" for="missedCheck">Missed</label>
          </div>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input class-filter"
              value="late"
              id="lateStartedCheck"
            />
            <label class="form-check-label" for="lateStartedCheck"
              >Late Started</label
            >
          </div>
        </div>
        <div class="filter-section">
          <h4>Show only (Exams)</h4>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input exam-filter"
              value="late-reported"
              id="lateReportedCheck"
            />
            <label class="form-check-label" for="lateReportedCheck"
              >Late Reported</label
            >
          </div>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input exam-filter"
              value="extra-time"
              id="extraTimeCheck"
            />
            <label class="form-check-label" for="extraTimeCheck"
              >Extra Time Given</label
            >
          </div>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input exam-filter"
              value="not-started"
              id="notStartedCheck"
            />
            <label class="form-check-label" for="notStartedCheck"
              >Not Started on Time</label
            >
          </div>
          <div class="form-check">
            <input
              type="checkbox"
              class="form-check-input exam-filter"
              value="proctor-not-reported"
              id="proctorNotReportedCheck"
            />
            <label class="form-check-label" for="proctorNotReportedCheck"
              >Proctor Not Reported on Time</label
            >
          </div>
        </div>
        <div class="filter-section">
          <h4>Time Filter</h4>
          <div class="form-check">
            <input
              type="radio"
              class="form-check-input"
              name="time-filter"
              id="onTimeFilter"
              value="on-time"
            />
            <label class="form-check-label" for="onTimeFilter">On Time</label>
          </div>
          <div class="form-check">
            <input
              type="radio"
              class="form-check-input"
              name="time-filter"
              id="lateFilter"
              value="late"
            />
            <label class="form-check-label" for="lateFilter">Late</label>
          </div>
        </div>
        <div class="color-picker">
          <h4>Color Picker</h4>
          <div class="header-color">
            <label for="colorPicker" class="form-check-label"
              >Pick a color</label
            >
            <input
              type="color"
              class="colorPicker"
              id="colorPicker"
              value="#00afaa"
            />
          </div>
        </div>
        <div class="date-picker mt-2">
          <h4>Select Date</h4>
          <input type="date" class="form-control" id="dateFilter" />
        </div>
      </div>
      <div class="filter-section mt-3">
        <h4>Refresh Time (seconds)</h4>
        <div class="refresh-time-container">
          <input
            type="number"
            class="form-control"
            id="refreshTimeInput"
            min="600000"
            value="600000"
          />
          <small class="text-muted"
            >Minimum 5 minutes, Maximum any value (default 10 minutes)</small
          >
          <div id="refreshTimeDisplay" class="mt-2">10 minutes</div>
        </div>
        <button class="btn btn-primary mt-3" onclick="applyClassFilter()">
          Apply
        </button>      
      </div>
      <div class="clear-cache-section mt-3 text-center">
        <p id="clearCache" class="mt-3">
          <i class="fas fa-sync-alt"></i> Clear All Filters
        </p>
      </div>
    </aside>

    <footer class="text-white text-center py-2 footer main-content">
      <div class="container">
        <span>DigiClass I DigiAssess I Q360 I HEBA.ai I Integer I</span>
        <a class="site-link text-white ml-2" href="https://digi-val.com/"
          >www.digi-val.com</a
        >
      </div>
    </footer>
  </body>
</html>
