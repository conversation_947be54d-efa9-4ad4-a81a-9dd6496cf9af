session filter should show what is selected inside (next to status )
filter apply for exam proctor late 
auto refresh based on the time (get the refresh time)
one day once auto refresh 
impliemnt the unification service 
side panel design fixed 
handle error in api
ended early 


Digiassesses 

    current 
         500$ 
            upm 
            sla jedda 
            sla ryadh 
            stagin Oracle 

2000 concurrent users 
    import 
    start facial 
    password

    
    website - application form - (22000 applicants)
        excel 
            name ,national id, email,marks,(filter based on marks)


            
level 1:    9000 - (exiting google form) - 
        windows 
            email id 
                send email 
                link 
                window based record - simple 
                serverless form 
                    millions 
                    windows 
                        40 and above 



level 2:     onsite 3000-4000(1800) - concurrent   
900



security concern 


    remote browser 

        digibrowser 
            content can be shown
                -


    qtn preparation 
    answer 
    