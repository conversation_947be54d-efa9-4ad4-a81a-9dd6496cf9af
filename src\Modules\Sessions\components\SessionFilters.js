import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Badge } from 'react-bootstrap';
import { IconButton } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import MButton from 'Widgets/FormElements/material/Button';

const SessionFilters = ({
  filters,
  selectedFilter,
  onFilterChange,
  showArrows,
  onScrollLeft,
  onScrollRight,
  exportEnabled,
  onExportClick,
}) => {
  const { t } = useTranslation();
  const badgeContainerRef = useRef(null);

  return (
    <div className="inner_header">
      <div className="row digi-white-bg digi-set-pad align-items-center">
        <div className="col-lg-12">
          <div className="row">
            <div
              className={`${exportEnabled ? 'col-md-8 col-lg-7' : 'col-md-12'}`}
              style={{ position: 'relative' }}
            >
              <div
                className={`badge-container py-1 ${showArrows ? 'pl-3' : ''}`}
                ref={badgeContainerRef}
                style={{
                  overflowX: 'hidden',
                  whiteSpace: 'nowrap',
                  paddingRight: '40px',
                }}
              >
                {filters.map((filter, index) => (
                  <span className="pb-2" key={index}>
                    <Badge
                      className={`digi-recent-button btn ${
                        selectedFilter === filter.get('type') ? 'active' : ''
                      }`}
                      pill
                      variant=""
                      onClick={() => onFilterChange(filter.get('type'))}
                    >
                      {t(filter.get('type', '').replace('_', ' '))} (
                      {filter.get('count')})
                    </Badge>
                  </span>
                ))}
              </div>
              {showArrows && (
                <>
                  <IconButton
                    onClick={onScrollLeft}
                    style={{
                      position: 'absolute',
                      left: 0,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: 1,
                      borderRadius: 'inherit',
                      background: '#ffffff',
                    }}
                  >
                    <ArrowBackIosIcon sx={{ color: '#2F80ED' }} />
                  </IconButton>
                  <IconButton
                    onClick={onScrollRight}
                    style={{
                      position: 'absolute',
                      right: 0,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: 1,
                      borderRadius: 'inherit',
                      background: '#ffffff',
                    }}
                  >
                    <ArrowForwardIosIcon sx={{ color: '#2F80ED' }} />
                  </IconButton>
                </>
              )}
            </div>
            {exportEnabled && (
              <div className="col-md-4 col-lg-5">
                <div className="float-right pt-1">
                  <MButton variant="contained" clicked={onExportClick}>
                    Export
                  </MButton>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

SessionFilters.propTypes = {
  filters: PropTypes.object.isRequired,
  selectedFilter: PropTypes.string.isRequired,
  onFilterChange: PropTypes.func.isRequired,
  showArrows: PropTypes.bool,
  onScrollLeft: PropTypes.func.isRequired,
  onScrollRight: PropTypes.func.isRequired,
  exportEnabled: PropTypes.bool,
  onExportClick: PropTypes.func.isRequired,
};

export default SessionFilters;
