[{"topic": "Digiclass Attendance", "new": true, "subtopics": [{"title": "How to use Liveliness check, <PERSON><PERSON>, <PERSON><PERSON><PERSON>(video)", "url": "https://www.youtube.com/embed/I0Cnza6_lJk", "userType": "staff"}, {"title": "Tardiness", "url": "https://www.youtube.com/embed/VdXLDlOdQEw", "userType": "staff"}, {"title": "How to export single session attendance reports in DC?", "url": "https://www.youtube.com/embed/RJsh6C_xndE", "userType": "staff"}, {"title": "How to export All session attendance reports in DC?", "url": "https://www.youtube.com/embed/8Hya5hNrDlw", "userType": "staff"}, {"title": "How staff conducts remote/online session in Digiclass?", "url": "https://www.youtube.com/embed/2vvE3KknpMg", "userType": "staff"}], "resources": []}, {"topic": "Digiclass Features", "new": true, "subtopics": [{"title": "DigiClass Student features(Arabic)", "url": "https://www.youtube.com/embed/-7HzTo-qxtg", "new": true, "userType": "student"}, {"title": "How to use help desk in DigiClass Student(Arabic)", "url": "https://www.youtube.com/embed/IEz2HrJdaRc", "new": true, "userType": "student"}], "resources": []}, {"topic": "Schedule Management", "new": true, "subtopics": [{"title": "How to check students schedule(Video)", "url": "https://www.youtube.com/embed/2SehmHNUYA0", "new": true, "userType": "student"}], "resources": []}, {"topic": "Digiclass Activity", "subtopics": [{"title": "How to create activity in Standard Method?", "url": "https://www.youtube.com/embed/s9PWI9to5XQ", "new": true, "userType": "staff"}, {"title": "How to create activity in scheduled basis?", "url": "https://www.youtube.com/embed/ZMQ649HKXs8", "new": true, "userType": "staff"}, {"title": "How to create activity from Question Bank?", "url": "https://www.youtube.com/embed/7odOPpjcAq0", "new": true, "userType": "staff"}, {"title": "How to draft the created Activity?", "url": "https://www.youtube.com/embed/Zk0CbRfh9sM", "new": true, "userType": "staff"}], "resources": []}, {"topic": "Digiclass Survey", "subtopics": [{"title": "How to create Survey", "url": "https://www.youtube.com/embed/CbnP2tfzlKA", "new": true, "userType": "staff"}, {"title": "How to start and Monitor survey in Digiclass", "url": "https://www.youtube.com/embed/nFES2Qd9HOI", "new": true, "userType": "staff"}, {"title": "How to stop survey and export results in Digiclass", "url": "https://www.youtube.com/embed/bUQIrVFmUKQ", "new": true, "userType": "staff"}], "resources": []}, {"topic": "Digiclass Assignment", "subtopics": [{"title": "Assignment Introduction", "url": "https://www.youtube.com/embed/XcP8AcpQdkc", "new": true, "userType": "staff"}, {"title": "Assignment Settings", "url": "https://www.youtube.com/embed/UZS9zaYfofw", "new": true, "userType": "staff"}, {"title": "Assignment Creation", "url": "https://www.youtube.com/embed/0KsW2TZovcA", "new": true, "userType": "staff"}, {"title": "Assignment Creation using Rubrics in CAM", "url": "https://www.youtube.com/embed/VRro6eAemeY", "new": true, "userType": "staff"}, {"title": "Assignment Evaluation", "url": "https://www.youtube.com/embed/q3bwR4nLP", "userType": "staff"}, {"title": "Assignment Reports", "url": "https://www.youtube.com/embed/k1TG_S1SO5M", "userType": "staff"}, {"title": "Assignment Submission", "url": "https://www.youtube.com/embed/6eRFf0je_do", "new": true, "userType": "student"}, {"title": "Assignment Evaluation with Rubrics", "url": "https://www.youtube.com/embed/oVZ_gnYH4LM", "new": true, "userType": "staff"}, {"title": "Assignment use in Mobile", "url": "https://www.youtube.com/embed/e9V801-RfMs", "new": true, "userType": "student"}, {"title": "How to Extend the Due Date, Reset and Duplicate an Assignment", "url": "https://www.youtube.com/embed/DMBM4joK68Q", "new": true, "userType": "staff"}], "resources": [{"title": "Assignment Creation(PPT)", "url": "https://docs.google.com/presentation/d/1UGDMo5bMQBJ4kPHk1PCzk4oKan9GW0NL/edit?usp=sharing&ouid=116113482800659869718&rtpof=true&sd=true", "type": "link", "new": true, "userType": "staff"}, {"title": "Assignment Report(PPT)", "url": "https://docs.google.com/presentation/d/18SW84Arsh1zQjpxkw6gnBwvnTPy1rzUJ/edit?usp=sharing&ouid=116113482800659869718&rtpof=true&sd=true", "type": "link", "new": true, "userType": "staff"}]}, {"topic": "Digiclass Announcements", "new": true, "subtopics": [{"title": "what is Announcements?", "url": "https://www.youtube.com/embed/03_EWAHvuHA", "userType": "staff"}, {"title": "How staff and students can view the Announcements?", "url": "https://www.youtube.com/embed/qNkt3t36onU", "userType": "staff"}, {"title": "How to create Announcements?", "url": "https://www.youtube.com/embed/9HZn4g7m3NM", "userType": "staff"}, {"title": "Announcement settings", "url": "https://www.youtube.com/embed/0PJlKKcEQOI", "userType": "staff"}], "resources": []}, {"topic": "Digiclass Discussion Forum", "new": true, "subtopics": [{"title": "Discussion Forum", "url": "https://www.youtube.com/embed/-qqfX_UlFaI", "userType": "staff"}], "resources": []}, {"topic": "SAAM(Student Academic Accountability Management)", "new": true, "subtopics": [{"title": "How to Apply Permission in SAAAM", "url": "https://www.youtube.com/embed/lJPmaHqUPEM", "new": true, "userType": "student"}, {"title": "How to Apply Leave in SAAAM", "url": "https://www.youtube.com/embed/iInnRiwz2tA", "new": true, "userType": "student"}, {"title": "How to Approve Leave in SAAM", "url": "https://www.youtube.com/embed/6jWUmCWZmg4", "new": true, "userType": "staff"}, {"title": "How to check students warning in Faculty Dashboard Page", "url": "https://www.youtube.com/embed/3a70sVaI2Vk", "new": true, "userType": "staff"}], "resources": []}, {"topic": "AAM(Academic Accountability Management)", "new": true, "subtopics": [{"title": "How to do Leave Settings", "url": "https://www.youtube.com/embed/KIVpDEk00Zc", "new": true, "userType": "staff"}, {"title": "How to do Permission settings", "url": "https://www.youtube.com/embed/ARuWSa3qyng", "new": true, "userType": "staff"}], "resources": []}]