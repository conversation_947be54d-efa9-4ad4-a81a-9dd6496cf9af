<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Digiclass Zoom Disconnected</title>
    <link rel="icon" href="../favicon.svg" />
    <link rel="apple-touch-icon" href="../favicon.svg" />
    <link rel="icon" href="../favicon.svg" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
      body {
        background-color: #56b8ff;
        color: white;
        font-size: 30px;
      }

      .container {
        min-height: 300px;
        padding: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 35px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    </style>
  </head>
  <body>
    <script src="js/common.js"></script>
    <div class="container">
      <span class="item">Zoom Disconnected......</span>
    </div>
    <script type="text/javascript">
      var _0x5d696d = _0x2862;
      (function (_0x2f39cb, _0x5741e3) {
        var _0x2616a4 = _0x2862,
          _0x395afa = _0x2f39cb();
        while (!![]) {
          try {
            var _0x3c28dd =
              (parseInt(_0x2616a4(0x1ba)) / 0x1) *
                (-parseInt(_0x2616a4(0x1b4)) / 0x2) +
              (-parseInt(_0x2616a4(0x1b3)) / 0x3) *
                (-parseInt(_0x2616a4(0x1c2)) / 0x4) +
              parseInt(_0x2616a4(0x1b8)) / 0x5 +
              (-parseInt(_0x2616a4(0x1b9)) / 0x6) *
                (parseInt(_0x2616a4(0x1bc)) / 0x7) +
              -parseInt(_0x2616a4(0x1b5)) / 0x8 +
              (parseInt(_0x2616a4(0x1bb)) / 0x9) *
                (parseInt(_0x2616a4(0x1b6)) / 0xa) +
              parseInt(_0x2616a4(0x1bd)) / 0xb;
            if (_0x3c28dd === _0x5741e3) break;
            else _0x395afa['push'](_0x395afa['shift']());
          } catch (_0x7e8ee4) {
            _0x395afa['push'](_0x395afa['shift']());
          }
        }
      })(_0x8694, 0x4a8b6),
        window[_0x5d696d(0x1c1)](_0x5d696d(0x1c0), function (_0x91bb97) {
          var _0x246498 = _0x5d696d;
          localStorage['removeItem'](_0x246498(0x1b7)),
            localStorage[_0x246498(0x1b2)](_0x246498(0x1bf)),
            localStorage['removeItem'](_0x246498(0x1be));
        });
      function _0x2862(_0x46cade, _0x53fd72) {
        var _0x86942 = _0x8694();
        return (
          (_0x2862 = function (_0x28624e, _0x3330cf) {
            _0x28624e = _0x28624e - 0x1b2;
            var _0x557986 = _0x86942[_0x28624e];
            return _0x557986;
          }),
          _0x2862(_0x46cade, _0x53fd72)
        );
      }
      function _0x8694() {
        var _0x70fdf9 = [
          '6QeWoUv',
          '240197FXAGNG',
          '21789NiUYGJ',
          '1019543AFaEeU',
          '10396089SoGdFo',
          'loggedIn',
          '_lib',
          'DOMContentLoaded',
          'addEventListener',
          '21900NRgsjZ',
          'removeItem',
          '69xRCsXp',
          '4AoRSwG',
          '2109824SZvReL',
          '180sgpJyp',
          '_lib_1',
          '402515oiSkLZ',
        ];
        _0x8694 = function () {
          return _0x70fdf9;
        };
        return _0x8694();
      }
    </script>
  </body>
</html>
