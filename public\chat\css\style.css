html,
body {
  font-family: 'Roboto', sans-serif;
  background-color: #ffffff;
  margin: 0;
  padding: 0;
}
.clr {
  clear: both;
}

.chat-input-box {
  box-shadow: 0px -2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  position: fixed;
  bottom: 0px;
  width: -webkit-fill-available;
  text-align: center;
  background-color: #ffffff;
  z-index: 99;
  display: flex;
  padding: 0 20px;
  text-align: center;
  margin: 0 auto;
  /* justify-content: center;
  align-items: center; */
}

.chat-input {
  box-shadow: 0px 1px 3px 0px #11182733;
  border-bottom: 1px solid #147afc;
  border-radius: 4px;
  padding: 6px 0px 0px 0;
  margin: 5px 10px;
  width: -webkit-fill-available;
  background-color: #ffffff;
}

.ask-me-type {
  display: flex;
  padding: 0 20px;
  text-align: center;
  margin: 0 auto;
  /* width: fit-content; */
  justify-content: center;
  align-items: center;
}

.rounded-input {
  padding: 16px 10px 0px 10px;
  /* margin-right: 12px; */
  border: none;
  outline: none;
  /* background: #f3f4f6;
  border-radius: 30px; */
  width: 80%;
  min-height: 20px;
  resize: none; /* Prevent manual resizing */
  overflow-y: hidden;
  font-family: 'Roboto', sans-serif;
}

.chat-support ul {
  padding: 0;
  margin: 5px 0 0 20px;
}

.chat-support ul li {
  padding: 5px 0;
  font-size: 13px;
  font-weight: 400;
}

/***Scrollbar design****/
::-webkit-scrollbar {
  width: 7px;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c4c4c4;
  border-radius: 10px;
}

.search-loader {
  position: fixed;
  top: 0;
  width: -webkit-fill-available;
  height: 100%;
  /* background-color: #f9fafb; */
  background-color: #ffffff;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

/* @keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} */

/* new loader */
.lds-circle {
  display: inline-block;
  transform: translateZ(1px);
  padding: 0 100px 0 0;
}
.lds-circle > div {
  display: inline-block;
  width: 32px;
  height: 32px;
  margin: 0px;
  border-radius: 50%;
  background: #cceeff;
  animation: lds-circle 2.4s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}
@keyframes lds-circle {
  0%,
  100% {
    animation-timing-function: cubic-bezier(0.5, 0, 1, 0.5);
  }
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(1800deg);
    animation-timing-function: cubic-bezier(0, 0.5, 0.5, 1);
  }
  100% {
    transform: rotateY(3600deg);
  }
}

/*************/

.ask-heba {
  font-size: 24px;
  color: #000000;
  font-weight: 500;
  margin: 0;
  padding: 5px 0 0 0;
  text-transform: capitalize;
}

.ask-heba-text {
  font-size: 24px;
  color: #000000;
  font-weight: 700;
  margin: 0;
  padding: 5px 0 0 0;
  letter-spacing: 10px;
}

.header {
  box-shadow: 0px 1px 3px 0px #11182733;
  border-bottom: 1px solid #d1d5db;
  border-radius: 8px 8px 0px 0px;
  padding: 16px 0;
}

.header h3 {
  margin: 0px;
  padding: 5px 0 0 0;
  color: #374151;
  font-size: 20px;
  font-weight: 700;
}

.flt-left {
  float: left;
}

.flt-left h3 {
  padding: 5px 0 0 10px;
}

.flt-right {
  float: right;
}

.side-nav-icon {
  vertical-align: middle;
  padding: 0 8px 0px 0;
}

/* .digi-question {
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  float: left;
  color: #374151;
  text-align: left;
  cursor: pointer;
  width: 230px;
  margin: 12px;
} */

/* .digi-courses {
  display: flex; 
  align-items: center;
  padding: 20px 28px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  float: left;
  color: #374151;
  text-align: left;
  cursor: pointer;
  width: 26%;
  margin: 12px;
  font-size: 16px;
  font-weight: 500;
} */

.digi-courses,
.common-chip,
.digi-question {
  display: flex; /* Use flexbox */
  align-items: center;
  padding: 20px 28px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  float: left;
  color: #374151;
  text-align: left;
  cursor: pointer;
  width: 280px;
  margin: 12px;
  font-size: 16px;
  font-weight: 500;
}

.digi-courses h6,
.common-chip h6 {
  color: #374151;
  font-size: 15px;
  font-weight: 400;
  margin: 0;
  text-align: center;
}

.digi-courses.active,
.common-chip.active {
  background-color: #dcfce7;
  border: 1px solid #16a34a;
  border-radius: 8px;
  color: #374151;
  text-align: left;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.digi-courses.active::before,
.common-chip.active::before {
  content: '\2713';
  padding: 0 8px 0 0;
  font-size: 13px;
}

.search-container {
  position: relative;
  padding: 0 10px 0 0;
}

.search-input {
  padding: 10px 30px 10px 45px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  outline: none;
  background-color: #f3f4f6;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  padding: 5px 0 0 5px;
}

.close_icon {
  cursor: pointer;
  padding: 0 5px;
}

.rounded-input1 {
  padding: 12px 10px 12px 20px;
  margin-top: 8px;
  margin-right: 10px;
  border: none;
  outline: none;
  background: #f3f4f6;
  border-radius: 10px;
  width: 250px;
}

/***side nav css***/
.sidebar {
  position: fixed;
  left: -325px;
  width: 325px;
  height: 100%;
  background: #ffffff;
  transition: all 0.5s ease;
  overflow-y: auto;
  z-index: 999;
}

.sidebar header {
  font-size: 22px;
  color: white;
  line-height: 70px;
  text-align: center;
  background: #063146;
  user-select: none;
}

.sidebar ul {
  margin: 12px;
  padding: 0px;
}

.sidebar ul li {
  list-style: none;
  padding: 0px;
  margin: 0px;
}

.sidebar ul a {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  padding: 10px 0 10px 20px;
  box-sizing: border-box;
  border-bottom: 1px solid #d1d5db;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  transition: 0.4s;
  text-decoration: none;
}

.sidebar ul li:hover a {
  /* padding-left: 30px; */
  background-color: #f7fcfd;
}

.sidebar ul li a.active {
  background-color: #f7fcfd;
}

.sidebar ul a i {
  margin-right: 16px;
}

.sidebar ul li:last-child a {
  border-bottom: none;
}

#check {
  display: none;
}

label #btn {
  /* left: -14px;
  top: 200px;
  transition: all 0.5s;
  border-radius: 3px;
  cursor: pointer;
  position: fixed;
  z-index: 11; */
}

label #cancel {
  position: fixed;
  border-radius: 3px;
  cursor: pointer;
  z-index: 1111;
  left: -195px;
  top: 335px;
  font-size: 30px;
  color: #0a5275;
  transition: all 0.5s ease;
}

#check:checked ~ .sidebar {
  left: 0;
}

#check:checked ~ label #btn {
  left: 250px;
  opacity: 0;
  pointer-events: none;
}

#check:checked ~ label #cancel {
  left: 290px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 1px 3px 0px #11182733;
}

#check:checked ~ section {
  margin-left: 330px;
}

section {
  background: url(bg.jpeg) no-repeat;
  background-position: center;
  background-size: cover;
  height: 100vh;
  transition: all 0.5s;
}

@media screen and (max-height: 450px) {
  .sidenav {
    padding-top: 15px;
  }
  .sidenav a {
    font-size: 18px;
  }
}

.arrow-img {
  cursor: pointer;
}

.open-arrow {
  display: none;
}

.close-arrow {
  display: inline;
}

.chat-helper-bg {
  /* background-color: #f4f6f7; */
  background-color: #f9fafb;
  padding: 20px 20px 0px 20px;
}

.icon_group {
  margin-top: 0px;
  display: flex;
  float: right;
  padding: 20px 10px 0px 0;
}
.chat-helper-bg-inner {
  background-color: #f5f5f6;
  border-radius: 8px;
  padding: 18px;
  box-shadow: 0px 1px 3px 0px #11182733;
}

.chat-bg-white {
  box-shadow: 0px 1px 3px 0px #11182733;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px 15px 15px 20px;
  margin: 25px 30px;
  text-align: left;
}

.bg-white {
  background-color: #ffffff;
}

.chat-text-center {
  text-align: center;
  color: #6b7280;
}

.text-left {
  text-align: left;
}

.div-center {
  text-align: center;
  margin: 0 auto;
  width: fit-content;
}

.div-center-course {
  text-align: center;
  margin: 0 auto !important;
  width: 750px;
}

.div-center-suggestion {
  text-align: center;
  margin: 0 auto;
  width: 750px;
}

.button-response {
  background-color: #ffffff;
  border-radius: 22px;
  width: 250px;
  padding: 15px;
  margin: 0px 0px 15px 0px;
  text-align: center;
  box-shadow: 0px 1px 3px 0px #11182733;
  border: none;
  color: #374151;
  font-size: 16px;
  font-weight: 500;
}

.icon-vertical-align {
  vertical-align: middle;
  padding: 0 3px 0 0px;
}

.page-banner {
  padding: 78px 0 0 0;
}

.fix-header {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 1;
  transition: top 0.3s;
}

.fix-header.hidden {
  top: -100px;
}

.digi-height {
  min-height: 38px;
}

.chat-helper-bg p {
  color: #6b7280;
  /* font-size: 14px; */
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  margin-bottom: 0px;
}

.chat-helper-bg p strong {
  color: #6b7280;
  font-size: 14px;
  line-height: 24px;
  text-align: left;
}

.d-flex {
  display: flex;
}

.divider {
  padding: 5px 12px;
}

.chat-border-bottom {
  justify-content: space-between;
  border-bottom: 1px solid #d1d5db;
  padding: 0 0 8px 0;
}

.flex-1 {
  flex: 1;
}

.img-attach {
  padding: 25px 15px 0 0;
}

.img-voice {
  margin: 15px 0 0 0;
}

.chat-bg {
  background-color: #f9fafb;
  /* background-color: #f4f6f7; */
  /* padding: 0 0 0 20px; */
}

.app-font {
  border-radius: 6px;
  padding: 6px;
  border: 1px solid #d1d5db;
  align-items: center;
  width: 100px;
  height: 30px;
}

.app-font-size {
  color: #374151;
  font-weight: bold;
  padding: 5px 7px;
  cursor: pointer;
}

.app-font-size:hover {
  color: #147afc;
  font-weight: bold;
  padding: 5px 7px;
  background-color: #eff9fb;
  border-radius: 4px;
  cursor: pointer;
}

.app-font-size.active {
  color: #147afc;
  font-weight: bold;
  padding: 5px 7px;
  background-color: #eff9fb;
  border-radius: 4px;
  cursor: pointer;
}

.f-12 {
  font-size: 12px;
}

.f-14 {
  font-size: 14px;
}

.f-18 {
  font-size: 18px;
}

.digi-d-flex {
  display: flex;
}

.digi-question-align {
  padding-right: 20px;
}

/* @media (max-width: 768px) {
  .digi-question {
    font-size: 14px;
  }

  .div-center {
    display: flex;
  }
} */

/* @media (max-width: 1024px) {
  .ask-heba {
    font-size: 16px;
    color: #000000;
    font-weight: 500;
    margin: 0;
    padding: 5px 0 0 0;
  }

  .digi-question {
    text-align: left;
    cursor: pointer;
    width: 160px;
  }
} */

@media screen and (max-width: 768px) {
  .div-center-suggestion {
    width: 80%;
  }

  .header {
    padding: 6px 0 12px 0;
    border-radius: 0;
  }

  .digi-question {
    width: 80%;
    margin: 12px 0;
  }

  .digi-courses {
    width: 80%;
    margin: 12px 0;
  }

  .common-chip {
    width: 80%;
    margin: 12px 0;
  }

  .box {
    padding: 6px;
    text-align: left;
    position: relative;
    width: 90%;
  }

  .box select {
    margin: 12px 0px 12px 0px !important;
    width: 98% !important;
  }

  .box:after {
    right: 40px !important;
  }

  .flt-left {
    float: none;
  }

  .flt-right {
    float: none;
    padding: 10px 0 0 10px;
  }

  .chat-input-box {
    padding: 0px;
  }

  .search-input {
    width: 78%;
  }

  .fix-header {
    background-color: #147afc !important;
  }

  .page-banner {
    padding: 102px 0 0 0 !important;
  }

  .chat-bg-white {
    padding: 15px !important;
  }

  .scroll-to-bottom {
    right: 1% !important;
    bottom: 75px !important;
  }

  .scroll-to-top {
    bottom: 125px !important;
    right: 3px !important;
  }

  #check:checked ~ section {
    margin-left: 0px;
  }

  .digi-d-flex {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
  }

  .digi-question-align {
    padding-right: 0;
  }

  /* .content {
    opacity: 0.1;
  } */

  .sidebar-open-img1 {
    display: none;
  }

  .rotate-image-open {
    left: -16px !important;
    background-color: #ffffff;
    box-shadow: 0px 1px 3px 0px #11182733;
    border-radius: 10px !important;
    width: 50px;
  }

  .search-input {
    background-color: #ffffff;
    width: 75%;
  }

  .chat-input {
    box-shadow: none !important;
    border-bottom: none;
  }

  .img-voice {
    margin: 18px 0 0 0;
  }

  .header h3 {
    color: #ffffff;
  }

  .chat-input {
    margin: 0px !important;
  }

  .chat-bg-white {
    margin: 25px 2px;
  }
}

@media screen and (max-width: 767px) {
  .desktop-send {
    display: none !important;
  }
}

/* Hide desktop view div in screens larger than 767px */
@media screen and (min-width: 768px) {
  .mobile-send {
    display: none !important;
  }
}

@media screen and (max-width: 576px) {
  .div-center-suggestion,
  .div-center-course {
    width: 92%;
    margin: 0px auto !important;
  }

  .pb-mobile {
    padding: 0 0px 90px 0 !important;
  }

  .icon_group {
    padding: 20px 10px 190px 0;
  }
}

@media screen and (max-width: 1024px) {
  .box {
    padding: 6px;
    width: 99%;
  }

  .box select {
    width: 96%;
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .div-center-course {
    text-align: center;
    margin: 0 auto !important;
    width: 85%;
  }

  .div-center-suggestion {
    text-align: center;
    margin: 0 auto !important;
    width: 85%;
  }

  .common-chip {
    width: 38%;
    margin: 10px;
  }

  .digi-courses {
    width: 38%;
    margin: 10px;
  }

  .digi-question {
    width: 38%;
    margin: 10px;
  }

  /* .search-input {
    width: 90%;
  } */

  .mobile-send {
    display: none !important;
  }
}

.box {
  padding: 6px 6px 6px 0;
  text-align: left;
  position: relative;
  width: 99.2%;
}

.box select {
  background-color: #ffffff;
  color: #374151;
  margin: 12px 0px 12px 12px;
  padding: 20px 50px 20px 28px;
  /* width: 686px; */
  width: 94%;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  -webkit-appearance: button;
  appearance: button;
  outline: none;

  -webkit-appearance: none;
  -moz-appearance: none;
  text-indent: 0.01px;
  text-overflow: '';
  position: relative;
}

.box:after {
  content: '';
  position: absolute;
  right: 50px;
  top: 42px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #333;
}

.box:hover::before {
  color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.2);
}

.box select option {
  padding: 30px;
}

.chat-label {
  color: #4b5563;
  font-size: 14px;
  font-weight: 400;
}

/***spacing css****/
.pl-15 {
  padding-left: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-25 {
  padding-top: 25px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-50 {
  padding-right: 50px;
}

.chat-bg-white ul {
  margin: 5px 5px 0px 5px;
  padding: 0 0 0 12px;
}

.chat-bg-white ul li {
  color: #6b7280;
  font-size: 14px;
  text-align: left;
  line-height: 24px;
}

.loader-gen {
  display: inline;
  color: #ffffff;
  padding: 6px 0px;
  font-size: 14px;
  font-weight: 500;
  background: radial-gradient(
    415.93% 130.28% at 46.56% 95.75%,
    #147afc 0%,
    #003374 95%
  );
  border: none;
  border-radius: 20px;
  width: 135px;
}

.loader-book {
  width: 50px;
  vertical-align: middle;
}

.loader-gen-image {
  width: 28px;
  vertical-align: middle;
  padding: 0 10px 0 0;
}

.chat-align {
  display: flex;
  justify-content: space-between;
}

.go-up {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.up-arrow {
  padding: 0px 20px 10px 0;
}

.cursor-pointer {
  cursor: pointer;
}

/***tooltip css ***/
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding-bottom: 2px;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 79px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px 0;
  position: absolute;
  z-index: 1;
  top: 100%;
  left: 80%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 13px;
}

.tooltip .tooltiptext::after {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent black transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/******tooltip right***/
.tooltip .tooltiptextright {
  visibility: hidden;
  width: 110px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px 0;
  position: absolute;
  z-index: 1;
  top: -5px;
  left: 105%;
  font-size: 13px;
}

.tooltip:hover .tooltiptextright {
  visibility: visible;
}

.tooltip .tooltiptextright::after {
  content: ' ';
  position: absolute;
  top: 50%;
  left: -10px;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent black transparent transparent;
}

/* 
.tooltipleft {
  position: relative;
  display: inline-block;
}

.tooltipleft .tooltiptextleft {
  visibility: hidden;
  width: 120px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  top: 10px;
  right: 105%;
}

.tooltipleft .tooltiptextleft::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent black;
}

.tooltipleft:hover .tooltiptextleft {
  visibility: visible;
} */

.scroll-button-container {
  position: fixed;
  bottom: 62px;
  right: 5px;
}

.scroll-button {
  position: fixed;
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 20px;
  cursor: pointer;
  outline: none;
}

.scroll-button:hover {
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.3);
}

.scroll-to-top {
  bottom: 85px;
  right: 20px;
  display: none;
}

.scroll-to-bottom {
  bottom: 85px;
  right: 50%;
  display: none;
}

.sidebar-open-img1 {
  left: -14px;
  top: 240px;
  transition: all 0.5s;
  border-radius: 3px;
  cursor: pointer;
  position: fixed;
  z-index: 99;
}

.rotate-image-close {
  transform: rotate(90deg);
}

.rotate-image-open {
  transform: rotate(-90deg);
  left: 22px;
  top: 315px;
  transition: all 0.5s;
  border-radius: 3px;
  cursor: pointer;
  position: fixed;
  z-index: 9;
}

.sidebar-close-arrow {
  background-color: #ffffff;
  padding: 12px;
}

/****snackbar css ******/
#snackbar {
  visibility: hidden;
  min-width: 250px;
  background-color: #147afc;
  border: 1px solid #147afc;
  color: #ffffff;
  text-align: center;
  border-radius: 12px;
  padding: 16px;
  position: fixed;
  z-index: 1;
  left: 50%;
  transform: translateX(-50%); /* Center horizontally */
  bottom: 50%; /* Center vertically */
  font-size: 17px;
}

#snackbar.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
  from {
    bottom: -50%;
    opacity: 0;
  }
  to {
    bottom: 50%;
    opacity: 1;
  }
}

@keyframes fadein {
  from {
    bottom: -50%;
    opacity: 0;
  }
  to {
    bottom: 50%;
    opacity: 1;
  }
}

.heba-user-name {
  text-transform: capitalize;
  font-weight: bold;
}

.send-icon {
  padding: 6px 8px 0 0px;
}

/*kabeer*/

/* .fix-header {
  position: sticky;
  width: 100%;
  background: white;
  top: 0;
  z-index: 1;
} */

.hidden {
  display: none;
}
/**/
