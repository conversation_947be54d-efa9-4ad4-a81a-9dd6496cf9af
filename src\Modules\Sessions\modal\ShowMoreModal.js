import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { List, Map } from 'immutable';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import {
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material';

import { getShowMoreData } from 'ReduxApi/Sessions/action';
import { selectShowMoreData } from 'ReduxApi/Sessions/selectors';
import { getMergeSessions } from 'Modules/Utils/utils';
import SuccessImg from '../../../Assets/icons/add_circle.svg';
import FailedImg from '../../../Assets/icons/groupicon.svg';
import useMomentHooks from 'Hooks/useMomentHooks';

const getRetakeOptionLabel = (option) => {
  const labels = {
    retake_all: 'Repeat for All',
    retake_absent: 'Repeat for Late Attendees',
    buzzer: 'Buzzer',
    surprise_quiz: 'Engager',
    primary: 'Primary',
    manual: 'Secondary',
  };
  return labels[option] || '';
};

export default function ShowMoreModal({
  open,
  cancelModal,
  schedule,
  authData,
}) {
  const dispatch = useDispatch();
  const showMoreData = useSelector(selectShowMoreData);
  const { humanReadableTimeWithoutDate } = useMomentHooks();
  const userId = authData.get('_id', '');
  const scheduleId = schedule.get('_id', '');

  useEffect(() => {
    if (scheduleId && userId) dispatch(getShowMoreData(scheduleId, userId));
  }, [scheduleId, userId]); //eslint-disable-line

  const handleClose = () => cancelModal(false);

  const getTitle = () => {
    const deliverySymbol = schedule.getIn(['session', 'delivery_symbol'], '');
    const deliveryNo = schedule.getIn(['session', 'delivery_no'], '');
    if (schedule.get('merge_status', false)) {
      const mergeSessions =
        schedule.get('merge_with', List()).size > 0
          ? `, ${getMergeSessions(schedule.get('merge_with'))}`
          : '';
      return `${deliverySymbol} ${deliveryNo}${mergeSessions}`;
    }
    return schedule.get('type', '') === 'regular'
      ? `${deliverySymbol} ${deliveryNo}`
      : `${schedule.get('title', '')} - ${schedule.get('sub_type', '')}`;
  };

  const renderStudentStatus = ({ isPrimary, attendanceData }) => {
    const currentStudent = schedule
      .get('students', List())
      .find((student) =>
        [
          userId,
          attendanceData?.getIn(['students', '_student_id'], ''),
        ].includes(student.get('_id', ''))
      );

    if (isPrimary && currentStudent?.get('primaryStatus', '') === 'present') {
      return (
        <img style={{ marginTop: '-3px' }} src={SuccessImg} alt="Success" />
      );
    }

    const isManualMode = attendanceData?.get('modeBy', '') === 'manual';
    const isAbsentMode = attendanceData?.get('modeBy', '') === 'retake_absent';
    const isPresentStatus =
      attendanceData?.getIn(['students', 'status'], '') === 'present';

    if (attendanceData?.get('students', List()).size > 0) {
      return isManualMode && !isPresentStatus ? (
        <img style={{ marginTop: '-3px' }} src={FailedImg} alt="Failed" />
      ) : (
        <img style={{ marginTop: '-3px' }} src={SuccessImg} alt="Success" />
      );
    }

    return isAbsentMode ? (
      <span style={{ color: '#9CA3AF', fontSize: 14 }}>--</span>
    ) : (
      <img style={{ marginTop: '-3px' }} src={FailedImg} alt="Failed" />
    );
  };

  const getStudentTime = ({ isPrimary, student, index }) => {
    const createdAt =
      student?.get('modeBy') === 'manual'
        ? humanReadableTimeWithoutDate(student.getIn(['students', 'time'], ''))
        : humanReadableTimeWithoutDate(student?.get('createdAt', ''));

    const time =
      index === 0
        ? createdAt
          ? `Latest - ${createdAt}`
          : 'Latest'
        : createdAt;
    const primaryTime = humanReadableTimeWithoutDate(
      schedule.getIn(['students', 0, 'primaryTime'], '')
    );

    return isPrimary
      ? showMoreData.size === 0
        ? `Latest  ${primaryTime}`
        : primaryTime
      : time;
  };

  const getCurrentStudentStatus = () => {
    const currentStudent = schedule
      .get('students', List())
      .find((student) => userId === student.get('_id', ''));
    return currentStudent ? currentStudent.get('status', '') : '';
  };
  const studentStatus = getCurrentStudentStatus();
  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      scroll="paper"
      open={open}
      onClose={handleClose}
    >
      <div className="f-16 px-3 pt-3 pb-2">
        <div className="bold">
          {schedule.get('course_code', '')} - {schedule.get('course_name', '')}
        </div>
        <div>{getTitle()}</div>
      </div>
      <Divider />
      <div className="f-16 px-3 py-2">
        Attendance Status{' '}
        {studentStatus !== '' && (
          <span
            className={`${
              studentStatus === 'absent'
                ? 'student_a_status'
                : 'DiGiBGColorGreen'
            } f-15 ml-1 p-1 rounded`}
          >
            {studentStatus === 'absent' ? 'Absent' : 'Present'}
          </span>
        )}
      </div>
      <DialogContent dividers>
        <TableContainer>
          <Table sx={{ '& td, & th': { borderBottom: 'none' } }}>
            <TableBody>
              {showMoreData.reverse().map((attendanceData, index) => {
                const status = renderStudentStatus({
                  isPrimary: false,
                  attendanceData,
                });
                const time = getStudentTime({
                  isPrimary: false,
                  student: attendanceData,
                  index,
                });
                const isChecked = attendanceData
                  .getIn(['isCompared'], '')
                  .includes(attendanceData.get('_id', ''));

                return (
                  <TableRow key={index}>
                    <TableCell>
                      {getRetakeOptionLabel(attendanceData.get('modeBy', ''))}
                    </TableCell>
                    <TableCell>{status}</TableCell>
                    <TableCell>
                      {status.props.src !== FailedImg
                        ? time
                        : index === 0
                        ? 'Latest'
                        : null}
                    </TableCell>
                    <TableCell>
                      {isChecked && <CheckCircleIcon color="primary" />}
                    </TableCell>
                  </TableRow>
                );
              })}
              <TableRow>
                <TableCell>{getRetakeOptionLabel('primary')}</TableCell>
                <TableCell>
                  {renderStudentStatus({ isPrimary: true })}
                </TableCell>
                <TableCell>{getStudentTime({ isPrimary: true })}</TableCell>
                <TableCell>
                  {(showMoreData.size === 0 ||
                    showMoreData
                      .getIn([0, 'isCompared'], List())
                      .includes(scheduleId)) && (
                    <CheckCircleIcon color="primary" />
                  )}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions className="pb-2">
        <Button variant="contained" color="primary" onClick={handleClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

ShowMoreModal.propTypes = {
  open: PropTypes.bool,
  cancelModal: PropTypes.func,
  schedule: PropTypes.instanceOf(Map),
  authData: PropTypes.instanceOf(Map),
};
