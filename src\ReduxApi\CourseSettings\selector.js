import { List, Map } from 'immutable';
const courseWiseSetting = (state) => state.courseWiseSetting;
const selectIsLoading = (state) => courseWiseSetting(state).get('isLoading');
const selectMessage = (state) => courseWiseSetting(state).get('message');
const selectBreadcrumbs = (state) =>
  courseWiseSetting(state).get('breadcrumbs');
const selectResetBreadcrumbs = (state) =>
  courseWiseSetting(state).get(`breadcrumbs`);
const selectGetCourseWiseData = (state) =>
  courseWiseSetting(state).get('getFacialType', Map());
const selectCourseBasedStaffDetails = (state) =>
  courseWiseSetting(state).get('courseBasedStaffDetails', List());
const selectDeliveryBasedStaff = (state) =>
  courseWiseSetting(state).get('deliveryBasedStaff', List());

export {
  selectIsLoading,
  selectMessage,
  selectResetBreadcrumbs,
  selectBreadcrumbs,
  selectGetCourseWiseData,
  selectCourseBasedStaffDetails,
  selectDeliveryBasedStaff,
};
