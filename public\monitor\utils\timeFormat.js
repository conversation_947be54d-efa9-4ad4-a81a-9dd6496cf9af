$(document).ready(function () {

// Function to format time in 12-hour format with AM/PM
function formatDate(date) {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
}

function formatTime(date) {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    
    hours = hours % 12;
    hours = hours ? hours : 12; 
    
    const minutesStr = minutes < 10 ? '0' + minutes : minutes;
    
    return `${hours}:${minutesStr} ${ampm}`;
}
  
// Function to update date and time
function updateDateTime() {
    const now = new Date();
    const currentDate = formatDate(now);
    const currentTime = formatTime(now);
  
    $('#current-date').text(currentDate);
    $('#current-time').text(currentTime);
}
  
// Call the function initially and then every minute
updateDateTime();
setInterval(updateDateTime, 60000);
  
updateDateTime();
setInterval(updateDateTime, 60000);

})

// Function to convert milliseconds to a readable format (hours, minutes, seconds)
function formatMilliSecondsTime(milliseconds) {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const remainingSeconds = totalSeconds % 60;
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''} ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
}