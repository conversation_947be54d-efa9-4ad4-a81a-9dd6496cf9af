import moment from 'moment';

const useMomentHooks = () => {
  const formatMomentDate = (date, format = 'DD/MM/YYYY') => {
    return moment(date).format(format);
  };

  const formatMomentDateObject = (date, format = 'DD/MM/YYYY') => {
    return moment(date, format);
  };
  const formatMomentDateWithUtc = (date, format = '') => {
    return moment(date).utc().format(format);
  };
  const humanReadableTime = (updatedAt) => {
    let formattedDate;
    const dateThreshold = moment().subtract(24, 'hours');
    if (moment(updatedAt).isAfter(dateThreshold)) {
      formattedDate = moment(updatedAt).fromNow();
    } else {
      formattedDate = moment(updatedAt).format('DD MMM, YYYY - hh:mm a');
    }
    // formattedDate = moment(updatedAt).fromNow();
    return formattedDate;
  };
  const humanReadableTimeWithoutDate = (updatedAt) => {
    return updatedAt ? moment(updatedAt).format('hh:mm a') : '';
    // let formattedDate;
    // const dateThreshold = moment().subtract(24, 'hours');
    // if (moment(updatedAt).isAfter(dateThreshold)) {
    //   formattedDate = moment(updatedAt).fromNow();
    // } else {
    //   formattedDate = moment(updatedAt).format('hh:mm a');
    // }
    // // formattedDate = moment(updatedAt).fromNow();
    // return formattedDate;
  };
  return {
    formatMomentDate,
    formatMomentDateWithUtc,
    humanReadableTime,
    formatMomentDateObject,
    humanReadableTimeWithoutDate,
  };
};

export default useMomentHooks;
