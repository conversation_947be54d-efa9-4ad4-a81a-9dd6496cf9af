$(document).ready(function () {
    // API configuration
    const apiUrl = 'https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/hebaAI/scheduleListDetails';
    const apiKey = 'Digicronfromdomain890731';
    const scheduleDate = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format

    // Fetch data for classes
    fetchData(apiUrl, apiKey, scheduleDate, function (response) {
        console.log('API Response for Classes:', response); // Debugging

        if (response.message === 'success' && response.data) {
            // Process each class session
            response.data.forEach(session => {
                // Convert UTC times to local timezone
                const startTime = convertToLocalTime(session.scheduleStartDateAndTime);
                const endTime = convertToLocalTime(session.scheduleEndDateAndTime);

                // Calculate time difference in minutes for late status
                const differenceInMinutes = calculateTimeDifference(session.sessionDetail.start_time, session.scheduleStartDateAndTime);
                let statusTextDisplay = statusText[session.status];
                let lateByText = '';
                let hasLate = false;

                if (differenceInMinutes > 5) {
                    session.status = 'late';
                    lateByText = `Late by ${Math.round(differenceInMinutes)} mins`;
                    hasLate = true;
                }

                // Apply status styles
                const styles = statusStyles[session.status] || {};

                // Update the UI with session details
                $('#classes-grid').append(`
                    <div class="status-card  ${session.status}" data-status="${session.status}" data-time="${differenceInMinutes}" style="border-left: 10px solid ${styles.borderColor}; box-shadow: ${styles.boxShadow};" onclick="openDetails('${session._id}')">
                        <i class="fas ${icons[session.status]}"></i>
                        <div>
                            <h3>${session.course_name}</h3>
                            <p>${session.staffs[0].staff_name.first} ${session.staffs[0].staff_name.last}</p>
                            <p>${startTime} - ${endTime}</p>
                            <p>Present: ${session.studentAttendance.presentCount} | Absent: ${session.studentAttendance.absentCount}</p>
                            <p><strong>Status: ${statusTextDisplay}</strong></p>
                            ${hasLate ? `<p><strong>${lateByText}</strong></p>` : ''}
                        </div>
                    </div>
                `);
            });
        } else {
            console.error('Error fetching class data:', response);
        }
    }, function (xhr, status, error) {
        console.error('Error fetching class data:', error);
    });

    // Open the sidebar with details
    window.openDetails = function (scheduleId) {
        const detailsUrl = `https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/hebaAI/scheduleWithDetails?scheduleId=${scheduleId}`;
        const apiKey = 'Digicronfromdomain890731';

        $.ajax({
            url: detailsUrl,
            headers: {
                'digicronkey': apiKey
            },
            success: function (response) {
                if (response.message === 'success' && response.data) {
                    const session = response.data;
                    const startTime = convertToLocalTime(session.scheduleStartDateAndTime);
                    const endTime = convertToLocalTime(session.scheduleEndDateAndTime);

                    let presentStudentsHtml = '';
                    let absentStudentsHtml = '';
                    session.students.forEach(student => {
                        const studentHtml = `<li>${student.name.first} ${student.name.last}</li>`;
                        if (student.status === 'present') {
                            presentStudentsHtml += studentHtml;
                        } else if (student.status === 'absent') {
                            absentStudentsHtml += studentHtml;
                        }
                    });

                    $('#details-content').html(`
                        <h3>${session.course_name}</h3>
                        <p><strong>Instructor:</strong> ${session.staffs[0].staff_name.first} ${session.staffs[0].staff_name.last}</p>
                        <p><strong>Timing:</strong> ${startTime} - ${endTime}</p>
                        <p><strong>Present:</strong> ${session.studentAttendance.presentCount}</p>
                        <p><strong>Absent:</strong> ${session.studentAttendance.absentCount}</p>
                        <p><strong>Program:</strong> ${session.program_name}</p>
                        <div class="students-container">
                            <div class="students-list">
                                <h4>Present</h4>
                                <ul>${presentStudentsHtml}</ul>
                            </div>
                            <div class="students-list">
                                <h4>Absent</h4>
                                <ul>${absentStudentsHtml}</ul>
                            </div>
                        </div>
                    `);
                    $('#sidebar').addClass('open');
                } else {
                    console.error('Error fetching session details:', response);
                }
            },
            error: function (xhr, status, error) {
                console.error('Error fetching session details:', error);
            }
        });
    };

    // Close the sidebar when clicking outside of it
    $(document).click(function (event) {
        if (!$(event.target).closest('.sidebar, .status-card').length) {
            closeSidebar();
        }
    });

    // Close the sidebar function
    window.closeSidebar = function () {
        $('#sidebar').removeClass('open');
    };

    // Open the settings sidebar
    window.openSettings = function () {
        $('#settings-sidebar').addClass('open');
    };

    // Close the settings sidebar
    window.closeSettings = function () {
        $('#settings-sidebar').removeClass('open');
    };

    // Apply class filter based on settings
    window.applyClassFilter = function () {
        const selectedStatuses = $('.class-filter:checked').map(function () {
            return this.value;
        }).get();

        const selectedTimeFilter = $('input[name="time-filter"]:checked').val();

        // Filter logic for classes
        $('.status-card').each(function () {
            const status = $(this).data('status');
            const time = $(this).data('time');

            let timeCondition = true;
            if (selectedTimeFilter) {
                if (selectedTimeFilter === 'late' && time <= 5) {
                    timeCondition = false;
                } else if (selectedTimeFilter === 'on-time' && time > 5) {
                    timeCondition = false;
                }
            }

            if (selectedStatuses.includes(status) && timeCondition) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        closeSettings();
    };
});
