<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Verification</title>
    <link
      rel="stylesheet"
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
    />
    <script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="./js/face-api.js"></script>
    <script src="./utils.js"></script>
    <style>
      #camera-container {
        position: relative; /* Make the container relative for absolute positioning of children */
        width: 500px;
        height: 375px;
        margin: auto;
      }

      /* #camera {
        width: 100%;
        height: 100%;
        transform: scaleX(-1);
      } */
      #overlay {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none; /* Prevent canvas from blocking interactions with other elements */
      }
      .preview-box {
        width: 100px;
        height: 100px;
        margin: 5px;
        overflow: hidden;
        border: 2px solid #ddd;
      }
      .preview-box img {
        width: 100%;
        height: 100px;
      }
      .oval-image {
        position: absolute;
        top: 0; /* Position it at the top of the container */
        left: 0;
        width: 100%;
        height: 100%; /* Make it fit the container */
        object-fit: contain; /* Adjust image scaling */
        /* z-index: 1; */
      }
    </style>
  </head>
  <body>
    <div class="container text-center mt-1">
      <p>Follow instructions to capture the required face positions.</p>
      <p id="loginCheck" style="display: none">Please login to use this</p>
      <p>
        <select id="userSelect">
          <option value="">Select a user</option>
        </select>
      </p>
      <div id="camera-container" class="mb-3" style="position: relative">
        <img src="./images/oval.png" alt="Oval shape" class="oval-image" />
        <video id="camera" width="500" height="375" autoplay muted></video>
        <canvas id="overlay"></canvas>
      </div>

      <button
        class="btn btn-primary"
        id="startCaptureBtn"
        onclick="startCapture()"
        style="display: none"
      >
        Start Capture
      </button>
      <button
        class="btn btn-primary"
        id="verifyCaptureBtn"
        onclick="verifyCapture()"
        style="display: none"
      >
        Verify Capture
      </button>
      <p id="instruction">Position 1: Please look straight at the camera</p>
      <p id="msg"></p>
      <p id="detection" class="mt-2"></p>
      <div id="preview" class="d-flex justify-content-center mt-1">
        <div class="preview-box" id="img1">
          <img src="./images/straight.svg" alt="Straight Image" />
        </div>
        <div class="preview-box" id="img2">
          <img src="./images/left.svg" alt="Left Image" />
        </div>
        <div class="preview-box" id="img3">
          <img src="./images/right.svg" alt="Right Image" />
        </div>
        <div class="preview-box" id="img4">
          <img src="./images/up.svg" alt="Up Image" />
        </div>
      </div>
      <!-- <audio
        id="instruction0"
        src="./audio/instruction0.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction1"
        src="./audio/instruction1.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction2"
        src="./audio/instruction2.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction3"
        src="./audio/instruction3.mp3"
        preload="auto"
      ></audio>
      <audio
        id="instruction4"
        src="./audio/instruction4.mp3"
        preload="auto"
      ></audio> -->
    </div>

    <script type="module">
      // import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
      // const { FaceLandmarker, FilesetResolver } = vision;
      const SIMILARITY_THRESHOLD = 0.47; // Define an appropriate threshold based on testing
      // let faceLandmarker;
      const token = getCookie('DC-access_token');
      let video = document.getElementById('camera');
      const overlay = document.getElementById('overlay');
      let currentStep = 1;
      let isModelLoaded = false;
      let straightFaceDescriptor = null;
      const minConfidence = 0.7;
      const instructions = [
        'Position 1: Please look straight at the camera',
        'Position 2: Please turn to your left',
        'Position 3: Please turn to your right',
        'Position 4: Please look straight up',
      ];
      const descriptors = [];

      const audioCache = {};

      function preloadAudio(audioPath) {
        const audio = new Audio(audioPath);
        audio.preload = 'auto'; // Preload the audio file
        audioCache[audioPath] = audio; // Cache it for later use
        console.log(`Preloaded: ${audioPath}`);
      }

      // Initialize MediaPipe FaceLandmarker
      // async function initializeFaceLandmarker() {
      // 	const filesetResolver = await FilesetResolver.forVisionTasks(
      // 		'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
      // 	);
      // 	faceLandmarker = await FaceLandmarker.createFromOptions(
      // 		filesetResolver,
      // 		{
      // 			baseOptions: {
      // 				modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
      // 			},
      // 			runningMode: 'video',
      // 			numFaces: 1,
      // 		}
      // 	);
      // }

      async function startVideo() {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        video.srcObject = stream;
      }

      function updateInstruction() {
        if (currentStep <= instructions.length) {
          document.getElementById('instruction').innerText =
            instructions[currentStep - 1];
        } else {
          document.getElementById('instruction').innerText =
            'All photos captured!';
          registerFacial();
        }
      }

      // function playAudioById(elementId) {
      //   const audio = document.getElementById(elementId);
      //   if (audio) {
      //     audio
      //       .play()
      //       .catch((err) => console.error('Error playing audio:', err));
      //     console.log('Audio started');
      //   } else {
      //     console.error('Audio element not found:', elementId);
      //   }
      // }

      function playPreloadedAudio(audioPath) {
        const audio = audioCache[audioPath];
        if (audio) {
          // Wait for the audio to be ready
          // const audio = new Audio("audio1.mp3");
          audio.addEventListener('canplaythrough', () =>
            console.log('Audio is ready')
          );
          audio
            .play()
            .catch((err) => console.error('Error playing audio:', err));
        } else {
          console.error('Audio not preloaded:', audioPath);
        }
      }

      async function capturePosition(orientation) {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const context = canvas.getContext('2d');
        context.filter = 'brightness(1.2) contrast(1.2)';
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        const detections = await faceapi
          .detectSingleFace(
            video,
            new faceapi.SsdMobilenetv1Options({ minConfidence })
          )
          .withFaceLandmarks()
          .withFaceDescriptor();

        // const detectionsFace = await faceLandmarker.detectForVideo(
        // 	video,
        // 	performance.now()
        // );
        // console.log({ detections });

        if (
          detections &&
          detections.landmarks &&
          detections.landmarks._positions.length > 0 &&
          detections.detection &&
          detections.detection.score > 0.8
          // &&
          // detectionsFace &&
          // detectionsFace.faceLandmarks.length > 0
        ) {
          const box = detections.detection.box; // Bounding box of the face
          const landmarks = detections.landmarks; // 68-point landmarks
          const detectedOrientation = getNewHeaderPosition(landmarks);
          // console.log({ detectedOrientation });
          if (detectedOrientation === orientation) {
            // Extract dimensions from the box
            let { x: minX, y: minY, width: boxWidth, height: boxHeight } = box;

            // Add padding to the bounding box
            const padding = 0.4; // Adjust padding as needed (e.g., 40%)
            const paddedWidth = boxWidth * (1 + padding);
            const paddedHeight = boxHeight * (1 + padding);

            // Adjust `minX` and `minY` to apply padding
            minX -= (paddedWidth - boxWidth) / 2;
            minY -= (paddedHeight - boxHeight) / 2;

            // Ensure the boundaries stay within the canvas dimensions
            minX = Math.max(0, minX);
            minY = Math.max(0, minY);
            const maxWidth = Math.min(canvas.width - minX, paddedWidth);
            const maxHeight = Math.min(canvas.height - minY, paddedHeight);

            // Create a new canvas for the cropped face
            const croppedCanvas = document.createElement('canvas');
            croppedCanvas.width = maxWidth;
            croppedCanvas.height = maxHeight;

            // Draw the cropped face onto the new canvas
            const croppedCtx = croppedCanvas.getContext('2d');
            croppedCtx.drawImage(
              canvas, // Source canvas
              minX, // Source x
              minY, // Source y
              maxWidth, // Source width
              maxHeight, // Source height
              0, // Destination x
              0, // Destination y
              maxWidth, // Destination width
              maxHeight // Destination height
            );

            const croppedImage = new Image();
            croppedImage.src = croppedCanvas.toDataURL();
            await croppedImage.decode();
            if (
              detectedOrientation === 'Head straight' &&
              orientation === 'Head straight'
            ) {
              straightFaceDescriptor = detections?.descriptor
                ? detections?.descriptor
                : null;
            }
            let distance = 0;
            if (
              detectedOrientation !== 'Head straight' &&
              orientation !== 'Head straight' &&
              straightFaceDescriptor !== null
            ) {
              distance = await faceapi.euclideanDistance(
                detections?.descriptor,
                straightFaceDescriptor
              );
            }
            // console.log({ detections, straightFaceDescriptor, distance });
            if (
              orientation === 'Head straight' ||
              (orientation !== 'Head straight' && distance <= 0.5)
            ) {
              descriptors.push([...detections?.descriptor]);
              document.getElementById('detection').innerText = ``;
              document.getElementById(
                `img${currentStep}`
              ).innerHTML = `<img src="${croppedImage.src}" alt="Captured Image">`;
              currentStep++;
              updateInstruction();
            }
            countdownAndCapture(); // Move to the next position countdown
          } else {
            // Retry if orientation does not match
            setTimeout(() => capturePosition(orientation), 1000);
            // document.getElementById('detection').innerText =
            //   `Detection Orientation : ` +
            //   detectedOrientation +
            //   `. Make sure to Correct Orientation : ` +
            //   orientation;
          }
        } else {
          // Retry if no detections were found
          // console.log('No face detected, retrying...');
          setTimeout(() => capturePosition(orientation), 1000);
        }
      }

      function countdownAndCapture() {
        let countdown = 3;
        const countdownInterval = setInterval(() => {
          if (countdown > 0) {
            if (currentStep > 4) {
              clearInterval(countdownInterval);
              // document.getElementById('instruction').innerText = ``;
              // endCapture();
              return;
            }
            document.getElementById(
              'msg'
            ).innerText = `Capturing in ${countdown}...`;
            countdown--;
          } else {
            clearInterval(countdownInterval);
            // console.log({ countdownInterval, currentStep });
            const findStep = [
              'Head straight',
              'Head turned left',
              'Head turned right',
              'Head tilted up',
            ][currentStep - 1];
            if (findStep) {
              // speakInstruction();
              // playPreRecordedAudio(`./audio/instruction${currentStep}.mp3`);
              // playAudioById(`instruction${currentStep}`);
              playPreloadedAudio(`./audio/instruction${currentStep}.mp3`);
              capturePosition(findStep);
            } else {
              currentStep = 1;
              clearInterval(countdownInterval);
              // document.getElementById('instruction').innerText = ``;
              // endCapture();
              return;
            }
          }
        }, 200);
      }

      function startCapture() {
        currentStep = 1;
        updateInstruction();
        countdownAndCapture();
        document.getElementById('startCaptureBtn').style.display = 'none';
        document.getElementById('verifyCaptureBtn').style.display = 'none';
      }

      function endCapture() {
        document.getElementById('startCaptureBtn').style.display =
          'inline-block';
        document.getElementById('verifyCaptureBtn').style.display =
          'inline-block';
      }

      function dString(name) {
        if (name !== '') {
          return window.atob(name);
        }
        return '';
      }

      function dataURLtoFile(dataurl, filename) {
        var arr = dataurl.split(','),
          mime = arr[0].match(/:(.*?);/)[1],
          bstr = dString(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      }

      function getCookie(name) {
        // Create a regular expression to find the cookie by name
        const cookieName = name + '=';
        const decodedCookie = decodeURIComponent(document.cookie); // Decode cookie value
        const cookieArray = decodedCookie.split(';'); // Split cookies into an array

        // Loop through all cookies
        for (let i = 0; i < cookieArray.length; i++) {
          let cookie = cookieArray[i].trim(); // Trim spaces
          // If cookie name matches, return its value
          if (cookie.indexOf(cookieName) === 0) {
            return cookie.substring(cookieName.length, cookie.length);
          }
        }
        // Return null if cookie not found
        return null;
      }

      function registerFacial() {
        const centerImgElement = document.querySelector('#img1 img');
        const centerImgSrc = centerImgElement.getAttribute('src');

        const leftImgElement = document.querySelector('#img2 img');
        const leftImgSrc = leftImgElement.getAttribute('src');

        const rightImgElement = document.querySelector('#img3 img');
        const rightImgSrc = rightImgElement.getAttribute('src');

        const upImgElement = document.querySelector('#img4 img');
        const upImgSrc = upImgElement.getAttribute('src');

        const center = dataURLtoFile(centerImgSrc, 'center.png');
        const left = dataURLtoFile(leftImgSrc, 'left.png');
        const right = dataURLtoFile(rightImgSrc, 'right.png');
        const up = dataURLtoFile(upImgSrc, 'up.png');

        let formData = new FormData();
        formData.append('userId', localStorage.getItem('biometricUserId'));
        formData.append('center', center);
        formData.append('left', left);
        formData.append('right', right);
        formData.append('up', up);
        formData.append(
          'faceDescriptors',
          JSON.stringify([
            {
              face: 'center',
              descriptor: descriptors[0],
            },
            {
              face: 'right',
              descriptor: descriptors[2],
            },
            {
              face: 'left',
              descriptor: descriptors[1],
            },
            {
              face: 'up',
              descriptor: descriptors[3],
            },
          ])
        );
        document.getElementById('msg').innerText = 'Face Data Saving...';
        const URL =
          'https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/user/userBiometricRegister';
        $.ajax({
          type: 'POST',
          url: URL,
          data: formData,
          cache: false,
          processData: false,
          contentType: false,
          headers: {
            'Access-Control-Allow-Origin': '*',
            authorization: 'Bearer ' + token,
          },
          success: function (data) {
            endCapture();
            document.getElementById('instruction').innerText =
              'Face Registeration Successfull.';
            document.getElementById('msg').innerText = '';
          },
          error: function () {
            console.log('facial request failed');
            document.getElementById('instruction').innerText =
              'Facial Request Failed.';
            document.getElementById('msg').innerText = '';
          },
        });
      }

      async function loadLibraries() {
        const MODEL_URL = 'models';
        document.getElementById(
          'msg'
        ).innerText = `Loading Face Models. Please wait...`;
        await faceapi.loadSsdMobilenetv1Model(MODEL_URL);
        await faceapi.loadFaceLandmarkModel(MODEL_URL);
        await faceapi.loadFaceRecognitionModel(MODEL_URL);
        //  await faceapi.loadFaceLandmarkTinyModel(MODEL_URL);
        // await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
        console.log('load facial Libraries done');
        isModelLoaded = true;
        document.getElementById('msg').innerText = ``;
      }

      async function detectFaces() {
        if (!isModelLoaded) return;

        const displaySize = { width: video.width, height: video.height };
        faceapi.matchDimensions(overlay, displaySize);
        // speakInstruction('sit straight and get ready for face registration.');
        setInterval(async () => {
          const detections = await faceapi
            .detectSingleFace(
              video,
              new faceapi.SsdMobilenetv1Options({ minConfidence })
            )
            .withFaceLandmarks();

          // Clear previous drawings
          const context = overlay.getContext('2d');
          context.clearRect(0, 0, overlay.width, overlay.height);

          // Resize and draw the detected boxes
          if (detections) {
            const resizedDetections = faceapi.resizeResults(
              detections,
              displaySize
            );
            faceapi.draw.drawDetections(overlay, resizedDetections);
            faceapi.draw.drawFaceLandmarks(overlay, resizedDetections);

            const landmarks = detections.landmarks;
            const direction = getNewHeaderPosition(landmarks);
            const instructionPosition =
              instructions[currentStep - 1] !== undefined
                ? instructions[currentStep - 1]
                : '';
            context.font = '18px Arial';
            let brightnessHtml = '';

            if (detections.detection.score < 0.8) {
              brightnessHtml =
                'Face detection confidence is low. Please adjust lighting or move closer to the camera.';
            }
            // const brightness = calculateBrightness(overlay);
            // if (brightness > 200) {
            //   brightnessHtml =
            //     'Background light is too strong. Please adjust lighting conditions.';
            // } else if (brightness < 50) {
            //   brightnessHtml = 'Lighting is too dim. Please increase lighting.';
            // }

            const lines =
              `${instructionPosition}\n                      ${direction}\n${brightnessHtml}`.split(
                '\n'
              ); // Split text into lines
            const x = resizedDetections.detection.box.x - 100;
            let y = resizedDetections.detection.box.y - 60;

            lines.forEach((line, i) => {
              context.fillText(line, x, y);
              context.fillStyle = i != 0 ? 'white' : 'red';
              y += 35; // Adjust this value based on your desired line spacing
            });
            document.getElementById('detection').innerText = '';
            // context.fillText(
            //   `${direction} \n ${direction}`,
            //   resizedDetections.detection.box.x + 50,
            //   resizedDetections.detection.box.y - 5
            // );
          } else {
            document.getElementById('detection').innerText =
              'Face detection confidence is low. Please adjust lighting or move closer to the camera.';
          }
        }, 1000); // Detect every 100ms
      }

      // Thresholds (normalized values)
      const horizontalThreshold = 0.1;
      const EAR_THRESHOLD = 0.25;
      let baselineEyeToChin;
      let frameCount = 0;
      function getNewHeaderPosition(landmarks) {
        const leftEye = landmarks.getLeftEye();
        const rightEye = landmarks.getRightEye();
        const nose = landmarks.getNose();
        const chin = landmarks.getJawOutline().pop(); // Last point in jaw outline
        const leftEyeMid = {
          x: (leftEye[0].x + leftEye[3].x) / 2,
          y: (leftEye[0].y + leftEye[3].y) / 2,
        };
        const rightEyeMid = {
          x: (rightEye[0].x + rightEye[3].x) / 2,
          y: (rightEye[0].y + rightEye[3].y) / 2,
        };

        // Calculate the key distances
        const noseTipX = nose[0].x; // Nose tip
        const leftEyeMidX = (leftEye[0].x + leftEye[3].x) / 2; // Midpoint of left eye
        const rightEyeMidX = (rightEye[0].x + rightEye[3].x) / 2; // Midpoint of right eye
        const eyeMidDistance = Math.abs(leftEyeMidX - rightEyeMidX);
        // Example: Horizontal turn detection
        const noseShift = noseTipX - (leftEyeMidX + rightEyeMidX) / 2;
        // if (noseShift > SIMILARITY_THRESHOLD) {
        // 	console.log('Head turned left');
        // } else if (noseShift < -SIMILARITY_THRESHOLD) {
        // 	console.log('Head turned right');
        // }

        // Eye-to-chin distance
        const eyeMidY = (leftEyeMid.y + rightEyeMid.y) / 2;
        const eyeToChinDistance = chin.y - eyeMidY;

        // Baseline
        if (baselineEyeToChin === undefined) {
          baselineEyeToChin = eyeToChinDistance; // Set baseline for the first time
        } // Set once when straight-facing

        // Angle calculation
        const deltaY = rightEyeMid.y - leftEyeMid.y;
        const deltaX = rightEyeMid.x - leftEyeMid.x;
        const slope = deltaY / deltaX;
        const angle = Math.atan(slope) * (180 / Math.PI); // Convert to degrees

        // Normalize distances
        const normalizedNoseShift = noseShift / eyeMidDistance;
        const eyeToChinRatio = eyeToChinDistance / eyeMidDistance;
        let headDirection = '';
        // Calculate EAR for both eyes

        const eyesClosed = checkEyesClosed(leftEye, rightEye, frameCount);

        if (normalizedNoseShift > horizontalThreshold) {
          if (eyesClosed) {
            frameCount++;
            return '';
          }
          headDirection = 'Head turned left';
        } else if (normalizedNoseShift < -horizontalThreshold) {
          if (eyesClosed) {
            frameCount++;
            return '';
          }
          headDirection = 'Head turned right';
        } else {
          if (eyesClosed && currentStep == 1) {
            frameCount++;
            return '';
          }
          headDirection = 'Head straight';
        }

        // Check vertical position
        if (eyeToChinRatio > 0.25) {
          headDirection = 'Head tilted up';
        }
        // else if (eyeToChinRatio < 0.1) {
        // 	headDirection = 'Head tilted down';
        // }

        return headDirection;
      }

      let baselineEAR = null; // To store the baseline EAR

      function calculateEAR(eye) {
        const vertical1 = Math.hypot(eye[1].x - eye[5].x, eye[1].y - eye[5].y);
        const vertical2 = Math.hypot(eye[2].x - eye[4].x, eye[2].y - eye[4].y);
        const horizontal = Math.hypot(eye[0].x - eye[3].x, eye[0].y - eye[3].y);

        return (vertical1 + vertical2) / (2.0 * horizontal);
      }

      function checkEyesClosed(leftEye, rightEye, frameCount) {
        const leftEAR = calculateEAR(leftEye);
        const rightEAR = calculateEAR(rightEye);
        const avgEAR = (leftEAR + rightEAR) / 2;
        if (baselineEAR === null || frameCount == 3) {
          baselineEAR = avgEAR;
          frameCount = 0;
          baselineEAR = null;
        }
        if (leftEAR < baselineEAR && rightEAR < baselineEAR) {
          return true;
        }
        return false;
      }

      video.addEventListener('play', detectFaces);

      // Initialize everything when the page loads
      window.onload = async () => {
        // await initializeFaceLandmarker();
        await loadLibraries();
        await startVideo();
        console.log('FaceLandmarker initialized and video started');
        const userSelect = document.getElementById('userSelect');
        // playPreRecordedAudio('./audio/instruction0.mp3');
        // playAudioById('instruction0');
        localStorage.removeItem('biometricUserId');
        localStorage.removeItem('biometricUserName');
        preloadAudio('./audio/instruction0.mp3');
        preloadAudio('./audio/instruction1.mp3');
        preloadAudio('./audio/instruction2.mp3');
        preloadAudio('./audio/instruction3.mp3');
        preloadAudio('./audio/instruction4.mp3');

        setTimeout(() => {
          playPreloadedAudio(`./audio/instruction0.mp3`);
        }, 3000);
      };
      let userId = '';
      let name = '';
      userSelect.addEventListener('change', (event) => {
        userId = event.target.value;

        if (event.target.value !== '') {
          const optionElement = document.querySelector(
            `option[value="${userId}"]`
          );
          name = optionElement.id;
          localStorage.setItem('biometricUserId', userId);
          localStorage.setItem('biometricUserName', name);
          document.getElementById('startCaptureBtn').style.display =
            'inline-block';
          document.getElementById('verifyCaptureBtn').style.display =
            'inline-block';
        } else {
          localStorage.removeItem('biometricUserId');
          localStorage.removeItem('biometricUserName');
          document.getElementById('startCaptureBtn').style.display = 'none';
          document.getElementById('verifyCaptureBtn').style.display = 'none';
        }
        // console.log('Selected User ID:', event.target.value);
      });

      function verifyCapture() {
        if (userId !== '') {
          window.location = 'verify.html';
        } else {
          alert('Select User');
        }
      }

      // Populate the select options
      usersList.forEach((user) => {
        const option = document.createElement('option');
        option.value = user.id; // Set the value to the user ID
        option.id = user.name;
        option.textContent = user.name + ' - ' + user.staffName; // Set the displayed text to the user name
        userSelect.appendChild(option);
      });

      if (token == null) {
        document.getElementById('loginCheck').style.display = 'inline-block';
        document.getElementById('userSelect').style.display = 'none';
      }
      window.startCapture = startCapture;
      window.verifyCapture = verifyCapture;
    </script>
  </body>
</html>
