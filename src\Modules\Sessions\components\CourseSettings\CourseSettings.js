import React, { useState, useEffect, useCallback } from 'react';
import {
  Avatar,
  Paper,
  Stack,
  Switch,
  TextField,
  Box,
  Typography,
  Chip,
} from '@mui/material';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import {
  updateCourseBasedFacial,
  getCourseBasedFacial,
} from 'ReduxApi/CourseSettings/action';
import { selectGetCourseWiseData } from 'ReduxApi/CourseSettings/selector';

// Blue backgrounds for avatars
const avatarStyles = {
  staffFacial: {
    background: 'linear-gradient(135deg, #0289ff 0%, #45a8ff 100%)',
    width: '56px',
    height: '56px',
    fontSize: '18px',
    fontWeight: 'bold',
    color: '#fff',
    boxShadow: '0 4px 12px rgba(33, 150, 243, 0.15)',
  },
  studentFacial: {
    background: 'linear-gradient(135deg, #0289ff 0%, #45a8ff 100%)',
    width: '56px',
    height: '56px',
    fontSize: '18px',
    fontWeight: 'bold',
    color: '#fff',
    boxShadow: '0 4px 12px rgba(33, 150, 243, 0.15)',
  },
  sessionChange: {
    background: 'linear-gradient(135deg, #0289ff 0%, #45a8ff 100%)',
    width: '56px',
    height: '56px',
    fontSize: '18px',
    fontWeight: 'bold',
    color: '#fff',
    boxShadow: '0 4px 12px rgba(33, 150, 243, 0.15)',
  },
  autoEndAttendance: {
    background: 'linear-gradient(135deg, #0289ff 0%, #45a8ff 100%)',
    width: '56px',
    height: '56px',
    fontSize: '18px',
    fontWeight: 'bold',
    color: '#fff',
    boxShadow: '0 4px 12px rgba(33, 150, 243, 0.15)',
  },
};

const CourseSettings = ({
  courseId,
  programId,
  term,
  year,
  level,
  rotation,
  rotationCount,
}) => {
  const dispatch = useDispatch();
  const courseWiseData = useSelector(selectGetCourseWiseData);
  const [facialSettings, setFacialSettings] = useState(
    Map({
      staffFacial: false,
      studentFacial: false,
      sessionChange: false,
      autoEndAttendance: 5,
    })
  );
  const [error, setError] = useState('');

  useEffect(() => {
    const params = {
      courseId: courseId || '',
      programId: programId || '',
    };
    dispatch(getCourseBasedFacial(params));
  }, [courseId, programId, dispatch]);

  // Load initial data from API response
  useEffect(() => {
    if (courseWiseData && courseWiseData.size > 0) {
      const courseData = courseWiseData.get(0); // Get first item from the array
      if (courseData) {
        // Handle new nested structure: data.settings contains the actual settings
        const settings = courseData.get('settings', Map());
        console.log('📊 Course Settings Data:', {
          courseData: courseData.toJS(),
          settings: settings.toJS(),
        });

        setFacialSettings(
          Map({
            staffFacial: settings.get('staffFacial', false),
            studentFacial: settings.get('studentFacial', false),
            sessionChange: settings.get('sessionChange', false),
            autoEndAttendance: settings.get('autoEndAttendance', 5),
          })
        );
      }
    }
  }, [courseWiseData]);

  const SwitchApiStaffFacial = (e, facialType) => {
    // Handle autoEndAttendance validation separately
    if (facialType === 'autoEndAttendance') {
      const value = e.target.value;

      // Update state immediately for text field
      setFacialSettings((prevSettings) => prevSettings.set(facialType, value));

      // Validate numeric input
      if (!/^\d*$/.test(value)) {
        return;
      }

      // Validate range
      if (value === '') {
        setError('');
      } else {
        const numValue = parseInt(value, 10);
        if (numValue < 5) {
          setError('Minimum value is 5');
        } else if (numValue > 15) {
          setError('Maximum value is 15');
        } else {
          setError('');
        }
      }
    } else {
      // Handle switch inputs (staffFacial, studentFacial, autoAssignSession)
      setFacialSettings((prevSettings) =>
        prevSettings.set(facialType, e.target.checked)
      );
    }

    const params = {
      programId: programId || '',
      courseId: courseId || '',
      // term: term || '',
      // year: year || '',
      // level: level || '',
      // ...(rotation === true && { rotationCount }),
      studentFacial:
        facialType === 'studentFacial'
          ? e.target.checked
          : facialSettings.get('studentFacial', false),
      staffFacial:
        facialType === 'staffFacial'
          ? e.target.checked
          : facialSettings.get('staffFacial', false),
      sessionChange:
        facialType === 'sessionChange'
          ? e.target.checked
          : facialSettings.get('sessionChange', false),
    };

    // Only add AutoEndAttendance if value is between 5-15
    const autoEndValue =
      facialType === 'autoEndAttendance'
        ? e.target.value
        : facialSettings.get('autoEndAttendance', '');

    if (
      autoEndValue &&
      parseInt(autoEndValue) >= 5 &&
      parseInt(autoEndValue) <= 15
    ) {
      params.autoEndAttendance = autoEndValue;
    }

    dispatch(updateCourseBasedFacial(params));
  };

  const getFacialSetting = (facialType) => {
    return facialSettings.get(facialType, false);
  };

  const renderSettingCard = (
    type,
    title,
    description,
    avatarText,
    avatarStyle
  ) => (
    <Paper
      elevation={0}
      sx={{
        borderRadius: '16px',
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        border: '1px solid #e2e8f0',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
          border: '1px solid #cbd5e1',
        },
      }}
      className="mb-4"
    >
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
          <Avatar sx={avatarStyle} variant="rounded">
            {avatarText}
          </Avatar>

          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: '#1e293b',
                mb: 0.5,
                fontSize: '1.1rem',
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#64748b',
                lineHeight: 1.5,
                fontSize: '0.9rem',
              }}
            >
              {description}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {type === 'autoEndAttendance' ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TextField
                  type="number"
                  value={getFacialSetting('autoEndAttendance')}
                  onChange={(e) => SwitchApiStaffFacial(e, 'autoEndAttendance')}
                  placeholder="5-15"
                  size="small"
                  error={!!error}
                  helperText={error}
                  inputProps={{
                    min: 5,
                    max: 15,
                    pattern: '[0-9]*',
                    inputMode: 'numeric',
                    style: {
                      textAlign: 'center',
                      fontWeight: 600,
                      fontSize: '1rem',
                    },
                  }}
                  onKeyPress={(e) => {
                    if (
                      !/[0-9]/.test(e.key) &&
                      e.key !== 'Backspace' &&
                      e.key !== 'Delete' &&
                      e.key !== 'Tab' &&
                      e.key !== 'Enter'
                    ) {
                      e.preventDefault();
                    }
                  }}
                  sx={{
                    width: '80px',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      backgroundColor: '#f8fafc',
                      '&:hover': {
                        backgroundColor: '#f1f5f9',
                      },
                      '&.Mui-focused': {
                        backgroundColor: '#ffffff',
                        boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.2)',
                      },
                    },
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{
                    color: '#64748b',
                    fontWeight: 500,
                    minWidth: '40px',
                  }}
                >
                  mins
                </Typography>
              </Box>
            ) : (
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  label="Off"
                  size="small"
                  sx={{
                    backgroundColor: '#e3f0fc',
                    color: '#1976d2',
                    fontWeight: 500,
                    fontSize: '0.75rem',
                  }}
                />
                <Switch
                  checked={getFacialSetting(type)}
                  onChange={(e) => SwitchApiStaffFacial(e, type)}
                  sx={{
                    '& .MuiSwitch-switchBase.Mui-checked': {
                      color: '#1976d2',
                      '&:hover': {
                        backgroundColor: 'rgba(33, 150, 243, 0.08)',
                      },
                    },
                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                      backgroundColor: '#1976d2',
                    },
                  }}
                />
                <Chip
                  label="On"
                  size="small"
                  sx={{
                    backgroundColor: '#1976d2',
                    color: '#fff',
                    fontWeight: 500,
                    fontSize: '0.75rem',
                  }}
                />
              </Stack>
            )}
          </Box>
        </Box>
      </Box>
    </Paper>
  );

  return (
    <Box
      sx={{
        paddingTop: '2rem',
        minHeight: '100vh',
        backgroundColor: '#f8fafc',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          background:
            'linear-gradient(90deg,rgba(250, 250, 250, 1) 0%, rgba(201, 228, 255, 1) 100%, rgba(2, 137, 255, 1) 49%);',
          pt: 4,
          pb: 2,
          px: 4,
          borderRadius: '0 0 20px 20px',
          mb: 4,
          boxShadow: '0 4px 20px rgba(33, 150, 243, 0.15)',
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontSize: '20px',
            textShadow: '0 2px 4px rgba(0,0,0,0.08)',
            color: 'black',
            fontWeight: 'bold',
          }}
        >
          Course Settings
        </Typography>
        <Typography
          variant="body1"
          sx={{
            opacity: 0.9,
            fontSize: '1rem',
            color: 'black',
          }}
        >
          Configure facial recognition and session management preferences
        </Typography>
      </Box>

      {/* Settings Container */}
      <Box sx={{ maxWidth: '900px', mx: 'auto', px: 3 }}>
        {renderSettingCard(
          'staffFacial',
          'Staff Facial Recognition',
          'Enable or disable face verification for staff members during sessions',
          'SF',
          avatarStyles.staffFacial
        )}

        {renderSettingCard(
          'studentFacial',
          'Student Facial Recognition',
          'Enable or disable face verification for students during sessions',
          'ST',
          avatarStyles.studentFacial
        )}
        {renderSettingCard(
          'sessionChange',
          'Session Status Change',
          'Are scheduled staff able to change missed sessions to completed sessions?',
          'SS',
          avatarStyles.sessionChange
        )}

        {renderSettingCard(
          'autoEndAttendance',
          'Auto-End Attendance Timer',
          'Automatically end attendance tracking after specified minutes',
          'AE',
          avatarStyles.autoEndAttendance
        )}
      </Box>
    </Box>
  );
};

export default CourseSettings;
CourseSettings.propTypes = {
  programId: PropTypes.string,
  courseId: PropTypes.string,
  term: PropTypes.string,
  year: PropTypes.string,
  level: PropTypes.string,
  rotation: PropTypes.string,
  rotationCount: PropTypes.string,
};
