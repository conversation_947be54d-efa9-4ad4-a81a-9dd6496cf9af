// Render sessions
function renderSessions(response, baseUrl, headers) {
  if (response.message === "success" && response.data) {
    $("#classes-grid").empty();
    $("#no-sessions-grid").hide();

    if (response.data.length > 0) {
      const currentTime = new Date();

      // Add sorting logic
      const sortedSessions = response.data.sort((a, b) => {
        const startA = new Date(a.scheduleStartDateAndTime);
        const endA = new Date(a.scheduleEndDateAndTime);
        const startB = new Date(b.scheduleStartDateAndTime);
        const endB = new Date(b.scheduleEndDateAndTime);

        const isCurrentInA = currentTime >= startA && currentTime <= endA;
        const isCurrentInB = currentTime >= startB && currentTime <= endB;

        // Prioritize sessions where current time falls within start and end time
        if (isCurrentInA && !isCurrentInB) return -1;
        if (!isCurrentInA && isCurrentInB) return 1;

        return 0;
      });

      sortedSessions.forEach((session) => {
        const startTime = convertToLocalTime(session.scheduleStartDateAndTime);
        const endTime = convertToLocalTime(session.scheduleEndDateAndTime);
        const differenceInMinutes = calculateTimeDifference(
          session.sessionDetail.start_time,
          session.scheduleStartDateAndTime
        );
        let statusTextDisplay = statusText[session.status];
        let lateByText = "";
        let hasLate = false;

        if (differenceInMinutes > 5) {
          session.status = "late";
          lateByText = `Late by ${Math.round(differenceInMinutes)} mins`;
          hasLate = true;
        }

        const styles = statusStyles[session.status] || {};

        // Process student groups
        let groupText = "";
        
        if (session.student_groups && session.student_groups.length > 0) {
          session.student_groups.forEach((mainGroup, index) => {
              if (mainGroup.group_name) {
                  if (index > 0) {
                      groupText += " , ";
                  }

                  // Add main group name
                  if (mainGroup.group_name.length <= 4) {
                      groupText += mainGroup.group_name;
                  } else {
                      const dashes = mainGroup.group_name.split('-');
                      if (dashes.length >= 2) {
                          const lastSegments = dashes.slice(-2);
                          groupText += lastSegments.join('-');
                      } else {
                          groupText += mainGroup.group_name;
                      }
                  }

                  // Add subgroups for this main group
                  let subGroups = [];
                  if (mainGroup.session_group && mainGroup.session_group.length > 0) {
                      mainGroup.session_group.forEach(sessionGroup => {
                          if (sessionGroup.group_name) {
                              const dashes = sessionGroup.group_name.split('-');
                              if (dashes.length >= 3) {
                                  const lastSegments = dashes.slice(-3);
                                  const groupId = lastSegments.join('-');
                                  if (!subGroups.includes(groupId)) {
                                      subGroups.push(groupId);
                                  }
                              }
                          }
                      });
                      
                      if (subGroups.length > 0) {
                          groupText += " - " + subGroups.join(", ");
                      }
                  }
              }
          });
        }

        const findRiyadh =
          session?._institution_calendar_id?.calendar_name?.includes("RUH");

        if (findRiyadh) {
        $("#classes-grid").append(`
                        <div class="status-card col-md-2 ${
                          session.status
                        }" data-type="session" data-status="${
          session.status
        }" data-time="${differenceInMinutes}" style="border-left: 10px solid ${
          styles.borderColor
        }; box-shadow: ${styles.boxShadow};" onclick="openSessionDetails('${
          session._id
        }', '${baseUrl}', '${headers}')">
                        <i class="fas ${icons[session.status]}"></i>
                        <div>
                            <h3>${session.course_name}</h3>
                            <p>${session.staffs[0].staff_name.first} ${
          session.staffs[0].staff_name.last
        }</p>
                            <p>${startTime} - ${endTime}</p>
                            <p>Present: ${
                              session.studentAttendance.presentCount
                            } | Absent: ${
          session.studentAttendance.absentCount
        }</p>
                            <p><strong>Status: ${statusTextDisplay}</strong></p>
                            ${
                              hasLate
                                ? `<p><strong>${lateByText}</strong></p>`
                                : ""
                            }
                            <p><strong>Student Group:</strong> ${groupText || "-"}</p>
                        </div>
                    </div>
                    `);
        }
      });

      if ($("#exams-tab").hasClass("active")) {
        if ($('.status-card[data-type="exam"]:visible').length === 0) {
          $("#no-exams-grid").show();
        }
      } else {
        if ($('.status-card[data-type="session"]:visible').length === 0) {
          $("#no-sessions-grid").show();
        }
      }
    } else {
      $("#no-sessions-grid").show();
    }
  } else {
    $("#no-sessions-grid").show();
  }
  applyFilterBySettings();
}

// Render sessions
function renderExams(response, daServiceApiBaseUrl, daServiceApiServiceKey) {
  if (response.message === "Retrieved successfully" && response.data) {
    $("#exams-grid").empty();
    $("#no-exams-grid").hide();

    if (response.data.length > 0) {
      const currentTime = new Date();

      // Sort exams by whether the current time falls within start and end times
      const sortedExams = response.data.sort((a, b) => {
        // Create proper Date objects for comparison
        const today = new Date();
        const startA = new Date(today);
        const endA = new Date(today);
        const startB = new Date(today);
        const endB = new Date(today);

        // Set hours and minutes for session A
        startA.setHours(
          a.session.start.format === "PM" && a.session.start.hour !== 12
            ? a.session.start.hour + 12
            : a.session.start.hour,
          a.session.start.minute
        );
        endA.setHours(
          a.session.end.format === "PM" && a.session.end.hour !== 12
            ? a.session.end.hour + 12
            : a.session.end.hour,
          a.session.end.minute
        );

        // Set hours and minutes for session B
        startB.setHours(
          b.session.start.format === "PM" && b.session.start.hour !== 12
            ? b.session.start.hour + 12
            : b.session.start.hour,
          b.session.start.minute
        );
        endB.setHours(
          b.session.end.format === "PM" && b.session.end.hour !== 12
            ? b.session.end.hour + 12
            : b.session.end.hour,
          b.session.end.minute
        );

        const isCurrentInA = currentTime >= startA && currentTime <= endA;
        const isCurrentInB = currentTime >= startB && currentTime <= endB;

        // Prioritize current sessions
        if (isCurrentInA && !isCurrentInB) return -1;
        if (!isCurrentInA && isCurrentInB) return 1;

        return startA - startB;
      });

      sortedExams.forEach((sessionData) => {
        const startTime = `${sessionData.session.start.hour}:${sessionData.session.start.minute} ${sessionData.session.start.format}`;
        const endTime = `${sessionData.session.end.hour}:${sessionData.session.end.minute} ${sessionData.session.end.format}`;

        let statusTextDisplay = sessionData.session.status;
        let lateByText = "";
        let hasLate = false;

        // Check if actual start time exists and calculate late status
        if (sessionData.session.actualStart) {
          const actualStart = `${sessionData.session.actualStart.hour}:${sessionData.session.actualStart.minute} ${sessionData.session.actualStart.format}`;
          const startMinutes =
            sessionData.session.start.hour * 60 +
            sessionData.session.start.minute;
          const actualStartMinutes =
            sessionData.session.actualStart.hour * 60 +
            sessionData.session.actualStart.minute;
          const differenceInMinutes = actualStartMinutes - startMinutes;

          if (differenceInMinutes > 5) {
            sessionData.session.status = "late";
            lateByText = `Late by ${Math.round(differenceInMinutes)} mins`;
            hasLate = true;
          }
        }

        const styles1 = statusStyles[sessionData.session.status] || {};

        // Create a comma-separated string of parameters
        const paramsString = encodeURIComponent(
          JSON.stringify({
            name: sessionData.name,
            startHour: sessionData.session.start.hour,
            startMinute: sessionData.session.start.minute,
            startFormat: sessionData.session.start.format,
            endHour: sessionData.session.end.hour,
            endMinute: sessionData.session.end.minute,
            endFormat: sessionData.session.end.format,
            assignedProctors: sessionData.assignedProctors,
            exams: sessionData.exams[0].course.name,
            apiUrl: daServiceApiBaseUrl,
            apiKey: daServiceApiServiceKey,
          })
        );

        $("#exams-grid").append(`
                    <div class="status-card col-md-2 ${statusTextDisplay}" data-type="exam" data-status="${
          sessionData.session.status
        }" style="border-left: 10px solid ${styles1.borderColor}; box-shadow: ${
          styles1.boxShadow
        };" onclick="openExamDetails('${paramsString}')">
                        <i class="fas ${icons[sessionData.session.status]}"></i>
                        <div>
                            <h3>${sessionData.exams[0].course.name} (${
          sessionData.exams[0].course.code
        })</h3>
                            <p>${sessionData.assignedProctors[0].name.first} ${
          sessionData.assignedProctors[0].name.last
        }</p>
                            <p>${startTime} - ${endTime}</p>
                            <p>Present: ${
                              sessionData.count.present
                            } | Absent: ${sessionData.count.absent}</p>
                            <p><strong>Status: ${statusTextDisplay}</strong></p>
                            ${
                              hasLate
                                ? `<p><strong>${lateByText}</strong></p>`
                                : ""
                            }
                        </div>
                    </div>
                `);
      });

      if ($("#exams-tab").hasClass("active")) {
        if ($('.status-card[data-type="exam"]:visible').length === 0) {
          $("#no-exams-grid").show();
        }
      } else {
        if ($('.status-card[data-type="session"]:visible').length === 0) {
          $("#no-sessions-grid").show();
        }
      }
    } else {
      $("#no-exams-grid").show();
    }
  } else {
    $("#no-exams-grid").show();
  }
  applyFilterBySettings();
}

// Close the sidebar when clicking outside of it
$(document).click(function (event) {
  if (!$(event.target).closest(".sidebar, .status-card").length) {
    closeSidebar();
  }
});

// Close the sidebar function
function closeSidebar() {
  $("#sidebar").removeClass("open");
}
