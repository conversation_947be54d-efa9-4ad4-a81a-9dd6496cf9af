import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Map } from 'immutable';

import {
  selectIsLoading,
  selectMessage,
} from '../../ReduxApi/Sessions/selectors';
import * as actions from '../../ReduxApi/Sessions/action';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import CourseSessionList from '../Sessions/components/CourseSessionList';
import {
  selectIsLoading as selectDocumentIsLoading,
  selectMessage as selectDocumentMessage,
} from '../../ReduxApi/Documents/selectors';
import {
  selectIsLoading as selectActivitiesIsLoading,
  selectMessage as selectActivitiesMessage,
} from '../../ReduxApi/Activites/selectors';
import { selectAuthData } from '../../ReduxApi/User/selectors';
import { selectIsTodaySessionLoading } from '../../ReduxApi/Sessions/selectors';
import { getURLParams } from '../../ReduxApi/util';
import {
  selectIsLoading as selectCourseSessionIsLoading,
  // selectMessage as selectCourseSessionMessage,
} from '../../ReduxApi/CourseSettings/selector';

class AllSessions extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sessionId: getURLParams('_id', true),
      scheduleId: getURLParams('_schedule_id', true),
      courseId: getURLParams('_course_id', true),
      calendarId: getURLParams('_cid', true),
      year: getURLParams('_year_no', true),
      level: getURLParams('_level_no', true),
      mergeStatus: getURLParams('_merge_status', true),
      programId: getURLParams('_program_id', true),
      type: getURLParams('_type', true),
      rotation: getURLParams('rotation', true),
      rotationCount: getURLParams('rotation_count', true),
      term: getURLParams('term', true),
      currentTab: 'today',
    };
  }

  componentDidMount() {
    this.fetchTabApi();
  }

  static getDerivedStateFromProps() {
    return {
      sessionId: getURLParams('_id', true),
      scheduleId: getURLParams('_schedule_id', true),
      courseId: getURLParams('_course_id', true),
      calendarId: getURLParams('_cid', true),
      year: getURLParams('_year_no', true),
      level: getURLParams('_level_no', true),
      mergeStatus: getURLParams('_merge_status', true),
      programId: getURLParams('_program_id', true),
      type: getURLParams('_type', true),
      rotation: getURLParams('rotation', true),
      rotationCount: getURLParams('rotation_count', true),
      term: getURLParams('term', true),
    };
  }

  fetchTabApi = () => {
    const { getCourseSessionTabs, authData } = this.props;
    const {
      courseId,
      calendarId,
      programId,
      year,
      level,
      mergeStatus,
      rotation,
      rotationCount,
      term,
    } = this.state;
    if (authData.get('_id', '') !== '') {
      getCourseSessionTabs(
        {
          userId: authData.get('_id', ''),
          courseId,
          programId,
          calendarId,
          year,
          level,
          mergeStatus,
          rotation,
          rotationCount,
          term,
          courseAdmin:
            getURLParams('_admin_courses', true) === 'true' ? true : false,
        },
        this.fetchApi
      );
    }
  };

  fetchApi = ({ type = 'today' }) => {
    const {
      courseId,
      calendarId,
      programId,
      year,
      level,
      mergeStatus,
      rotation,
      rotationCount,
      term,
    } = this.state;
    const { getCourseListWithSessions, authData } = this.props;

    if (authData.get('_id', '') !== '') {
      getCourseListWithSessions({
        userId: authData.get('_id', ''),
        courseId,
        programId,
        calendarId,
        year,
        level,
        mergeStatus,
        type,
        rotation,
        rotationCount,
        term,
        courseAdmin:
          getURLParams('_admin_courses', true) === 'true'
            ? 'course-admin'
            : 'course_session',
      });
      this.setState({ currentTab: type });
    }
  };

  render() {
    const {
      isLoading,
      message,
      isDocumentLoading,
      documentMessage,
      isTodaySessionLoading,
      isActivitiesLoading,
      activitiesMessage,
      courseSessionIsLoading,
      // courseSessionMessage,
    } = this.props;
    const { currentTab } = this.state;
    return (
      <>
        {message !== '' && <SnackBars show={true} message={message} />}
        {documentMessage !== '' && (
          <SnackBars show={true} message={documentMessage} />
        )}
        {activitiesMessage !== '' && (
          <SnackBars show={true} message={activitiesMessage} />
        )}
        {/* {courseSessionMessage !== '' && (
          <SnackBars show={true} message={courseSessionMessage} />
        )} */}
        <Loader
          isLoading={
            isTodaySessionLoading ||
            isLoading ||
            isDocumentLoading ||
            isActivitiesLoading ||
            courseSessionIsLoading
          }
        />
        <Switch>
          <Route
            exact
            path="/all-sessions/byCourse/:courseId"
            render={(props) => (
              <CourseSessionList
                {...props}
                tabsApi={this.fetchApi}
                currentTab={currentTab}
              />
            )}
          />
        </Switch>
      </>
    );
  }
}

AllSessions.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  isDocumentLoading: PropTypes.bool,
  documentMessage: PropTypes.string,
  getCourseListWithSessions: PropTypes.func,
  authData: PropTypes.instanceOf(Map),
  setBreadCrumbName: PropTypes.func,
  setData: PropTypes.func,
  getSchedule: PropTypes.func,
  isTodaySessionLoading: PropTypes.bool,
  isActivitiesLoading: PropTypes.bool,
  activitiesMessage: PropTypes.string,
  getCourseSessionTabs: PropTypes.func,
  courseSessionIsLoading: PropTypes.bool,
  // courseSessionMessage: PropTypes.string,
};

const mapStateToProps = (state) => {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    isDocumentLoading: selectDocumentIsLoading(state),
    documentMessage: selectDocumentMessage(state),
    authData: selectAuthData(state),
    isTodaySessionLoading: selectIsTodaySessionLoading(state),
    isActivitiesLoading: selectActivitiesIsLoading(state),
    activitiesMessage: selectActivitiesMessage(state),
    courseSessionIsLoading: selectCourseSessionIsLoading(state),
    // courseSessionMessage: selectCourseSessionMessage(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions)
)(AllSessions);
