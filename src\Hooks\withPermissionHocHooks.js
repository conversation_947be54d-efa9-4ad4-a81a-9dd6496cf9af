import React from 'react';
import usePermissionHook from './usePermissionHook';

function withPermissionHooks(Component) {
  const InjectedCalenderHooks = function (props) {
    const [
      checkPermissionAction,
      checkPermissionActionPhase1,
      checkPermissionSubTabAction,
      checkPermissionPage,
    ] = usePermissionHook();
    return (
      <Component
        {...props}
        checkPermissionAction={checkPermissionAction}
        checkPermissionActionPhase1={checkPermissionActionPhase1}
        checkPermissionSubTabAction={checkPermissionSubTabAction}
        checkPermissionPage={checkPermissionPage}
      />
    );
  };
  return InjectedCalenderHooks;
}

export default withPermissionHooks;
