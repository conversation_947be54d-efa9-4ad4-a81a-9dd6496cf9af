import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Container, Row, Col } from 'react-bootstrap';
import './attendance_report.scss';

class AttendanceReport extends Component {
  render() {
    return (
      <Container fluid className="p-0">
        <Container fluid className="p-0">
          <div className="d-flex justify-content-end">
            <div className="dig-footer-note text-right text-uppercase">
              Attendance (90/107)
            </div>
          </div>
        </Container>
        <Container fluid className="digi-attendance-report-card">
          <Row className="align-items-center">
            <Col lg={9} sm={8}>
              <div className="text-uppercase digi-font-500 mb-2">
                Attendance (90/107)
              </div>
              <div className="col-md-10 col-12 pl-0">
                <div className="row">
                  <div className="col-md-2 col-12 digi-font-14 mb-2">
                    <i
                      className="mr-1 fa fa-times-circle digi-cross-red"
                      aria-hidden="true"
                    ></i>
                    Absent : 7
                  </div>
                  <div className="col-md-2 col-12  digi-font-14 mb-2">
                    <i
                      className="mr-1 fa fa-check-circle"
                      aria-hidden="true"
                    ></i>
                    Present : 93
                  </div>
                  <div className="col-md-2 col-12  digi-font-14 mb-2">
                    <i
                      className="mr-1 fa fa-exclamation-circle"
                      aria-hidden="true"
                    ></i>
                    Leave : 10
                  </div>
                </div>
              </div>
            </Col>
            <Col
              lg={3}
              sm={4}
              className="d-flex justify-content-lg-end justify-content-md-end justify-content-sm-start"
            >
              <Link to={`/data/attendance`}>
                <div className="digi-font-14 text-uppercase digi-font-500 mr-4 digi-blue">
                  View report
                </div>
              </Link>
              <div>
                <i
                  className="mr-1 fa fa-times-circle digi-cross-black"
                  aria-hidden="true"
                ></i>
              </div>
            </Col>
          </Row>
        </Container>
      </Container>
    );
  }
}
export default withRouter(AttendanceReport);
