import React, { useState } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Grid,
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const Overview = () => {
  const [studentGroup, setStudentGroup] = useState('MG-1');

  return (
    <Accordion defaultExpanded>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
      >
        <Typography variant="h6" fontWeight={600}>
          ANAT 203 - Basic Human Anatomy
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ width: '100%' }}>
          <Typography color="text.secondary" mb={2}>
            Medicine | Year 2, Level 3, Regular | Standard | Aug 18 - Dec 29 (19
            weeks)
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="subtitle2" color="text.secondary">
                Participating Subjects
              </Typography>
              <Typography> Anatomy, Histology, Embryology </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="subtitle2" color="text.secondary">
                Administrating Subject
              </Typography>
              <Typography> Anatomy </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="subtitle2" color="text.secondary">
                Administrating Department
              </Typography>
              <Typography> Anatomy </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="subtitle2" color="text.secondary">
                Course Coordinator
              </Typography>
              <Typography fontWeight={500}>
                MARWA MOHAMED SAFWAT ZAKI
              </Typography>
            </Grid>
          </Grid>
          <Box mt={4} display="flex" alignItems="center">
            <Typography fontWeight={500} mr={2}>
              Student Group
            </Typography>
            <FormControl size="small">
              <Select
                value={studentGroup}
                onChange={(e) => setStudentGroup(e.target.value)}
                sx={{ minWidth: 100 }}
              >
                <MenuItem value="MG-1">MG-1</MenuItem>
                <MenuItem value="MG-2">MG-2</MenuItem>
                <MenuItem value="MG-3">MG-3</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default Overview;
