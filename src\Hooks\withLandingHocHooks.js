import React, { Suspense } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import LocalStorageService from 'LocalStorageService';
//import { getSiteUrl } from 'utils';

const LandingModal = React.lazy(() =>
  import('../Modules/Dashboard/modals/landing')
);

function withLandingHocHooks(Component) {
  const InjectedCurrentCalendar = function (props) {
    const { authData, history } = props;
    // const siteName = LocalStorageService.getCustomCookie('site-id-name');
    // useEffect(() => {
    //   if (siteName !== '' && siteName === 'dc-admin') {
    //     const siteURL = getSiteUrl();
    //     const siteOrigin = LocalStorageService.getCustomCookie('site-origin');
    //     window.location =
    //       siteURL.DC_ADMIN_URL === undefined
    //         ? siteOrigin
    //         : siteURL.DC_ADMIN_URL;
    //   }
    // }, [siteName]);
    const hasLandedAlready =
      LocalStorageService.getCustomCookie('landed') === 'false';
    return (
      <React.Fragment>
        {!authData.isEmpty() &&
        hasLandedAlready &&
        authData.get('user_type', '') !== 'student' ? (
          <Suspense fallback="">
            <LandingModal history={history} callBack={() => {}} />
          </Suspense>
        ) : (
          ''
        )}
        <Component {...props} />
      </React.Fragment>
    );
  };

  InjectedCurrentCalendar.propTypes = {
    authData: PropTypes.instanceOf(Map),
    history: PropTypes.object,
  };

  return InjectedCurrentCalendar;
}

export default withLandingHocHooks;
