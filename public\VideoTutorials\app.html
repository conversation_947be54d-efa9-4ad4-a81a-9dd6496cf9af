<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tutorial List</title>
  <!-- Include Bootstrap CSS -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <!-- Custom CSS for a professional look -->
  <style>
    body {
      background-color: #f8f9fa;
      color: #333;
    }

    .btn-primary {
      background-color: var(--backgroundTheme-color-vibrant);
      border-color: var(--backgroundTheme-color-vibrant);
    }

    .btn-primary:hover {
      background-color: var(--backgroundTheme-color-medium);
      border-color: var(--backgroundTheme-color-medium) ;
    }

    .card-header {
      background-color: #59abf2a8;
      color: #fff;
    }

    .subtopic {
      color: #007bff;
    }

    .subtopic:hover {
      text-decoration: underline;
    }

    #close-video {
     
      border-color: #59acf2;
      margin-bottom: 20px;
      float: right;
    }

    #close-video:hover {
      background-color: #007bff;
      border-color: #007bff;
    }

    .accordion-arrow::before {
      content: '\25B8';
      font-size: 1rem;
      float: right;
      transition: transform 0.3s ease-in-out;
      transform: rotate(90deg);
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        -o-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        transform: rotate(90deg);
    }

    .accordion-rotate::before {
      transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    #video-container{
        margin-bottom: 20%;
    }
    .card-header{
        padding: 0;
    }
    .btn-link{
        width: 100%;
        text-decoration: none;
        color: #413737;
        text-align: left;
        font-weight: 600;
    }
    .btn-link:hover,.btn-link:active,.btn-link:focus{
        text-decoration: none;
        color: black;
    }
    ul{
        margin-bottom: 0;
    }

    .text-center {
    display: flex;         /* Convert the container into a Flex container */
    align-items: center;   /* Align items vertically in the center */
    justify-content: center; /* Align items horizontally in the center */
    font-size: 20px;
}

.logo {
    width: 50px;           /* Define your image width here */
    height: 50px;          /* Define your image height here */
    margin-right: 10px;    /* Spacing between the image and the text */
}
.new_icon{
    display: inline-block;
	width: 30px;
}
.select-panel{
  text-align: center;
    margin: 12px;
}
.msg-video{
  display: none;
}

  </style>
  <script src="digiclass_data.json"></script>
  <script src="digiassess_data.json"></script>
</head>
<body>
  <!-- <label for="customRange1" class="form-label">Select  Product </label> -->

<div class="container mt-4">
    <h1 class="text-center">
        <img src="logo.jpeg" alt="Logo" class="logo">
        <div id="userTitle"></div> 
        
    </h1>
    <div class="d-grid gap-2 col-6 mx-auto select-panel">
    

      <button class="btn btn-primary btn-lg" type="button">DigiClass</button>
      <button class="btn btn-success btn-lg" type="button">DigiAssess</button>
    </div>
  
  <div id="accordion">
    <!-- Dynamic JSON data and generation here... -->
  </div>
  

  <!-- Embedded YouTube video container -->
  <div id="video-container" class="mt-4">
    <span class="msg-video"><b>Double click on videos to view Fullscreen - <b></span>
    <button id="close-video" class="btn btn-danger" style="display: none;">Close Video</button>
    <iframe id="youtube-iframe" width="100%" height="315" src=""   title="DigiVal- Empathizing your Concerns" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"  frameborder="0" allowfullscreen></iframe>
  </div>

 
</div>


<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


<script>
  // Your jQuery code for handling subtopic click and video display here...
  $(document).ready(function() {
    // accordion1
    $(".select-panel button").on('click',function(){
      $(".cards-dc,.cards-da").hide()
      if($(this).text()=="DigiClass"){
        $(".cards-dc").show('slow')
      } 
      else{
        $(".cards-da").show('slow')
      }
    })


    $(document).on('click', '.subtopic', function(e) {
      e.preventDefault();
      const videoUrl = $(this).data('video');
      $('#youtube-iframe').attr('src', videoUrl);

      $('#video-container').show();
      $('#close-video,.msg-video').show();
      $('html, body').animate({ scrollTop: $(document).height() }, 'slow');
    });

    $('#close-video').click(function() {
      $('#youtube-iframe').attr('src', '');
      $('#video-container').hide();
      $('#close-video,.msg-video').hide();
    });
    // Toggle accordion arrow on click
    $(document).on('click', '.btn-link', function() {
      $(this).find('.accordion-arrow').toggleClass('accordion-rotate');
    });
  });
  </script>

<script>
  // Your JSON data and generation code here...
  const queryParams = new URLSearchParams(window.location.search);
  const type = queryParams.get('type');
   accordionHTML=""
   oldIndex=0
  document.getElementById("userTitle").innerText = type === 'staff' ? 'Digival - Faculty knowledge Base' : 'Digival - Student knowledge Base';
  function render(product_wise_data,product_name,id) {
    const filteredData = product_wise_data.filter(item =>
      item.subtopics.some(subtopic => subtopic.userType === type) ||
      (item.resources && item.resources.some(resource => resource.userType === type))
    );
    filteredData.forEach((item, index) => {
      oldIndex++
      index=oldIndex
      const topicId = `topic${index}`;
      const collapseId = `collapseTopic${index}`;
      const videoContainerId = `video-container${index}`;
      accordionHTML += `
        <div class="card cards-${product_name}">
          <div class="card-header" id="${topicId}">
            <h5 class="mb-0 main-card">
              <button class="btn btn-link" data-toggle="collapse" data-target="#${collapseId}" aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="${collapseId}">
                ${item.topic}    ${item.new==true ?'<img  class="new_icon"src="new.gif">':""} <span class="accordion-arrow"></span>
              </button>
            </h5>
          </div>

          <div id="${collapseId}" class="collapse${index === 0 ? ' show' : ''}" aria-labelledby="${topicId}" data-parent="#accordion">
            <div class="card-body">
              <ul>
                <!-- Loop through subtopics for this topic -->
                ${item.subtopics.filter((user) => user?.userType === type).map((subtopic, subIndex) => `
                  <li>
                    <a href="#" class="subtopic" data-video="${subtopic.url}">
                      ${subtopic.title}
                      ${subtopic.new==true ?'<img  class="new_icon"src="new.gif">':""} 
                    </a>
                  </li>
                `).join('')}
              </ul>
              <!-- Loop through resources for this topic -->
              <ul>
                ${item.resources.filter((user) => user?.userType === type).map((resource, resourceIndex) => `
                  <li>
                    <a href="${resource.url}" target="_blank" rel="noopener noreferrer">
                      ${resource.title} (${resource.type})  ${resource.new==true ?'<img  class="new_icon"src="new.gif">':""}
                    </a>

                  </li>
                `).join('')}
              </ul>
            </div>
          </div>
        </div>
      `;
      // console.log("accordionHTML",accordionHTML)


    });
    if(id){
      $('#accordion').append(accordionHTML);
    } 
  }

  // Fetch data and render content
  fetch('digiclass_data.json')
    .then(response => response.json())
    .then(digiclass_data => {
      render(digiclass_data, "dc", false);
    });

  fetch('digiassess_data.json')
    .then(response => response.json())
    .then(digiassess_data => {
      render(digiassess_data, "da", true);
    });
</script>

</body>
</html>
