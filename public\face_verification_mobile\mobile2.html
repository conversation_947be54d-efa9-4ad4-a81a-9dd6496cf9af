<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Face capture camera</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="assets/css/style.css" />
    <style>
      .canvas,
      .debug {
        display: none;
      }
      #canvas {
        display: none;
      }

      .init-hide {
        display: none !important;
      }

      .bodyhide {
        display: none !important;
      }

      #original-video {
        /*    display: none;*/
      }

      .digi-blink-fail-panel,
      .digi-blink-panel {
        display: none;
      }
      .digi-body-content {
        height: auto;
      }
      .digi-camera-page.digi-active {
        display: inline-flex;
      }
      .digi-info-icon-container .digi-tooltip-container {
        padding: 10px 10px 15px;
        width: 235px;
        border-radius: 5px;
      }
      .digi-tooltip-header {
        margin-bottom: 6px;
      }
      .digi-info-icon-container
        .digi-tooltip-container
        .material-icons.digi-eyes-icon {
        font-size: 22px;
      }
      .digi-tooltip-title {
        font-size: 16px;
      }
      .digi-info-icon-container
        .digi-tooltip-container
        .digi-close-icon-container {
        margin-top: 4px;
      }
      .digi-info-icon-container .digi-tooltip-container .material-icons.iclose {
        font-size: 18px;
      }

      @media screen and (max-width: 750px) {
        .digi-camera-container {
          min-width: auto;
          max-width: 340px;
        }
        /* .digi-camera-center-container {
          height: auto;
        } */
        .digi-video {
          transform: scale(-1, 1) !important;
        }
      }
    </style>

    <script src="//code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="assets/js/face-api.js"></script>

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    />

    <!-- Require the peer dependencies of facemesh. -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-core@2.6.0/dist/tf-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-converter@2.6.0/dist/tf-converter.min.js"></script> -->

    <!-- You must explicitly require a TF.js backend if you're not using the tfs union bundle. -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-wasm@2.6.0/dist/tf-backend-wasm.min.js"></script> -->

    <!-- Pretrained facemesh model. -->
    <!-- <script
        src="https://cdn.jsdelivr.net/npm/@tensorflow-models/face-landmarks-detection@0.0.2/dist/face-landmarks-detection.min.js"></script> -->

    <!-- https://medium.com/swlh/how-to-access-webcam-and-take-picture-with-javascript-b9116a983d78 -->
    <script
      type="text/javascript"
      src="https://unpkg.com/webcam-easy/dist/webcam-easy.min.js"
    ></script>
    <script
      type="text/javascript"
      src=" https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"
    ></script>
  </head>

  <body>
    <img
      id="snapshotplaceholder"
      class="dummyfacialimage"
      src="assets/images/dummy.JPG"
    />

    <div class="loader"></div>
    <canvas id="canvas" class="d-none"></canvas>
    <section class="digi-camera-page init-hide digi-active">
      <div class="digi-body-content">
        <section class="digi-camera-container">
          <div class="digi-camera-center-container">
            <div class="digi-frame-container">
              <video
                id="webcam"
                autoplay
                playsinline
                class="digi-video"
              ></video>
              <!-- <video id="original-video" autoplay playsinline></video> -->
            </div>

            <div class="digi-curve-cut-frame-container">
              <img src="assets/images/frame.png" class="digi-curve-cut-frame" />
            </div>

            <div class="step3 steps digi-blink-now-text blink-soft">
              Blink Now
            </div>

            <section class="step3 steps digi-blinker">
              <div class="digi-eyes-container">
                <div class="digi-eyes">
                  <span class="digi-eyes-gif">
                    <img
                      src="assets/images/eye gif.gif"
                      class="digi-blink-icon"
                    />
                  </span>
                  <span style="min-width: 45px"></span>
                  <span class="digi-eyes-gif">
                    <img
                      src="assets/images/eye gif.gif"
                      class="digi-blink-icon"
                    />
                  </span>
                </div>
              </div>
            </section>

            <div class="digi-bottom-pannel-container">
              <section
                class="step1 steps digi-control-panel digi-bg-black digi-active"
              >
                <span class="digi-icon-container">
                  <span class="digi-info-icon-container">
                    <div class="digi-tooltip-container">
                      <div class="digi-tooltip-header">
                        <span class="digi-eye-icon-container">
                          <span class="material-icons digi-eyes-icon">
                            visibility
                          </span>
                        </span>
                        <span style="min-width: 5px"></span>
                        <span class="digi-tooltip-title"
                          >Facial Authentication</span
                        >
                        <span style="flex: 1"></span>
                        <span class="digi-close-icon-container">
                          <span class="material-icons iclose"> close </span>
                        </span>
                      </div>
                      <span class="digi-tooltip-content">
                        Click on capture button to verify your Face
                      </span>
                      <span class="digi-down-arrow"></span>
                    </div>

                    <span class="material-icons digi-white-color info-tooltip">
                      info
                    </span>
                  </span>
                </span>

                <span style="flex: 1"></span>
                <span class="digi-icon-container">
                  <span>
                    <span
                      class="material-icons cam1 digi-home-icon digi-white-color"
                    >
                      camera
                    </span>
                    <span class="material-icons cam2 digi-white-color">
                      <i class="large material-icons">add_a_photo</i>
                    </span>
                  </span>
                </span>

                <span style="flex: 1"></span>
                <span class="digi-icon-container iclose2">
                  <span>
                    <span class="material-icons digi-white-color"> close </span>
                  </span>
                </span>
              </section>
              <div id="taptocapture">Tap to capture</div>
              <section class="step2 steps digi-loader-panel digi-bg-black">
                <div class="digi-loader-panel-center">
                  <div class="loading">
                    <span class="loading__dot"></span>
                    <span class="loading__dot"></span>
                    <span class="loading__dot"></span>
                  </div>

                  <span style="min-width: 10px"></span>
                  <span class="digi-loading-text"
                    >Please come Closer to Camera</span
                  >
                </div>
              </section>

              <section class="step3 steps digi-blink-panel digi-bg-black">
                <div class="digi-blink-panel-center">
                  <span class="digi-blink-icon-container">
                    <img
                      src="assets/images/eye gif.gif"
                      class="digi-blink-icon"
                    />
                  </span>
                  <span style="min-width: 10px"></span>
                  <span class="digi-blink-text">Blink Now</span>
                </div>
              </section>

              <section class="step4 steps digi-blink-fail-panel digi-bg-white">
                <div class="digi-blink-fail-status-container">
                  <span class="digi-warning-icon-container">
                    <span class="material-icons digi-warning-icon">
                      error_outline
                    </span>
                  </span>

                  <span style="min-width: 10px"></span>
                  <div class="digi-blink-fail-text-container">
                    <h4 class="digi-blink-fail-title">
                      Blink Identification Failed
                    </h4>
                    <span class="digi-blink-fail-subtitle">
                      You have missed to blink on the request / Too many wrong
                      blinks
                    </span>
                  </div>
                </div>

                <div class="digi-blink-fail-action-container">
                  <button class="digi-fail-button cancel-track">Cancel</button>
                  <span style="min-width: 10px"></span>
                  <button class="digi-fail-button">Try again</button>
                </div>
              </section>
              <section class="step5 steps digi-blink-fail-panel digi-bg-white">
                <div class="digi-blink-fail-status-container">
                  <span class="digi-warning-icon-container">
                    <span class="material-icons digi-warning-icon">
                      error_outline
                    </span>
                  </span>

                  <span style="min-width: 10px"></span>
                  <div class="digi-blink-fail-text-container">
                    <h4 class="digi-blink-fail-title">
                      Face Identification Failed
                    </h4>
                    <span class="digi-blink-fail-subtitle">
                      Please make sure your face fits the circle and lighting is
                      good
                    </span>
                  </div>
                </div>

                <div class="digi-blink-fail-action-container">
                  <button class="digi-fail-button cancel-track">Cancel</button>
                  <span style="min-width: 10px"></span>
                  <button class="digi-fail-button">Try again</button>
                </div>
              </section>
            </div>
          </div>
        </section>
      </div>
      <button class="post-snap debug">snap and post</button>
      <button class="window-message debug">check window message</button>
      <img src="" class="test-img" />
    </section>

    <section class="init-hide digi-verification-success-page">
      <section class="digi-authentication-container">
        <h4 class="digi-authentication-title">Authentication</h4>
        <div class="digi-profile-picture-container">
          <img src="" alt="" class="digi-profile-picture" />
          <!-- <img src="assets/images/profile.png" alt="profile picture" class="digi-profile-picture" /> -->
        </div>

        <section class="digi-success-container">
          <div class="digi-success-content">
            <span class="digi-success-image-container">
              <span class="material-icons digi-success-icon">
                check_circle
              </span>
            </span>
            <span style="min-width: 5px"></span>
            <span class="digi-success-text">Authentication successful</span>
          </div>
        </section>

        <!-- <section class="digi-processing-bottom-panel">
          <div class="digi-processing-button-container">
            <button class="digi-processing-button">Processing...</button>
          </div>
        </section> -->
      </section>
    </section>
    <script src="assets/js/blink_mobile2.js"></script>
  </body>
</html>
