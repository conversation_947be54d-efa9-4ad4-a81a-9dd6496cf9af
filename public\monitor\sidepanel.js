// Function to open the settings sidebar
function openSettings() {
    $('#settings-sidebar').addClass('open');
}

// Function to close the settings sidebar
function closeSettings() {
    $('#settings-sidebar').removeClass('open');
}

function saveFilterValues() {
    const selectedStatusesClass = $('.class-filter:checked').map(function () {
        return this.value;
    }).get();
    const selectedStatusExam = $('.exam-filter:checked').map(function () {
        return this.value;
    }).get();
    
    const selectedTimeFilter = $('input[name="time-filter"]:checked').val();
    const colorPicker = $('#colorPicker').val();
    const dateFilter = $('#dateFilter').val();

    if(selectedStatusesClass.length > 0 || selectedStatusExam.length > 0 || selectedTimeFilter !== undefined || dateFilter !== undefined){
        localStorage.setItem('selectedStatusesClass', JSON.stringify(selectedStatusesClass));
        localStorage.setItem('selectedStatusExam', JSON.stringify(selectedStatusExam));
        localStorage.setItem('selectedTimeFilter', selectedTimeFilter);
        localStorage.setItem('colorPicker', colorPicker);
        localStorage.setItem('load_value', 'true'); 
        localStorage.setItem('dateFilter', dateFilter); 
    }

    // Get refresh time in seconds, ensure it's at least 60 seconds
    const refreshTime = $('#refreshTimeInput').val() || refreshPageTime;
    localStorage.setItem('refreshPageTime', refreshTime.toString());
}

function loadFilterValues() {
    const selectedStatusesClass = JSON.parse(localStorage.getItem('selectedStatusesClass') || '[]');
    const selectedStatusExam = JSON.parse(localStorage.getItem('selectedStatusExam') || '[]');
    const selectedTimeFilter = localStorage.getItem('selectedTimeFilter');
    const colorPicker = localStorage.getItem('colorPicker');
    const dateFilter = localStorage.getItem('dateFilter');

    $("#current-date").text(dateFilter || new Date().toISOString().split('T')[0]);
    $("#dateFilter").val(dateFilter || new Date().toISOString().split('T')[0]);

    $('.class-filter').each(function () {
        if (selectedStatusesClass.includes(this.value)) {    
            this.checked = true;
        }
    });
    $('.exam-filter').each(function () {
        if (selectedStatusExam.includes(this.value)) {    
            this.checked = true;
        }
    });

    if (selectedTimeFilter) {
        $('input[name="time-filter"][value="' + selectedTimeFilter + '"]').prop('checked', true);
    }

    if(colorPicker){
        $('#colorPicker').val(colorPicker)
        $('.header').css('background-color', colorPicker);
        $('.footer').css('background-color', colorPicker);
    }

    if(dateFilter){
        $('#dateFilter').val(dateFilter);
    }

    const savedRefreshTime = localStorage.getItem('refreshPageTime');
    if (savedRefreshTime) {
        $('#refreshTimeInput').val(savedRefreshTime);
    }
}

// Function to apply class filter based on settings
function applyClassFilter() {
    saveFilterValues();

    const colorPicker = $('#colorPicker').val()
    $('.header').css('background-color', colorPicker);
    $('.footer').css('background-color', colorPicker);

    const selectedClassStatuses = $('.class-filter:checked').map(function () {
        return this.value;
    }).get();

    const selectedExamStatuses = $('.exam-filter:checked').map(function () {
        return this.value;
    }).get();

    const selectedTimeFilter = $('input[name="time-filter"]:checked').val();

    const refreshTime = $('#refreshTimeInput').val() || refreshPageTime;
    setInterval(refreshPage, refreshTime);

    if(selectedClassStatuses.length > 0 || selectedExamStatuses.length > 0 || selectedTimeFilter !== undefined){

        // Filter logic for classes
        $('.status-card').each(function () {
            const status = $(this).data('status');
            const time = $(this).data('time');                
            let timeCondition = true;
            if (selectedClassStatuses) {
                if (selectedTimeFilter === 'late' && time <= 5) {
                    timeCondition = false;
                } else if (selectedTimeFilter === 'on-time' && time > 5) {
                    timeCondition = false;
                }
            }
    
            // Check if the card should be shown based on both class and exam status
            const matchesClass = selectedClassStatuses.includes(status);
            const matchesExam = selectedExamStatuses.includes(status);
            
            // Show the card if it matches either class or exam status AND meets time condition
            if ((matchesClass || matchesExam) && timeCondition) {
                $(this).show();              
            } else {
                $(this).hide();
            }

        });

        if ($('#exams-tab').hasClass('active')) {
            if($('.status-card[data-type="exam"]:visible').length === 0){
                $('#no-exams-grid').hide();
                $('#no-exams-grid-filter').show();
            } else {
                $('#no-exams-grid-filter').hide();
                $('#no-exams-grid').hide();
            }
        }else{
            if ($('.status-card[data-type="session"]:visible').length === 0) {
                $('#no-sessions-grid').hide();
                $('#no-sessions-grid-filter').show();
            } else {
                $('#no-sessions-grid-filter').hide();
                $('#no-sessions-grid').hide();
            }
        }

    } else {
        $('.status-card').show();
        setTimeout(() => {
            if ($('#exams-tab').hasClass('active')) {
                if($('.status-card[data-type="exam"]:visible').length === 0){
                    $('#no-exams-grid').show();
                    $('#no-exams-grid-filter').hide();
                } else {
                    $('#no-exams-grid-filter').hide();
                    $('#no-exams-grid').hide();
                }
            }else{
                if ($('.status-card[data-type="session"]:visible').length === 0) {
                    $('#no-sessions-grid-filter').hide();
                    $('#no-sessions-grid').show();
                } else {
                    $('#no-sessions-grid-filter').hide();
                    $('#no-sessions-grid').hide();
                }
            } 
        }, 0);
    }

    closeSettings();
}

// Open the sidebar with session details
function openSessionDetails(scheduleId, apiUrl, headers) {
    const detailsUrl = `${apiUrl}v1/hebaAI/scheduleWithDetails?scheduleId=${scheduleId}`;
    showLoader(true)
    $.ajax({
        url: detailsUrl,
        headers: { 'digicronkey': headers },
        success: function (response) {
            showLoader(false)
            if (response.message === 'success' && response.data) {
                const session = response.data;
                const startTime = convertToLocalTime(session.scheduleStartDateAndTime);
                const endTime = convertToLocalTime(session.scheduleEndDateAndTime);

                let presentStudentsHtml = '';
                let absentStudentsHtml = '';
                let pendingStudentsHtml = '';
                let presentCount = 0;
                let absentCount = 0;
                let pendingCount = 0;

                // Process student groups
                let groupText = "";

                if (session.student_groups && session.student_groups.length > 0) {
                    session.student_groups.forEach((mainGroup, index) => {
                        if (mainGroup.group_name) {
                            if (index > 0) {
                                groupText += " , ";
                            }

                            // Add main group name
                            if (mainGroup.group_name.length <= 4) {
                                groupText += mainGroup.group_name;
                            } else {
                                const dashes = mainGroup.group_name.split('-');
                                if (dashes.length >= 2) {
                                    const lastSegments = dashes.slice(-2);
                                    groupText += lastSegments.join('-');
                                } else {
                                    groupText += mainGroup.group_name;
                                }
                            }

                            // Add subgroups for this main group
                            let subGroups = [];
                            if (mainGroup.session_group && mainGroup.session_group.length > 0) {
                                mainGroup.session_group.forEach(sessionGroup => {
                                    if (sessionGroup.group_name) {
                                        const dashes = sessionGroup.group_name.split('-');
                                        if (dashes.length >= 3) {
                                            const lastSegments = dashes.slice(-3);
                                            const groupId = lastSegments.join('-');
                                            if (!subGroups.includes(groupId)) {
                                                subGroups.push(groupId);
                                            }
                                        }
                                    }
                                });
                                
                                if (subGroups.length > 0) {
                                    groupText += " - " + subGroups.join(", ");
                                }
                            }
                        }
                    });
                }

                session.students.forEach(student => {
                    const studentRow = `
                        <tr>
                            <td>${student.name.first} ${student.name.last}</td>
                            <td>${student.status.charAt(0).toUpperCase() + student.status.slice(1)}</td>
                        </tr>
                    `;
                    if (student.status === 'present') {
                        presentStudentsHtml += studentRow;
                        presentCount++;
                    } else if (student.status === 'absent') {
                        absentStudentsHtml += studentRow;
                        absentCount++;
                    } else if ( student.status === 'pending'){
                        pendingStudentsHtml += studentRow;
                        pendingCount++;    
                   }
                });

                let presentSection = presentCount > 0 ? `
                    <tr><td colspan="3" class="text-success"><strong>Present</strong></td></tr>
                    ${presentStudentsHtml}
                ` : '';

                let absentSection = absentCount > 0 ? `
                    <tr><td colspan="3" class="text-danger"><strong>Absent</strong></td></tr>
                    ${absentStudentsHtml}
                ` : '';

                let pendingSection = pendingCount > 0 ? `
                   <tr><td colspan="3" class="text-danger"><strong>Pending</strong></td></tr>
                   ${pendingStudentsHtml}
               ` : '';
                

                $('#details-content').html(`
                    <h3>${session.course_name}</h3>
                    <p><strong>Instructor:</strong> ${session.staffs[0].staff_name.first} ${session.staffs[0].staff_name.last}</p>
                    <p><strong>Timing:</strong> ${startTime} - ${endTime}</p>
                    <p><strong>Present Count:</strong> ${presentCount}</p>
                    <p><strong>Absent Count:</strong> ${absentCount}</p>
                    <p><strong>Program:</strong> ${session.program_name}</p>
                    <p><strong>Student Group:</strong> ${groupText || "-"}</p>
                    <div id="student-container-exam" class="students-container-exam mt-4" style="max-height: 350px; overflow-y: auto;">
                        <table class="table table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${absentSection}
                                ${pendingSection}
                                ${presentSection}
                            </tbody>
                        </table>
                    </div>
                `);
                $('#sidebar').addClass('open');
            }
        },
        error: function (xhr, status, error) {
            showLoader(false)
        }
    });
}

// Open the sidebar with exam details
function openExamDetails(paramsString) {
    const params = JSON.parse(decodeURIComponent(paramsString));
    const { name, startHour, startMinute, startFormat, endHour, endMinute, endFormat, assignedProctors, exams, apiUrl, apiKey } = params;
    const scheduleDate = $('#dateFilter').val() || new Date().toISOString().split('T')[0]; 
    //const detailsUrl = `${apiUrl}/misc/testcenter-student?date=${scheduleDate}&testcenterName=${name}&startHour=${startHour}&startMinute=${startMinute}&startFormat=${startFormat}&endHour=${endHour}&endMinute=${endMinute}&endFormat=${endFormat}`;
   const detailsUrl = `https://daapi.prodsla.digi-val.com/api/v1/misc/testcenter-student?date=${scheduleDate}&testcenterName=${name}&startHour=${startHour}&startMinute=${startMinute}&startFormat=${startFormat}&endHour=${endHour}&endMinute=${endMinute}&endFormat=${endFormat}`;
   showLoader(true)
   $.ajax({
       url: detailsUrl,
       headers: { 'x-api-key': apiKey },
        success: function (response) {
           showLoader(false)
           if (response.message === 'Retrieved successfully' && response.data) {
               let presentStudentsHtml = '';
               let absentStudentsHtml = '';
               let presentCount = 0;
               let absentCount = 0;
               
               response.data.forEach(student => {
                   const studentRow = `
                       <tr>
                           <td>${student.name.first} ${student.name.middle || ''} ${student.name.last}</td>
                           <td>${student.academicNo}</td>
                           <td>${student.status}</td>
                       </tr>
                   `;
                   
                   if (student.status === 'EXAM_STARTED') {
                       presentStudentsHtml += studentRow;
                       presentCount++;
                   } else if (student.status === 'ABSENT') {
                       absentStudentsHtml += studentRow;
                       absentCount++;
                   } 
               });

               let presentSection = presentCount > 0 ? `
                   <tr><td colspan="3" class="text-success"><strong>Present</strong></td></tr>
                   ${presentStudentsHtml}
               ` : '';

               let absentSection = absentCount > 0 ? `
                   <tr><td colspan="3" class="text-danger"><strong>Absent</strong></td></tr>
                   ${absentStudentsHtml}
               ` : '';

               $('#details-content').html(`
                   <h3>${name}</h3>
                   <p><strong>Instructor:</strong> ${assignedProctors[0].name.first} ${assignedProctors[0].name.last}</p>
                   <p><strong>Timing:</strong> ${startHour}:${startMinute} ${startFormat} - ${endHour}:${endMinute} ${endFormat}</p>
                   <p><strong>Present:</strong> ${response.data.filter(student => student.status === 'EXAM_STARTED').length}</p>
                   <p><strong>Absent:</strong> ${response.data.filter(student => student.status === 'ABSENT').length}</p>
                   <p><strong>Exam:</strong> ${exams}</p>
                   <div id="student-container-exam" class="students-container-exam mt-4" style="max-height: 350px; overflow-y: auto;">
                       <table class="table table-striped">
                           <thead class="thead-dark">
                               <tr>
                                   <th>Name</th>
                                   <th>Academic No</th>
                                   <th>Status</th>
                               </tr>
                           </thead>
                           <tbody>
                                ${absentSection}
                                ${presentSection}
                           </tbody>
                       </table>
                   </div>
               `);
               $('#sidebar').addClass('open');
           }
       },
       error: function (xhr, status, error) {
           showLoader(false)
       }
   });
}

// Close the sidebar when clicking outside of it
$(document).click(function (event) {
    if (!$(event.target).closest('.sidebar, .status-card').length) {
        closeSidebar();
    }
});

// Close the sidebar function
function closeSidebar() {
    $('#sidebar').removeClass('open');
}

// Function to apply class filter based on settings
function applyFilterBySettings() {
    const colorPicker = $('#colorPicker').val()
    $('.header').css('background-color', colorPicker);
    $('.footer').css('background-color', colorPicker);

    const selectedClassStatuses = $('.class-filter:checked').map(function () {
        return this.value;
    }).get();

    const selectedExamStatuses = $('.exam-filter:checked').map(function () {
        return this.value;
    }).get();

    const selectedTimeFilter = $('input[name="time-filter"]:checked').val();

    if(selectedClassStatuses.length > 0 || selectedExamStatuses.length > 0 || selectedTimeFilter !== undefined){

        // Filter logic for classes
        $('.status-card').each(function () {
            const status = $(this).data('status');
            const time = $(this).data('time');                
            let timeCondition = true;
            if (selectedClassStatuses) {
                if (selectedTimeFilter === 'late' && time <= 5) {
                    timeCondition = false;
                } else if (selectedTimeFilter === 'on-time' && time > 5) {
                    timeCondition = false;
                }
            }
    
            // Check if the card should be shown based on both class and exam status
            const matchesClass = selectedClassStatuses.includes(status);
            const matchesExam = selectedExamStatuses.includes(status);
            
            // Show the card if it matches either class or exam status AND meets time condition
            if ((matchesClass || matchesExam) && timeCondition) {
                $(this).show();              
            } else {
                $(this).hide();
            }

            if ($('#exams-tab').hasClass('active')) {
                if($('.status-card[data-type="exam"]:visible').length === 0){
                    $('#no-exams-grid').hide();
                    $('#no-exams-grid-filter').show();
                } else {
                    $('#no-exams-grid-filter').hide();
                    $('#no-exams-grid').hide();
                }
            }else{
                if ($('.status-card[data-type="session"]:visible').length === 0) {
                    $('#no-sessions-grid').hide();
                    $('#no-sessions-grid-filter').show();
                } else {
                    $('#no-sessions-grid-filter').hide();
                    $('#no-sessions-grid').hide();
                }
            }
        });
    } else {
        $('.status-card').show();
        setTimeout(() => {
            if ($('#exams-tab').hasClass('active')) {
                if($('.status-card[data-type="exam"]:visible').length === 0){
                    $('#no-exams-grid').show();
                    $('#no-exams-grid-filter').hide();
                } else {
                    $('#no-exams-grid-filter').hide();
                    $('#no-exams-grid').hide();
                }
            }else{
                if ($('.status-card[data-type="session"]:visible').length === 0) {
                    $('#no-sessions-grid-filter').hide();
                    $('#no-sessions-grid').show();
                } else {
                    $('#no-sessions-grid-filter').hide();
                    $('#no-sessions-grid').hide();
                }
            } 
        }, 0);
    }
}
