<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Face Photo Capture with MediaPipe</title>
		<link
			rel="stylesheet"
			href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
		/>
		<style>
			#camera {
				width: 100%;
				max-width: 500px;
				margin: auto;
			}
			.preview-box {
				width: 100px;
				height: 100px;
				margin: 5px;
				overflow: hidden;
				border: 2px solid #ddd;
			}
			.preview-box img {
				width: 100%;
				height: 100px;
			}
			.oval-image {
				position: absolute;
			}
		</style>
	</head>
	<body>
		<div class="container text-center mt-5">
			<h2>Face Capture Tool</h2>
			<p>Follow instructions to capture the required face positions.</p>

			<div id="camera-container" class="mb-3">
				<img src="./images/oval.png" alt="Oval shape" class="oval-image" />
				<video id="camera" autoplay></video>
			</div>

			<button class="btn btn-primary" onclick="startCapture()">
				Start Capture
			</button>
			<p id="instruction" class="mt-2">
				Position 1: Please look straight at the camera
			</p>

			<div id="preview" class="d-flex justify-content-center mt-1">
				<div class="preview-box" id="img1">
					<img src="./images/straight.svg" alt="Straight Image" />
				</div>
				<div class="preview-box" id="img2">
					<img src="./images/left.svg" alt="Left Image" />
				</div>
				<div class="preview-box" id="img3">
					<img src="./images/right.svg" alt="Right Image" />
				</div>
				<div class="preview-box" id="img4">
					<img src="./images/up.svg" alt="Up Image" />
				</div>
			</div>
		</div>

		<script type="module">
			import vision from 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3';
			const { FaceLandmarker, FilesetResolver } = vision;

			let faceLandmarker;
			let video = document.getElementById('camera');
			let currentStep = 1;
			const instructions = [
				'Position 1: Please look straight at the camera',
				'Position 2: Please turn to your left',
				'Position 3: Please turn to your right',
				'Position 4: Please look straight down',
			];

			// Initialize MediaPipe FaceLandmarker
			async function initializeFaceLandmarker() {
				const filesetResolver = await FilesetResolver.forVisionTasks(
					'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
				);
				faceLandmarker = await FaceLandmarker.createFromOptions(
					filesetResolver,
					{
						baseOptions: {
							modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
						},
						runningMode: 'video',
						numFaces: 1,
					}
				);
			}

			async function startVideo() {
				const stream = await navigator.mediaDevices.getUserMedia({
					video: true,
				});
				video.srcObject = stream;
			}

			function updateInstruction() {
				if (currentStep <= instructions.length) {
					document.getElementById('instruction').innerText =
						instructions[currentStep - 1];
				} else {
					document.getElementById('instruction').innerText =
						'All photos captured!';
				}
			}

			function getFaceOrientation(landmarks) {
				const leftEyeIndex = 33,
					rightEyeIndex = 263,
					noseIndex = 1,
					chinIndex = 152;
				const leftEye = landmarks[leftEyeIndex],
					rightEye = landmarks[rightEyeIndex];
				const nose = landmarks[noseIndex],
					chin = landmarks[chinIndex];

				const eyeDistanceY = Math.abs(leftEye.y - rightEye.y);
				const noseXPosition = nose.x;
				const eyeCenterY = (leftEye.y + rightEye.y) / 2;

				if (eyeDistanceY < 0.02 && Math.abs(nose.x - 0.5) < 0.05)
					return 'straight';
				else if (noseXPosition < 0.4) return 'right';
				else if (noseXPosition > 0.6) return 'left';
				else if (chin.y > eyeCenterY - 0.1) return 'up';
				else return 'unknown';
			}

			async function capturePosition(orientation) {
				const canvas = document.createElement('canvas');
				canvas.width = video.videoWidth;
				canvas.height = video.videoHeight;
				const context = canvas.getContext('2d');
				context.drawImage(video, 0, 0, canvas.width, canvas.height);

				const detections = await faceLandmarker.detectForVideo(
					video,
					performance.now()
				);
				console.log({ detections });

				// Check if detections are found
				if (detections && detections.faceLandmarks.length > 0) {
					const landmarks = detections.faceLandmarks[0];
					const detectedOrientation = getFaceOrientation(landmarks);
					console.log({ detectedOrientation, orientation });

					// If the detected orientation matches the required one, capture the image
					if (detectedOrientation === orientation) {
						// Calculate bounding box from landmarks
						const xs = landmarks.map((point) => point.x * canvas.width);
						const ys = landmarks.map((point) => point.y * canvas.height);
						let minX = Math.min(...xs);
						let minY = Math.min(...ys);
						let maxX = Math.max(...xs);
						let maxY = Math.max(...ys);
						let faceWidth = maxX - minX;
						let faceHeight = maxY - minY;

						// Define padding as a percentage of face dimensions
						const padding = 0.4; // 20% padding around the face
						minX -= faceWidth * padding;
						minY -= faceHeight * padding;
						faceWidth += faceWidth * padding * 2;
						faceHeight += faceHeight * padding * 2;

						// Ensure boundaries are within the image
						minX = Math.max(0, minX);
						minY = Math.max(0, minY);
						faceWidth = Math.min(canvas.width - minX, faceWidth);
						faceHeight = Math.min(canvas.height - minY, faceHeight);

						// Create a cropped canvas for the face
						const croppedCanvas = document.createElement('canvas');
						croppedCanvas.width = faceWidth;
						croppedCanvas.height = faceHeight;
						croppedCanvas
							.getContext('2d')
							.drawImage(
								canvas,
								minX,
								minY,
								faceWidth,
								faceHeight,
								0,
								0,
								faceWidth,
								faceHeight
							);
						const croppedImage = croppedCanvas.toDataURL();
						document.getElementById(
							`img${currentStep}`
						).innerHTML = `<img src="${croppedImage}" alt="Captured Image">`;
						currentStep++;
						updateInstruction();
						countdownAndCapture(); // Move to the next position countdown
					} else {
						// Retry if orientation does not match
						setTimeout(() => capturePosition(orientation), 1000);
					}
				} else {
					// Retry if no detections were found
					console.log('No face detected, retrying...');
					setTimeout(() => capturePosition(orientation), 1000);
				}
			}

			function countdownAndCapture() {
				let countdown = 3;
				const countdownInterval = setInterval(() => {
					if (countdown > 0) {
						if (currentStep > 4) {
							clearInterval(countdownInterval);
							document.getElementById('instruction').innerText = ``;
							return;
						}
						document.getElementById(
							'instruction'
						).innerText = `Capturing in ${countdown}...`;
						countdown--;
					} else {
						clearInterval(countdownInterval);
						console.log({ countdownInterval, currentStep });
						const findStep = ['straight', 'left', 'right', 'up'][
							currentStep - 1
						];
						if (findStep) {
							capturePosition(findStep);
						} else {
							currentStep = 1;
							clearInterval(countdownInterval);
							document.getElementById('instruction').innerText = ``;
							return;
						}
					}
				}, 1000);
			}

			function startCapture() {
				currentStep = 1;
				updateInstruction();
				countdownAndCapture();
			}

			// Initialize everything when the page loads
			window.onload = async () => {
				await initializeFaceLandmarker();
				await startVideo();
				console.log('FaceLandmarker initialized and video started');
			};
			window.startCapture = startCapture;
		</script>
	</body>
</html>
