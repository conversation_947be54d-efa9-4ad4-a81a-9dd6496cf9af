import { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import { useDispatch } from 'react-redux';
import { fromJS, Map } from 'immutable';
import LocalStorageService from 'LocalStorageService';
import { decryptData } from '../encryption';

export const UNIFY_SERVICE_API =
  'https://digi-unify.service.digi-val.com/api/v1/institutions/institutionList_encrypt_v2';
export const UNIFY_SERVICE_API_KEY = '8bq1v%j!1Jyl7Gr^qm';

function useUnifyServiceHook() {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [unifyData, setUnifyData] = useState(Map());
  const apiCalledRef = useRef(false);

  useEffect(() => {
    const getUnifyServiceData = () => {
      if (apiCalledRef.current) return;
      apiCalledRef.current = true;

      setLoading((prev) => !prev);
      axios
        .get(`${UNIFY_SERVICE_API}`, {
          params: {
            code: process.env.REACT_APP_CLIENT_NAME,
          },
          headers: {
            digikey: UNIFY_SERVICE_API_KEY,
          },
        })
        .then((res) => {
          if (res?.data?.data) {
            const response = decryptData({
              content: res.data.data,
              handledBy: 'CLIENT',
            });

            const collegeData = response.collegeList?.[0];
            if (collegeData) {
              const splitData = {
                baseUrl: collegeData.baseUrl,
                hebaUrl: collegeData.baseUrl,
                services: collegeData.services,
                collegeLogo: collegeData.collegeLogo,
                sso: collegeData.sso,
                ssoProvider: collegeData.ssoProvider,
              };
              LocalStorageService.setCustomCookie('unifyData', splitData, true);
              setUnifyData(fromJS(splitData));
            }
          }
          setLoading((prev) => !prev);
        })
        .catch((error) => {
          setLoading((prev) => !prev);
        });
    };

    const timeoutId = setTimeout(() => {
      const response = LocalStorageService.getCustomCookie('unifyData', true);
      if (!response) getUnifyServiceData();
      else setUnifyData(fromJS(response));
    }, 1000);

    return () => {
      clearTimeout(timeoutId);
      apiCalledRef.current = false;
    };
  }, [dispatch]);

  const hebaUrl = unifyData.get('hebaUrl', '');
  return { loading, unifyData, hebaUrl };
}

export default useUnifyServiceHook;
