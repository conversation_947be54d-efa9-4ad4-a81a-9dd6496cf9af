const CRYPTO_KEY = 'ThzWs8tss6fB6AywyQ3Jo8na9m71oeB8';
const IV_SIZE = 16;
const ENCRYPT_STORAGE = true;

const baseUrl = getUnifyData('DC-unifyData');
let heba_main_object = [
  {
    name: 'Monitoring',
    techName: 'monitoring',
    iconUrl: './images/monitoring',
    contextObject: 'Monitoring',
    allowed: ['staff', 'admin'],

    chipSuggestions: [
      {
        name: 'Late Started',
        prompt:
          'Analyze late started sessions based on actual start and scheduled start time',
      },
      {
        name: 'Missed Sessions',
        prompt: 'Analyze missed  sessions',
      },
      {
        name: 'Canceled Sessions',
        prompt: 'Analyze cancelled  sessions',
      },
      {
        name: 'Rescheduled Sessions',
        prompt: 'Analyze Rescheduled  sessions',
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },
  {
    name: 'Policy',
    techName: 'policy',
    iconUrl: './images/policy',
    allowed: ['staff', 'admin', 'student'],
    contextObject: 'Policy',
    chipSuggestions: [
      {
        name: 'Deadline for Tuition Fee Payment',
        prompt:
          'from the provided context  provide information on tuition Fee Payment',
      },
      {
        name: 'Operating Hours During Ramadan',
        prompt:
          'from the provided context write responce on Operating Hours During Ramadan',
      },
      {
        name: 'Procedure After Denial Notification',
        prompt:
          'from the provided context write responce on Operating Hours During Ramadan.',
      },
      {
        name: 'Lab and Clinic Dress Code',
        prompt: 'from the provided context write summury on Dress Code',
      },
    ],
    chipSuggestionsStaff: [
      {
        name: 'Staff Leave Regulations',
        prompt:
          "Navigating leave regulations is crucial for planning. Let's discuss these regulations to ensure you understand how to apply for leave and what conditions apply.",
      },
      {
        name: 'Staff Ramadan Working Hours',
        prompt:
          "During Ramadan, working hours for staff will adjust. Let's examine these changes to help you plan your work schedule and responsibilities accordingly.",
      },
      {
        name: 'Staff Dress Code Policy',
        prompt:
          "Maintaining a professional appearance is important. Let's review the dress code policy for staff, highlighting any specific requirements for different departments.",
      },
      {
        name: 'Safety Guidelines for Staff',
        prompt:
          "Safety in the workplace is our top priority. Let's go over the key safety guidelines that you need to follow, especially if you're working in labs or with students in clinical settings.",
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },

  {
    name: 'Grades and Outcomes',
    techName: 'grades_and_outcomes',
    iconUrl: './images/grade',
    contextObject: 'GradesAndOutcomes',
    endpoint:
      'https://api.digiassess.digivalsolutions.com/api/v1/student/report?academicNo=1625887',
    method: 'GET',
    headers: {
      'x-api-key': '4Dy@aWXt',
    },
    chipSuggestions: [
      {
        name: 'Exam Performance',
        prompt:
          "based on the provided object  analyze   exam performance in detail. We'll look at areas where you excelled and where you might need improvement. Based on this analysis, I'll offer some tailored advice on how to approach your studies moving forward.",
      },
      {
        name: 'Learning Outcomes Progress',
        prompt:
          "Considering your recent academic performances, let's assess your progress towards achieving the specified learning outcomes. I'll guide you through understanding which outcomes you're meeting and where there might be gaps.",
      },
      {
        name: 'Areas need Improvement',
        prompt: 'analyze and provide the area of improvments.',
      },
      {
        name: 'Areas of Excellence',
        prompt:
          "You've shown remarkable strength in certain areas. Let's highlight these areas of excellence and discuss how you can leverage these strengths further to support your overall academic growth.",
      },
      {
        name: 'Study Focus Topics',
        prompt:
          "Based on your academic record, there are specific topics you should focus more on. I'll recommend study strategies and resources to help you deepen your understanding in these areas.",
      },
    ],
    allowed: ['student'],
    dataFetched: false,
    fetchedData: null,
  },
  {
    name: 'Grades and Outcomes',
    techName: 'grades_and_outcomes_staff',
    allowed: ['staff', 'admin'],
    iconUrl: './images/grade',
    contextObject: 'GradesAndOutcomes',
    endpoint:
      'https://api.digiassess.digivalsolutions.com/api/v1/student/report?academicNo=1625887',
    method: 'GET',
    headers: {
      'x-api-key': '4Dy@aWXt',
    },
    endpoint2:
      'https://api.digiassess.digivalsolutions.com/api/v1/student/report?academicNo=',

    chipSuggestions: [
      {
        name: 'Overall Exam Performance Insights',
        prompt:
          'Analyze the overall exam performance of students to identify common trends. This analysis will help us understand where students are excelling and which areas require more attention in our teaching strategies.',
      },
      {
        name: 'Learning Outcomes Assessment',
        prompt:
          'Evaluate the collective progress of students in achieving learning outcomes. This evaluation will guide curriculum adjustments and targeted teaching interventions.',
      },
      {
        name: 'Student Excellence and Improvement Areas',
        prompt:
          'Identify patterns of excellence and areas for improvement among students. Based on these patterns, suggest modifications to instructional methods or additional resources to enhance learning.',
      },
      {
        name: 'Focused Study Topics for Improvement',
        prompt:
          'Based on assessment data, pinpoint topics where students commonly struggle. Recommend strategies for faculty to emphasize these topics, potentially through additional lectures, workshops, or revised materials.',
      },
    ],
    staffchipSuggestions: [
      {
        name: 'Overall Exam Performance Insights',
        prompt:
          'Analyze the overall exam performance of students to identify common trends. This analysis will help us understand where students are excelling and which areas require more attention in our teaching strategies. if student name is present add it with the output ',
      },
      {
        name: 'Learning Outcomes Assessment',
        prompt:
          'Evaluate the collective progress of students in achieving learning outcomes. This evaluation will guide curriculum adjustments and targeted teaching interventions.',
      },
      {
        name: 'Student Excellence and Improvement Areas',
        prompt:
          'Identify patterns of excellence and areas for improvement among students. Based on these patterns, suggest modifications to instructional methods or additional resources to enhance learning.',
      },
      {
        name: 'Focused Study Topics for Improvement',
        prompt:
          'Based on assessment data, pinpoint topics where students commonly struggle. Recommend strategies for faculty to emphasize these topics, potentially through additional lectures, workshops, or revised materials.',
      },
    ],

    dataFetched: false,
    fetchedData: null,
  },
  {
    name: 'Topics and Quizes',
    techName: 'learning_resources',
    iconUrl: './images/learning',
    contextObject: 'LearningResources',
    endpoint:
      'https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/hebaAI/studentCourseList?studentId=656036fa4c84ea167b9c3e7e',
    method: 'GET',
    endpoint2:
      'https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/hebaAI/courseSessionList?courseId=6597e8f94d988902186cdefd',
    endpoint3:
      'https://isnc-staging-dsapi-yk25kmkzeq-el.a.run.app/api/v1/hebaAi/getSessionText?courseId=65b0a32bf602763fe67beb98&sessionId=65cb5495d85fdd305bcd5142',
    headers: {
      digicronkey: 'Digicronfromdomain890731',
      _institution_id: '5e5d0f1a15b4d600173d5692',
    },
    chipSuggestions: [
      {
        name: 'Resource Summary for Topic',
        prompt: 'provide summury',
      },
      {
        name: 'Key Learning Outcomes from Resources',
        prompt: 'Key Learning Outcomes from Resources',
      },
      {
        name: 'Generate 3 MCQq',
        prompt:
          'based on the provided object Generate 3 MCQq CASE BASED with reason right answers and wrong answers  ',
      },

      {
        name: "Bloom's Taxonomy - Evaluation Level Case based 5 MCQs",
        prompt:
          'based on the provided object  Blooms Taxonomy - Evaluation Level Case based 5 MCQs',
      },
    ],
    chipSuggestionsStaff: [
      {
        name: 'Session Topic Summary',
        prompt:
          "Prepare a concise summary of the upcoming session's topic. Include key points and essential resources that students should review to maximize their understanding and engagement.",
      },
      {
        name: 'Critical Topic Points',
        prompt:
          'Identify critical points that students must grasp for the upcoming topic. Offering a list of these points can help students focus their studies more effectively.',
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },

  {
    name: 'Absence & Leave',
    techName: 'absence_leave',
    iconUrl: './images/absence',
    contextObject: 'AbsenceAndLeave',
    allowed: ['staff', 'student'],

    chipSuggestions: [
      {
        name: 'Attendance Record',
        prompt: 'based on the data analyze the attendance report',
      },
      {
        name: 'how many leaves i took ',
        prompt: 'how many leaves i took',
      },
    ],
    chipSuggestionsStaff: [
      {
        name: 'Class Average Attendance',
        prompt:
          "Monitoring class attendance can provide insights into student engagement. Let's analyze the average attendance for your classes and discuss strategies to improve attendance if needed.",
      },
      {
        name: 'Denials and Warnings',
        prompt:
          "Issuing denial and warning notifications is a part of managing class attendance. Let's discuss how to effectively communicate these notifications and the follow-up process.",
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },
  {
    name: 'General Chat',
    techName: 'general_chat',
    iconUrl: './images/general-chat',
    contextObject: 'GeneralChat',
    allowed: ['staff', 'admin', 'student', 'q360admin'],
    chipSuggestions: [
      {
        name: 'Nouns and Pronouns',
        prompt:
          "Let's dive into the basics of grammar by distinguishing between nouns and pronouns. Understanding these parts of speech is fundamental to mastering the language.",
      },
      {
        name: 'Data Structures Concept',
        prompt:
          "Data structures are a critical concept in computer science. Let's explore the different types of data structures and how they are used to organize and store data efficiently.",
      },
    ],
    chipSuggestionsStaff: [
      {
        name: 'Explaining Nouns and Pronouns',
        prompt:
          "Teaching the difference between nouns and pronouns can be foundational for students learning a language. Let's discuss effective strategies for teaching these concepts.",
      },
      {
        name: 'Teaching Data Structures',
        prompt:
          "Data structures are a key concept in computer science education. Let's review methods for effectively conveying the importance and application of different data structures to students.",
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },
  {
    name: 'Schedule',
    techName: 'schedule',
    iconUrl: './images/schedule',
    contextObject: 'ClassSchedule',
    allowed: ['staff', 'admin'],
    chipSuggestions: [
      {
        name: "Today's Schedule",
        prompt: 'summurize todays Schedules ',
      },
      {
        name: 'Proctoring Duties - this week ',
        prompt: 'analyze This Week Procoring Duties',
      },
    ],
    chipSuggestionsStaff: [
      {
        name: 'Missed Sessions',
        prompt:
          "Life happens, and sometimes sessions are missed. Let's discuss how you can catch up on any missed content and keep your students on track.",
      },
      {
        name: 'Canceled Sessions',
        prompt:
          "If you need to cancel a session, communication is key. Let's talk about how to notify your students and reschedule the session efficiently.",
      },
      {
        name: 'Rescheduled Sessions',
        prompt:
          "Rescheduling sessions requires planning. Let's ensure that the new time works for everyone and that the transition is smooth for your students.",
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },
  // {
  //   name: 'Quiz',
  //   techName: 'quiz',
  //   iconUrl: './images/quiz',
  //   contextObject: 'Quiz',
  //   chipSuggestionsStaff: [
  //     {
  //       name: 'Application Level MCQs',
  //       prompt:
  //         "Creating application-level MCQs can help students apply theoretical knowledge to practical scenarios. Let's design some questions that challenge them to think critically.",
  //     },
  //     {
  //       name: 'Evaluation Level MCQs',
  //       prompt:
  //         "Evaluation level MCQs test students' ability to judge and make decisions based on their knowledge. Let's compile questions that encourage higher-order thinking.",
  //     },
  //     {
  //       name: 'Recall Level MCQs',
  //       prompt:
  //         "Recall level MCQs are essential for testing foundational knowledge. Let's develop questions that cover the core concepts of your subject.",
  //     },
  //     {
  //       name: 'Analytical Level MCQs',
  //       prompt:
  //         "Analytical level MCQs challenge students to break down complex information. Let's create questions that test their analytical skills.",
  //     },
  //   ],
  //   dataFetched: false,
  //   fetchedData: null,
  // },
  {
    name: 'Annual program report',
    allowed: ['admin'],

    techName: 'plo_analysis',
    iconUrl: './images/plo-analysis',
    contextObject: 'PLOAnalysis',
    chipSuggestions: [
      {
        name: 'Resource Utilization',
        prompt: 'analysing Resource Utilization.',
      },
      {
        name: 'Negatively Impacting Courses',
        prompt: 'analyse and provide  Negatively Impacting CLO and PLOs.',
      },
      {
        name: 'Summary of Achievements',
        prompt: 'Summary of Achievements.',
      },
      {
        name: 'Key Performance Indicators',
        prompt: 'Analyze Key Performance Indicators',
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },
  {
    name: 'Q360',
    techName: 'q360',
    iconUrl: './images/q360',
    contextObject: 'Q360',
    allowed: ['admin', 'q360admin'],
    headers: {
      authorization: 'Bearer ' + getCookie('DC-access_token'),
      _user_id: getUserId(),
    },
    academicYearEndPoint:
      baseUrl + 'v1/qapcCategoryForm_v2/getAllInstitutionCalender',
    formsListEndPoint: baseUrl + 'v1/formInitiator/publishedFormInitiator',
    q360DataEndPoint: baseUrl + 'v1/formInitiator/selectedFormList',
    viewForm: baseUrl + 'v1/formInitiator/formInitiatedAttachments',
    getSignedUrl: baseUrl + 'v1/qapcCategoryForm_v2/qapcSignedUrl?url=',
    chipSuggestions: [
      {
        name: 'Summary of the form',
        prompt: 'provide the summary of the form',
      },
      {
        name: 'Overall analysis of the data',
        prompt: 'provide overall analysis of the data',
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },

  {
    name: 'Survey',
    techName: 'survey',
    allowed: ['staff', 'admin'],

    iconUrl: './images/survey',
    contextObject: 'Survey',
    chipSuggestionsStaff: [
      {
        name: 'Summary',
        prompt:
          "Survey summaries can provide valuable insights into student satisfaction and areas for improvement. Let's review the latest survey results and identify key takeaways.",
      },
      {
        name: 'Students Satisfaction',
        prompt:
          "Student satisfaction is a key indicator of our program's success. Let's delve into the factors contributing to satisfaction and areas where we can enhance the student experience.",
      },
      {
        name: 'Improvement Areas',
        prompt:
          "Identifying areas for improvement from survey feedback is essential for our growth. Let's outline actionable steps based on student feedback to enhance our educational offerings.",
      },
      {
        name: 'Negative sentiments',
        prompt:
          "Addressing negative sentiments expressed in surveys is crucial for maintaining a positive learning environment. Let's discuss strategies for addressing and mitigating these concerns.",
      },
    ],
    dataFetched: false,
    fetchedData: null,
  },
];

function getUnifyData(name, type = 'baseUrl') {
  const cookieValue = getCookie(name);
  if (!cookieValue) return '';

  const unifyData = ENCRYPT_STORAGE
    ? decryptData({ content: cookieValue, handledBy: 'CLIENT' })
    : JSON.parse(decodeURIComponent(cookieValue));
  return unifyData[type] || '';
}

function getUserId() {
  const cookieValue = getCookie('DC-user_Id');
  if (!cookieValue) return '';

  return ENCRYPT_STORAGE
    ? decryptData({ content: cookieValue, handledBy: 'CLIENT', parsed: false })
    : cookieValue;
}

function getCookie(name) {
  // Create a regular expression to find the cookie by name
  const cookieName = name + '=';
  const decodedCookie = decodeURIComponent(document.cookie); // Decode cookie value
  const cookieArray = decodedCookie.split(';'); // Split cookies into an array

  // Loop through all cookies
  for (let i = 0; i < cookieArray.length; i++) {
    let cookie = cookieArray[i].trim(); // Trim spaces
    // If cookie name matches, return its value
    if (cookie.indexOf(cookieName) === 0) {
      return cookie.substring(cookieName.length, cookie.length);
    }
  }
  // Return null if cookie not found
  return null;
}

function decryptData({ content = '', handledBy = 'SERVER', parsed = true }) {
  try {
    // Decode the hex-encoded string into a WordArray
    const combined = CryptoJS.enc.Hex.parse(content);

    // Extract the IV (last IV_SIZE_BYTES bytes of the combined data)
    const iv = CryptoJS.lib.WordArray.create(
      combined.words.slice(-(IV_SIZE / 4)),
      IV_SIZE
    );

    // Extract the ciphertext (everything except the IV)
    const ciphertext = CryptoJS.lib.WordArray.create(
      combined.words.slice(0, -IV_SIZE / 4),
      combined.sigBytes - IV_SIZE
    );

    // Prepare the decryption key
    const keyString = handledBy === 'SERVER' ? CRYPTO_KEY : CRYPTO_KEY;
    const key = CryptoJS.enc.Utf8.parse(keyString);

    // Decrypt the ciphertext
    const decrypted = CryptoJS.AES.decrypt({ ciphertext }, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    // Convert the decrypted data to a UTF-8 string
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    return parsed ? JSON.parse(decryptedText) : decryptedText;
  } catch (error) {
    // console.error('Error decrypting data:', error);
    throw new Error('Failed to decrypt data');
  }
}
