import { useDispatch } from 'react-redux';
import { getDocumentsDownload } from '../ReduxApi/Documents/action';

function useDownload() {
  const dispatch = useDispatch();

  const linkDownload = (LinkDownload) => {
    const filename = LinkDownload.split('/').pop().split('?')[0] || 'document';
    fetch(LinkDownload)
      .then((res) => res.blob())
      .then((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
  };

  const downloadFile = (item) => {
    dispatch(getDocumentsDownload(item, linkDownload));
  };

  return {
    downloadFile,
  };
}

export default useDownload;
