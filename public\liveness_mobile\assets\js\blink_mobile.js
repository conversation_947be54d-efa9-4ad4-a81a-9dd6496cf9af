const webcamElement = document.getElementById('webcam');
const canvasElement = document.getElementById('canvas');
const webcam = new Webcam(webcamElement, 'user', canvasElement, null);
var blinkThreshhold = 0.1;

const urlSearchParams = new URLSearchParams(window.location.search);
const params = Object.fromEntries(urlSearchParams.entries());
if (Object.keys(params).length < 4) {
  alert('params not supplied');
}
platform = params.platform;
let bodyClassName = 'digi-ios-font';
platform == 'android' ? (bodyClassName = 'digi-android-font') : null;
platform == 'ios' ? (blinkThreshhold = 0.07) : null;
$('body').addClass(bodyClassName);

let model;
let cameraFrame = null;
let running = false;
let timeout = null;

console.log('mobile');
let challenges = [];

if (params.debug == 'true') {
  $('.debug').show();
}
$('.post-snap').on('click', function () {
  verifyFacial();
});

$('.iclose2,.cancel-track').on('click', function () {
  sendMesssageToPlatform('close1clicked');
});
// $('.iclose2,.cancel-track').on('touchstart', function () {
//   sendMesssageToPlatform('close1clicked');
// });

$('.iclose').on('click', function () {
  $('.digi-tooltip-container').removeClass('digi-active');
});

$('.info-tooltip').on('click', function () {
  $('.digi-tooltip-container').toggleClass('digi-active');
});

$('.digi-fail-button').on('click', function () {
  //  window.location.reload();

  if (platform == 'android') {
    sendMesssageToPlatform('tryagain');
  } else {
    window.location.href = window.location.href;
  }
});

$('.digi-info-icon').on('click', function (event) {
  event.stopPropagation();
  $('.digi-tooltip-container,.digi-close-icon-container').toggleClass(
    'digi-active'
  );
});

$('.digi-home-icon').on('click', function () {
  //if (this.checked) {

  //  $('#webcam-caption').text('Click to Stop Camera');
  $('.step1').removeClass('digi-active');
  $('.step2').addClass('digi-active');
  console.log('webcam started');

  setTimeout(function () {
    $('.digi-loading-text').text('Please wait for the blink');
    startCaptura();
  }, 3000);
});

async function stopCaptura() {
  if (running) {
    running = false;
    if (cameraFrame != null) {
      console.log('cancel time frame called ');
      cancelAnimationFrame(cameraFrame);
    }
  }
}
async function startCaptura() {
  $('.step2').removeClass('digi-active');
  startChallenge();
  // Load the MediaPipe Facemesh package.

  cameraFrame = detectKeyPoints();
  timeout = setTimeout(() => {
    stopCaptura();
  }, 10000);

  running = true;
}

async function main() {
  await setupFaceLandmarkDetection();
}

async function setupFaceLandmarkDetection() {
  // Setup TF Backend type
  await tf.setBackend('wasm');
}

async function detectaPiscada(keypoints) {
  return true;
}

async function detectKeyPoints() {
  const predictions = await model.estimateFaces({
    input: document.querySelector('video'),
    returnTensors: false,
    flipHorizontal: true,
    predictIrises: true,
    willReadFrequently: true,
  });

  if (predictions.length > 0) {
    const keypoints = predictions[0].scaledMesh;
    detectBlink(keypoints);
  }

  cameraFrame = requestAnimationFrame(detectKeyPoints);
}

function detectBlink(keypoints) {
  if (detectarPiscada(keypoints)) {
    console.log('-----> blinked on', challengesec);
    globalblinks.push(challengesec);
    // $('.digi-camera-container').prepend(challengesec);
  }
}

function detectarPiscada(keypoints) {
  //console.log('detect key point called');
  //console.log('face points', keypoints);

  leftEye_l = 263;
  leftEye_r = 362;
  leftEye_t = 386;
  leftEye_b = 374;

  rightEye_l = 133;
  rightEye_r = 33;
  rightEye_t = 159;
  rightEye_b = 145;

  aL = euclidean_dist(
    keypoints[leftEye_t][0],
    keypoints[leftEye_t][1],
    keypoints[leftEye_b][0],
    keypoints[leftEye_b][1]
  );
  bL = euclidean_dist(
    keypoints[leftEye_l][0],
    keypoints[leftEye_l][1],
    keypoints[leftEye_r][0],
    keypoints[leftEye_r][1]
  );
  earLeft = aL / (2 * bL);

  aR = euclidean_dist(
    keypoints[rightEye_t][0],
    keypoints[rightEye_t][1],
    keypoints[rightEye_b][0],
    keypoints[rightEye_b][1]
  );
  bR = euclidean_dist(
    keypoints[rightEye_l][0],
    keypoints[rightEye_l][1],
    keypoints[rightEye_r][0],
    keypoints[rightEye_r][1]
  );
  earRight = aR / (2 * bR);

  //  console.log('-----> ' + earLeft + '\t' + earRight);
  //if (earLeft < 0.1 || earRight < 0.1) {
  if (earLeft < blinkThreshhold && earRight < blinkThreshhold) {
    return true;
  } else {
    return false;
  }
}

function euclidean_dist(x1, y1, x2, y2) {
  return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
}

//kabeer custom logic starts
challengesec = 0;
globalblinks = [];

let minimumCorrectBlink = 1;
let allowedWrongBling = 4;
let totalblinkstocheck = 3;

//kabeer custom logic ends

function analyzeLivenessResults(timer) {
  clearInterval(timer);
  // let primary_result = _.intersection(checksarray, globalblinks);

  var primary_result = checksarray.filter(function (n) {
    return globalblinks.indexOf(n) !== -1;
  });
  console.log('compare result', primary_result);
  let uniqeblinks = removeDuplicates(globalblinks);
  let falsetries = difference(uniqeblinks, primary_result);
  console.log('false tried', falsetries);
  if (falsetries.length > allowedWrongBling) {
    handleLivenessFailiure();
    console.log('too many wrong blinks and failed');
    return false;
  }

  if (primary_result.length >= minimumCorrectBlink) {
    handleLivenessSuccess();
    console.log('liveness verification sucess');
  } else {
    handleLivenessFailiure();
    console.log('liveness verification failed');
  }
}

function sendMesssageToPlatform(message) {
  if (params.debug == 'true') {
    alert(platform + message);
    console.log(platform, 'message sent', message);
  }
  //platform = params.platform;
  if (platform == 'ios')
    window.webkit.messageHandlers.Mobile.postMessage(message);
  if (platform == 'android') {
    //commented for not sending socket to server
    // let messageObj = { event: message };
    // let sendnotificationUrl =
    //   params.cron +
    //   'buzzstart?eventId=' +
    //   params.id +
    //   '&data=' +
    //   JSON.stringify(messageObj);
    // console.log('android post url', sendnotificationUrl);

    // $.get(sendnotificationUrl, function (data, status) {
    //   console.log('notification sent to android', data);
    // });

    //
    //https://ecs-dscron-staging.digi-val.com
    Mobile.showMessageFromWeb(message);
  }
  if (platform == 'web') window.parent.postMessage(message, '*');
}

function handleLivenessFailiure() {
  $('.steps').removeClass('digi-active');
  $('.step4').addClass('digi-active');
  console.log('handle failed sent');
  sendMesssageToPlatform('facefailed');
}

function handleAuthenticationSuccess() {
  $('.digi-camera-page').removeClass('digi-active');
  $('.digi-verification-success-page').addClass('digi-active');
  console.log('handle succcses sent');
  setTimeout(function () {
    sendMesssageToPlatform('facesuccess');
  }, 2000);

  // $('.digi-blink-fail-panel').hide();
}

function handleAuthenticationFailiure() {
  $('.steps').removeClass('digi-active');
  $('.step5').addClass('digi-active');
}

function handleLivenessSuccess() {
  verifyFacial();
}

function dString(name) {
  if (name !== '') {
    return window.atob(name);
  }
  return '';
}

function dataURLtoFile(dataurl, filename) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = dString(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

function verifyFacial() {
  let appname = params.appname;
  let type = params.type;
  let employeeOrAcademicId = params.employeeOrAcademicId;

  //picture = webcam.snap();
  //$('.test-img').attr('src', picture);
  let picture = $('.digi-profile-picture').attr('src');

  const binaryValue = dataURLtoFile(picture, 'Face.png');
  console.log('snapped', binaryValue);
  let formData = new FormData();
  formData.append('app', appname);
  formData.append('type', type);
  formData.append('employeeOrAcademicId', employeeOrAcademicId);
  formData.append('facial', binaryValue);

  const URL = params.faceurl;
  console.log('verify facial requested');
  $.ajax({
    type: 'POST',
    url: URL,
    data: formData, //$('#myForm').serializeArray(),
    cache: false,
    processData: false,
    contentType: false,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    success: function (data) {
      console.log('face request success');
      if (data.success == true) {
        console.log('verify facial success');
        handleAuthenticationSuccess();
      } else {
        console.log('verify facial failiure');
        handleAuthenticationFailiure();
      }
    },
    error: function () {
      handleAuthenticationFailiure();
      console.log('facial request failed');
    },
  });
}

function generateRandomNumberArray() {
  let previousNumber;
  const numbers = new Set();
  while (numbers.size < 3) {
    let randomNumber;
    do {
      randomNumber = Math.floor(Math.random() * 7) + 1;
    } while (
      Math.abs(randomNumber - previousNumber) < 2 ||
      numbers.has(randomNumber) ||
      numbers.has(randomNumber - 1) ||
      numbers.has(randomNumber + 1)
    );
    previousNumber = randomNumber;
    numbers.add(randomNumber);
  }
  return Array.from(numbers).sort((a, b) => a - b);
}

function startChallenge() {
  //checksarray = generateRandomNumberArray();
  checksarray = [];
  let random_number1 = Math.floor(Math.random() * (4 - 1 + 1)) + 1;
  let random_number2 = Math.floor(Math.random() * (7 - 4 + 1)) + 4;
  checksarray[0] = random_number1;
  checksarray[1] = random_number1 + 1;
  checksarray[2] = random_number2;
  checksarray[3] = random_number2 + 1;

  console.log('checksarray chanegd', checksarray);

  //$('.digi-camera-container').prepend('---', checksarray.toString());
  console.log(checksarray);

  timer = setInterval(() => {
    console.log('timer ruuning');
    $('.sec').text(challengesec);

    challengesec++;

    if (checksarray.indexOf(challengesec) == -1) {
      $('.steps').removeClass('digi-active');
      $('.step2 ').addClass('digi-active');
    } else {
      $('.step2 ').removeClass('digi-active');
      $('.step3').addClass('digi-active');
    }

    //take unaware snap to check authentication

    if (challengesec == 8) {
      stopCaptura();
      let picture = webcam.snap();
      $('.digi-profile-picture').attr('src', picture);
    }

    if (challengesec == 10) {
      analyzeLivenessResults(timer);

      checksarray = [];
      globalblinks = [];
      challengesec = 0;

      console.log('timer closed');
    }
  }, 1000);
}
/*utility function */

function removeDuplicates(arr) {
  return arr.filter((item, index) => arr.indexOf(item) === index);
}

function difference(arr1, arr2) {
  return arr1.filter((x) => arr2.indexOf(x) === -1);
}

/*utility ends*/

$(document).ready(function () {
  webcam.start();
  faceLandmarksDetection
    .load(faceLandmarksDetection.SupportedPackages.mediapipeFacemesh, {
      maxFaces: 1,
    })
    .then((mdl) => {
      model = mdl;
      console.log('initial load done', model);
      const test = model.estimateFaces({
        input: document.querySelector('video'),
        returnTensors: false,
        flipHorizontal: true,
        predictIrises: true,
        willReadFrequently: true,
      });
      $('.init-hide').removeClass('init-hide');
      $('.loader').removeClass('loader');
    });

  main();
});
