import { List, Map } from 'immutable';
import { useSelector } from 'react-redux';
import { selectAuthData } from 'ReduxApi/User/selectors';

const usePermissionHook = () => {
  const getAuthData = useSelector(selectAuthData);

  const roles = useSelector((state) =>
    state?.user.getIn(['authData', 'roles'], List())
  );
  const selectedRole = useSelector((state) =>
    state?.user?.get('selectedRole', Map())
  );

  if (getAuthData.get('user_type', '') === 'staff' && selectedRole.size) {
    const modules = roles
      .filter((item) => item.get('_id') === selectedRole.get('_id', ''))
      .reduce((_, el) => el.get('modules', List()));

    const getModuleAction = (list, type) => {
      return list.filter((item) => {
        return item.get('name', '') === type;
      });
    };

    const checkPermissionAction = (
      module = '',
      page = '',
      tabs = '',
      action = ''
    ) => {
      const checkModules = getModuleAction(
        modules.get('modules', List()),
        module
      );

      const checkPages = getModuleAction(
        checkModules.getIn([0, 'pages'], List()),
        page
      );

      const checkTabs = getModuleAction(
        checkPages.getIn([0, 'tabs'], List()),
        tabs
      );

      const checkActions = getModuleAction(
        checkTabs.getIn([0, 'actions'], List()),
        action
      );

      return (
        checkModules.size !== 0 &&
        checkPages.size !== 0 &&
        checkTabs.size !== 0 &&
        checkActions.size !== 0
      );
    };

    const checkPermissionActionPhase1 = (
      module = '',
      page = '',
      action = ''
    ) => {
      const checkModules = getModuleAction(
        modules.get('modules', List()),
        module
      );

      const checkPages = getModuleAction(
        checkModules.getIn([0, 'pages'], List()),
        page
      );

      const checkActions = getModuleAction(
        checkPages.getIn([0, 'actions'], List()),
        action
      );

      return (
        checkModules.size !== 0 &&
        checkPages.size !== 0 &&
        checkActions.size !== 0
      );
    };

    const checkPermissionSubTabAction = (
      module = '',
      page = '',
      tabs = '',
      subTabs = '',
      action = ''
    ) => {
      const checkModules = getModuleAction(
        modules.get('modules', List()),
        module
      );

      const checkPages = getModuleAction(
        checkModules.getIn([0, 'pages'], List()),
        page
      );

      const checkTabs = getModuleAction(
        checkPages.getIn([0, 'tabs'], List()),
        tabs
      );
      const checkSubTabs = getModuleAction(
        checkTabs.getIn([0, 'subTabs'], List()),
        subTabs
      );
      const checkActions = getModuleAction(
        checkSubTabs.getIn([0, 'actions'], List()),
        action
      );

      return (
        checkModules.size !== 0 &&
        checkPages.size !== 0 &&
        checkTabs.size !== 0 &&
        checkSubTabs.size !== 0 &&
        checkActions.size !== 0
      );
    };

    const checkPermissionPage = (module = '', page = '', action = '') => {
      return modules.getIn([module, 'pages', page, action], false);
    };

    return [
      checkPermissionAction,
      checkPermissionActionPhase1,
      checkPermissionSubTabAction,
      checkPermissionPage,
    ];
  } else {
    const checkPermissionAction = () => false;
    const checkPermissionActionPhase1 = () => false;
    const checkPermissionSubTabAction = () => {};
    return [
      checkPermissionAction,
      checkPermissionActionPhase1,
      checkPermissionSubTabAction,
    ];
  }
};

export default usePermissionHook;
