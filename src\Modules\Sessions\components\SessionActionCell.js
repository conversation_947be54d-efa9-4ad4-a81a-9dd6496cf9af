import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { List } from 'immutable';
import { getClassName } from '../../Utils/utils';
import { CapitalizeAll, checkEnableSession } from '../../../ReduxApi/util';
import mobile from '../../../Assets/session_mobile.svg';

const SessionActionCell = ({
  schedule,
  authData,
  userType,
  loggedStudentData,
  onSessionClick,
}) => {
  const { t } = useTranslation();

  if (!schedule.get('isActive', '')) return null;

  const status = schedule.get('status', '');
  if (status === 'completed') {
    return (
      <CompletedSessionAction
        schedule={schedule}
        authData={authData}
        userType={userType}
      />
    );
  }

  if (status === 'pending') {
    return (
      <PendingSessionAction
        schedule={schedule}
        userType={userType}
        onSessionClick={onSessionClick}
      />
    );
  }

  if (status === 'ongoing') {
    return (
      <OngoingSessionAction
        schedule={schedule}
        loggedStudentData={loggedStudentData}
        onSessionClick={onSessionClick}
      />
    );
  }

  if (status === 'missed') {
    return (
      <div className="f-16 text-warning text-end bold icon">{t('missed')}</div>
    );
  }

  if (loggedStudentData.get('status', '') === 'exclude') {
    return (
      <div className="f-16 session_attendance_excluded text-end icon">
        {t('exclude')}
      </div>
    );
  }

  return null;
};

const CompletedSessionAction = ({ schedule, authData, userType }) => {
  const { t } = useTranslation();

  const loggedInUserType = userType === 'staff' ? 'staffs' : 'students';
  const loggedInUserId = userType === 'staff' ? '_staff_id' : '_id';

  const userData =
    schedule
      .get(loggedInUserType, List())
      .find((item) => item.get(loggedInUserId) === authData.get('_id', '')) ||
    schedule
      .get(loggedInUserType, List())
      .filter((item) => item.get(loggedInUserId) === authData.get('_id', ''));
  if (!userData) return null;

  return (
    <div className="d-flex">
      <div>
        {userType === 'staff' &&
          schedule.getIn(['feedBack', 'avgRating'], '') !== '' && (
            <span className={getClassName('pl-2', 'pr-2')}>
              <i
                className={getClassName(
                  'pl-2',
                  'pr-2',
                  'fa fa-star text-warning'
                )}
                aria-hidden="true"
              ></i>
              {schedule.getIn(['feedBack', 'avgRating'], '')}
            </span>
          )}
        {userData.get('status') === 'present' ? (
          <span className="text-success">{t('present')}</span>
        ) : (
          <span
            className={
              ['absent', 'leave'].includes(userData.get('status'))
                ? 'text-red'
                : 'text-green'
            }
          >
            {CapitalizeAll(userData.get('status', '').replace('_', ' '))}
            {userData.get('isRestricted', false) &&
              userData.get('status') === 'absent' && (
                <span className="f-14 ml-1">(Course Restricted)</span>
              )}
          </span>
        )}
      </div>
    </div>
  );
};

const PendingSessionAction = ({ schedule, userType, onSessionClick }) => {
  const { t } = useTranslation();
  const mode = schedule.get('mode', '');

  if (mode === 'onsite') {
    return (
      <div className="f-14">
        <img src={mobile} alt="Digiclass" className="digi-add-icon" />
      </div>
    );
  }

  const canStart = checkEnableSession(schedule);
  const className = `f-16 text-end bold icon ${
    canStart ? 'text-skyblue' : 'text-grey'
  }`;
  const action = userType === 'staff' ? 'START' : t('join');

  return (
    <div
      className={getClassName('pr-3-arabic', 'pr-3', className)}
      onClick={canStart ? () => onSessionClick(schedule) : undefined}
    >
      {action}
    </div>
  );
};

const OngoingSessionAction = ({
  schedule,
  loggedStudentData,
  onSessionClick,
}) => {
  const { t } = useTranslation();
  const mode = schedule.get('mode', '');

  if (mode === 'onsite') {
    return (
      <div className="f-14">
        <img src={mobile} alt="Digiclass" className="digi-add-icon" />
      </div>
    );
  }

  const status = loggedStudentData.get('status', 'present');
  const action =
    status === 'present' ? t('end') : CapitalizeAll(status.replace('_', ' '));

  return (
    <div
      className={getClassName(
        'pr-3-arabic',
        'pr-3',
        'f-16 text-skyblue text-end bold icon'
      )}
      onClick={() => onSessionClick(schedule)}
    >
      {action}
    </div>
  );
};

SessionActionCell.propTypes = {
  schedule: PropTypes.object.isRequired,
  authData: PropTypes.object.isRequired,
  userType: PropTypes.string.isRequired,
  loggedStudentData: PropTypes.object.isRequired,
  onSessionClick: PropTypes.func.isRequired,
};

CompletedSessionAction.propTypes = {
  schedule: PropTypes.object.isRequired,
  authData: PropTypes.object.isRequired,
  userType: PropTypes.string.isRequired,
};

PendingSessionAction.propTypes = {
  schedule: PropTypes.object.isRequired,
  userType: PropTypes.string.isRequired,
  onSessionClick: PropTypes.func.isRequired,
};

OngoingSessionAction.propTypes = {
  schedule: PropTypes.object.isRequired,
  loggedStudentData: PropTypes.object.isRequired,
  onSessionClick: PropTypes.func.isRequired,
};

export default SessionActionCell;
